-- ----------------------------
-- Apache Doris版本 - 若依系统数据库脚本
-- 转换为Unique Key模型，适用于Apache Doris
-- 原始文件：ry_20240529.sql
-- 转换日期：2025-07-21
-- ----------------------------

-- ----------------------------
-- 1、部门表
-- ----------------------------
DROP TABLE IF EXISTS sys_dept;
CREATE TABLE sys_dept (
  dept_id           BIGINT          NOT NULL                       COMMENT '部门id',
  parent_id         BIGINT          DEFAULT 0                      COMMENT '父部门id',
  ancestors         VARCHAR(50)     DEFAULT ''                     COMMENT '祖级列表',
  dept_name         VARCHAR(30)     DEFAULT ''                     COMMENT '部门名称',
  order_num         INT             DEFAULT 0                      COMMENT '显示顺序',
  leader            VARCHA<PERSON>(20)     DEFAULT NULL                   COMMENT '负责人',
  phone             VARCHAR(11)     DEFAULT NULL                   COMMENT '联系电话',
  email             VARCHAR(50)     DEFAULT NULL                   COMMENT '邮箱',
  status            CHAR(1)         DEFAULT '0'                    COMMENT '部门状态（0正常 1停用）',
  del_flag          CHAR(1)         DEFAULT '0'                    COMMENT '删除标志（0代表存在 2代表删除）',
  create_by         VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time       DATETIME                                       COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time       DATETIME                                       COMMENT '更新时间'
) UNIQUE KEY(dept_id)
DISTRIBUTED BY HASH(dept_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '部门表';

-- ----------------------------
-- 2、用户信息表
-- ----------------------------
DROP TABLE IF EXISTS sys_user;
CREATE TABLE sys_user (
  user_id           BIGINT          NOT NULL                       COMMENT '用户ID',
  dept_id           BIGINT          DEFAULT NULL                   COMMENT '部门ID',
  user_name         VARCHAR(30)     NOT NULL                       COMMENT '用户账号',
  nick_name         VARCHAR(30)     NOT NULL                       COMMENT '用户昵称',
  user_type         VARCHAR(2)      DEFAULT '00'                   COMMENT '用户类型（00系统用户）',
  email             VARCHAR(50)     DEFAULT ''                     COMMENT '用户邮箱',
  phonenumber       VARCHAR(11)     DEFAULT ''                     COMMENT '手机号码',
  sex               CHAR(1)         DEFAULT '0'                    COMMENT '用户性别（0男 1女 2未知）',
  avatar            VARCHAR(100)    DEFAULT ''                     COMMENT '头像地址',
  password          VARCHAR(100)    DEFAULT ''                     COMMENT '密码',
  status            CHAR(1)         DEFAULT '0'                    COMMENT '帐号状态（0正常 1停用）',
  del_flag          CHAR(1)         DEFAULT '0'                    COMMENT '删除标志（0代表存在 2代表删除）',
  login_ip          VARCHAR(128)    DEFAULT ''                     COMMENT '最后登录IP',
  login_date        DATETIME                                       COMMENT '最后登录时间',
  create_by         VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time       DATETIME                                       COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time       DATETIME                                       COMMENT '更新时间',
  remark            VARCHAR(500)    DEFAULT NULL                   COMMENT '备注'
) UNIQUE KEY(user_id)
DISTRIBUTED BY HASH(user_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '用户信息表';

-- ----------------------------
-- 3、岗位信息表
-- ----------------------------
DROP TABLE IF EXISTS sys_post;
CREATE TABLE sys_post (
  post_id           BIGINT          NOT NULL                       COMMENT '岗位ID',
  post_code         VARCHAR(64)     NOT NULL                       COMMENT '岗位编码',
  post_name         VARCHAR(50)     NOT NULL                       COMMENT '岗位名称',
  post_sort         INT             NOT NULL                       COMMENT '显示顺序',
  status            CHAR(1)         NOT NULL                       COMMENT '状态（0正常 1停用）',
  create_by         VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time       DATETIME                                       COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time       DATETIME                                       COMMENT '更新时间',
  remark            VARCHAR(500)    DEFAULT NULL                   COMMENT '备注'
) UNIQUE KEY(post_id)
DISTRIBUTED BY HASH(post_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '岗位信息表';

-- ----------------------------
-- 4、角色信息表
-- ----------------------------
DROP TABLE IF EXISTS sys_role;
CREATE TABLE sys_role (
  role_id              BIGINT          NOT NULL                    COMMENT '角色ID',
  role_name            VARCHAR(30)     NOT NULL                    COMMENT '角色名称',
  role_key             VARCHAR(100)    NOT NULL                    COMMENT '角色权限字符串',
  role_sort            INT             NOT NULL                    COMMENT '显示顺序',
  data_scope           CHAR(1)         DEFAULT '1'                 COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  menu_check_strictly  TINYINT         DEFAULT 1                   COMMENT '菜单树选择项是否关联显示',
  dept_check_strictly  TINYINT         DEFAULT 1                   COMMENT '部门树选择项是否关联显示',
  status               CHAR(1)         NOT NULL                    COMMENT '角色状态（0正常 1停用）',
  del_flag             CHAR(1)         DEFAULT '0'                 COMMENT '删除标志（0代表存在 2代表删除）',
  create_by            VARCHAR(64)     DEFAULT ''                  COMMENT '创建者',
  create_time          DATETIME                                    COMMENT '创建时间',
  update_by            VARCHAR(64)     DEFAULT ''                  COMMENT '更新者',
  update_time          DATETIME                                    COMMENT '更新时间',
  remark               VARCHAR(500)    DEFAULT NULL                COMMENT '备注'
) UNIQUE KEY(role_id)
DISTRIBUTED BY HASH(role_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '角色信息表';

-- ----------------------------
-- 5、菜单权限表
-- ----------------------------
DROP TABLE IF EXISTS sys_menu;
CREATE TABLE sys_menu (
  menu_id           BIGINT          NOT NULL                       COMMENT '菜单ID',
  menu_name         VARCHAR(50)     NOT NULL                       COMMENT '菜单名称',
  parent_id         BIGINT          DEFAULT 0                      COMMENT '父菜单ID',
  order_num         INT             DEFAULT 0                      COMMENT '显示顺序',
  path              VARCHAR(200)    DEFAULT ''                     COMMENT '路由地址',
  component         VARCHAR(255)    DEFAULT NULL                   COMMENT '组件路径',
  query             VARCHAR(255)    DEFAULT NULL                   COMMENT '路由参数',
  is_frame          INT             DEFAULT 1                      COMMENT '是否为外链（0是 1否）',
  is_cache          INT             DEFAULT 0                      COMMENT '是否缓存（0缓存 1不缓存）',
  menu_type         CHAR(1)         DEFAULT ''                     COMMENT '菜单类型（M目录 C菜单 F按钮）',
  visible           CHAR(1)         DEFAULT '0'                    COMMENT '菜单状态（0显示 1隐藏）',
  status            CHAR(1)         DEFAULT '0'                    COMMENT '菜单状态（0正常 1停用）',
  perms             VARCHAR(100)    DEFAULT NULL                   COMMENT '权限标识',
  icon              VARCHAR(100)    DEFAULT '#'                    COMMENT '菜单图标',
  create_by         VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time       DATETIME                                       COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time       DATETIME                                       COMMENT '更新时间',
  remark            VARCHAR(500)    DEFAULT ''                     COMMENT '备注'
) UNIQUE KEY(menu_id)
DISTRIBUTED BY HASH(menu_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '菜单权限表';

-- ----------------------------
-- 6、用户和角色关联表  用户N-1角色
-- ----------------------------
DROP TABLE IF EXISTS sys_user_role;
CREATE TABLE sys_user_role (
  user_id   BIGINT NOT NULL COMMENT '用户ID',
  role_id   BIGINT NOT NULL COMMENT '角色ID'
) UNIQUE KEY(user_id, role_id)
DISTRIBUTED BY HASH(user_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '用户和角色关联表';

-- ----------------------------
-- 7、角色和菜单关联表  角色1-N菜单
-- ----------------------------
DROP TABLE IF EXISTS sys_role_menu;
CREATE TABLE sys_role_menu (
  role_id   BIGINT NOT NULL COMMENT '角色ID',
  menu_id   BIGINT NOT NULL COMMENT '菜单ID'
) UNIQUE KEY(role_id, menu_id)
DISTRIBUTED BY HASH(role_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '角色和菜单关联表';

-- ----------------------------
-- 8、角色和部门关联表  角色1-N部门
-- ----------------------------
DROP TABLE IF EXISTS sys_role_dept;
CREATE TABLE sys_role_dept (
  role_id   BIGINT NOT NULL COMMENT '角色ID',
  dept_id   BIGINT NOT NULL COMMENT '部门ID'
) UNIQUE KEY(role_id, dept_id)
DISTRIBUTED BY HASH(role_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '角色和部门关联表';

-- ----------------------------
-- 9、用户与岗位关联表  用户1-N岗位
-- ----------------------------
DROP TABLE IF EXISTS sys_user_post;
CREATE TABLE sys_user_post (
  user_id   BIGINT NOT NULL COMMENT '用户ID',
  post_id   BIGINT NOT NULL COMMENT '岗位ID'
) UNIQUE KEY(user_id, post_id)
DISTRIBUTED BY HASH(user_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '用户与岗位关联表';

-- ----------------------------
-- 10、操作日志记录
-- ----------------------------
DROP TABLE IF EXISTS sys_oper_log;
CREATE TABLE sys_oper_log (
  oper_id           BIGINT          NOT NULL                       COMMENT '日志主键',
  title             VARCHAR(50)     DEFAULT ''                     COMMENT '模块标题',
  business_type     INT             DEFAULT 0                      COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  method            VARCHAR(200)    DEFAULT ''                     COMMENT '方法名称',
  request_method    VARCHAR(10)     DEFAULT ''                     COMMENT '请求方式',
  operator_type     INT             DEFAULT 0                      COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  oper_name         VARCHAR(50)     DEFAULT ''                     COMMENT '操作人员',
  dept_name         VARCHAR(50)     DEFAULT ''                     COMMENT '部门名称',
  oper_url          VARCHAR(255)    DEFAULT ''                     COMMENT '请求URL',
  oper_ip           VARCHAR(128)    DEFAULT ''                     COMMENT '主机地址',
  oper_location     VARCHAR(255)    DEFAULT ''                     COMMENT '操作地点',
  oper_param        VARCHAR(2000)   DEFAULT ''                     COMMENT '请求参数',
  json_result       VARCHAR(2000)   DEFAULT ''                     COMMENT '返回参数',
  status            INT             DEFAULT 0                      COMMENT '操作状态（0正常 1异常）',
  error_msg         VARCHAR(2000)   DEFAULT ''                     COMMENT '错误消息',
  oper_time         DATETIME                                       COMMENT '操作时间',
  cost_time         BIGINT          DEFAULT 0                      COMMENT '消耗时间'
) UNIQUE KEY(oper_id)
DISTRIBUTED BY HASH(oper_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '操作日志记录';

-- ----------------------------
-- 11、字典类型表
-- ----------------------------
DROP TABLE IF EXISTS sys_dict_type;
CREATE TABLE sys_dict_type (
  dict_id          BIGINT          NOT NULL                       COMMENT '字典主键',
  dict_name        VARCHAR(100)    DEFAULT ''                     COMMENT '字典名称',
  dict_type        VARCHAR(100)    DEFAULT ''                     COMMENT '字典类型',
  status           CHAR(1)         DEFAULT '0'                    COMMENT '状态（0正常 1停用）',
  create_by        VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time      DATETIME                                       COMMENT '创建时间',
  update_by        VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time      DATETIME                                       COMMENT '更新时间',
  remark           VARCHAR(500)    DEFAULT NULL                   COMMENT '备注'
) UNIQUE KEY(dict_id)
DISTRIBUTED BY HASH(dict_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '字典类型表';

-- ----------------------------
-- 12、字典数据表
-- ----------------------------
DROP TABLE IF EXISTS sys_dict_data;
CREATE TABLE sys_dict_data (
  dict_code        BIGINT          NOT NULL                       COMMENT '字典编码',
  dict_sort        INT             DEFAULT 0                      COMMENT '字典排序',
  dict_label       VARCHAR(100)    DEFAULT ''                     COMMENT '字典标签',
  dict_value       VARCHAR(100)    DEFAULT ''                     COMMENT '字典键值',
  dict_type        VARCHAR(100)    DEFAULT ''                     COMMENT '字典类型',
  css_class        VARCHAR(100)    DEFAULT NULL                   COMMENT '样式属性（其他样式扩展）',
  list_class       VARCHAR(100)    DEFAULT NULL                   COMMENT '表格回显样式',
  is_default       CHAR(1)         DEFAULT 'N'                    COMMENT '是否默认（Y是 N否）',
  status           CHAR(1)         DEFAULT '0'                    COMMENT '状态（0正常 1停用）',
  create_by        VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time      DATETIME                                       COMMENT '创建时间',
  update_by        VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time      DATETIME                                       COMMENT '更新时间',
  remark           VARCHAR(500)    DEFAULT NULL                   COMMENT '备注'
) UNIQUE KEY(dict_code)
DISTRIBUTED BY HASH(dict_code) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '字典数据表';

-- ----------------------------
-- 13、参数配置表
-- ----------------------------
DROP TABLE IF EXISTS sys_config;
CREATE TABLE sys_config (
  config_id         INT             NOT NULL                       COMMENT '参数主键',
  config_name       VARCHAR(100)    DEFAULT ''                     COMMENT '参数名称',
  config_key        VARCHAR(100)    DEFAULT ''                     COMMENT '参数键名',
  config_value      VARCHAR(500)    DEFAULT ''                     COMMENT '参数键值',
  config_type       CHAR(1)         DEFAULT 'N'                    COMMENT '系统内置（Y是 N否）',
  create_by         VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time       DATETIME                                       COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time       DATETIME                                       COMMENT '更新时间',
  remark            VARCHAR(500)    DEFAULT NULL                   COMMENT '备注'
) UNIQUE KEY(config_id)
DISTRIBUTED BY HASH(config_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '参数配置表';

-- ----------------------------
-- 14、系统访问记录
-- ----------------------------
DROP TABLE IF EXISTS sys_logininfor;
CREATE TABLE sys_logininfor (
  info_id        BIGINT          NOT NULL                        COMMENT '访问ID',
  user_name      VARCHAR(50)     DEFAULT ''                      COMMENT '用户账号',
  ipaddr         VARCHAR(128)    DEFAULT ''                      COMMENT '登录IP地址',
  login_location VARCHAR(255)    DEFAULT ''                      COMMENT '登录地点',
  browser        VARCHAR(50)     DEFAULT ''                      COMMENT '浏览器类型',
  os             VARCHAR(50)     DEFAULT ''                      COMMENT '操作系统',
  status         CHAR(1)         DEFAULT '0'                     COMMENT '登录状态（0成功 1失败）',
  msg            VARCHAR(255)    DEFAULT ''                      COMMENT '提示消息',
  login_time     DATETIME                                        COMMENT '访问时间'
) UNIQUE KEY(info_id)
DISTRIBUTED BY HASH(info_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '系统访问记录';

-- ----------------------------
-- 15、定时任务调度表
-- ----------------------------
DROP TABLE IF EXISTS sys_job;
CREATE TABLE sys_job (
  job_id              BIGINT          NOT NULL                    COMMENT '任务ID',
  job_name            VARCHAR(64)     DEFAULT ''                  COMMENT '任务名称',
  job_group           VARCHAR(64)     DEFAULT 'DEFAULT'           COMMENT '任务组名',
  invoke_target       VARCHAR(500)    NOT NULL                    COMMENT '调用目标字符串',
  cron_expression     VARCHAR(255)    DEFAULT ''                  COMMENT 'cron执行表达式',
  misfire_policy      VARCHAR(20)     DEFAULT '3'                 COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  concurrent          CHAR(1)         DEFAULT '1'                 COMMENT '是否并发执行（0允许 1禁止）',
  status              CHAR(1)         DEFAULT '0'                 COMMENT '状态（0正常 1暂停）',
  create_by           VARCHAR(64)     DEFAULT ''                  COMMENT '创建者',
  create_time         DATETIME                                    COMMENT '创建时间',
  update_by           VARCHAR(64)     DEFAULT ''                  COMMENT '更新者',
  update_time         DATETIME                                    COMMENT '更新时间',
  remark              VARCHAR(500)    DEFAULT ''                  COMMENT '备注信息'
) UNIQUE KEY(job_id, job_name, job_group)
DISTRIBUTED BY HASH(job_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '定时任务调度表';

-- ----------------------------
-- 16、定时任务调度日志表
-- ----------------------------
DROP TABLE IF EXISTS sys_job_log;
CREATE TABLE sys_job_log (
  job_log_id          BIGINT          NOT NULL                    COMMENT '任务日志ID',
  job_name            VARCHAR(64)     NOT NULL                    COMMENT '任务名称',
  job_group           VARCHAR(64)     NOT NULL                    COMMENT '任务组名',
  invoke_target       VARCHAR(500)    NOT NULL                    COMMENT '调用目标字符串',
  job_message         VARCHAR(500)                                COMMENT '日志信息',
  status              CHAR(1)         DEFAULT '0'                 COMMENT '执行状态（0正常 1失败）',
  exception_info      VARCHAR(2000)   DEFAULT ''                  COMMENT '异常信息',
  create_time         DATETIME                                    COMMENT '创建时间'
) UNIQUE KEY(job_log_id)
DISTRIBUTED BY HASH(job_log_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '定时任务调度日志表';

-- ----------------------------
-- 17、通知公告表
-- ----------------------------
DROP TABLE IF EXISTS sys_notice;
CREATE TABLE sys_notice (
  notice_id         INT             NOT NULL                       COMMENT '公告ID',
  notice_title      VARCHAR(50)     NOT NULL                       COMMENT '公告标题',
  notice_type       CHAR(1)         NOT NULL                       COMMENT '公告类型（1通知 2公告）',
  notice_content    TEXT            DEFAULT NULL                   COMMENT '公告内容',
  status            CHAR(1)         DEFAULT '0'                    COMMENT '公告状态（0正常 1关闭）',
  create_by         VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time       DATETIME                                       COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time       DATETIME                                       COMMENT '更新时间',
  remark            VARCHAR(255)    DEFAULT NULL                   COMMENT '备注'
) UNIQUE KEY(notice_id)
DISTRIBUTED BY HASH(notice_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '通知公告表';

-- ----------------------------
-- 18、代码生成业务表
-- ----------------------------
DROP TABLE IF EXISTS gen_table;
CREATE TABLE gen_table (
  table_id          BIGINT          NOT NULL                       COMMENT '编号',
  table_name        VARCHAR(200)    DEFAULT ''                     COMMENT '表名称',
  table_comment     VARCHAR(500)    DEFAULT ''                     COMMENT '表描述',
  sub_table_name    VARCHAR(64)     DEFAULT NULL                   COMMENT '关联子表的表名',
  sub_table_fk_name VARCHAR(64)     DEFAULT NULL                   COMMENT '子表关联的外键名',
  class_name        VARCHAR(100)    DEFAULT ''                     COMMENT '实体类名称',
  tpl_category      VARCHAR(200)    DEFAULT 'crud'                 COMMENT '使用的模板（crud单表操作 tree树表操作）',
  tpl_web_type      VARCHAR(30)     DEFAULT ''                     COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  package_name      VARCHAR(100)                                   COMMENT '生成包路径',
  module_name       VARCHAR(30)                                    COMMENT '生成模块名',
  business_name     VARCHAR(30)                                    COMMENT '生成业务名',
  function_name     VARCHAR(50)                                    COMMENT '生成功能名',
  function_author   VARCHAR(50)                                    COMMENT '生成功能作者',
  gen_type          CHAR(1)         DEFAULT '0'                    COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  gen_path          VARCHAR(200)    DEFAULT '/'                    COMMENT '生成路径（不填默认项目路径）',
  options           VARCHAR(1000)                                  COMMENT '其它生成选项',
  create_by         VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time       DATETIME                                       COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time       DATETIME                                       COMMENT '更新时间',
  remark            VARCHAR(500)    DEFAULT NULL                   COMMENT '备注'
) UNIQUE KEY(table_id)
DISTRIBUTED BY HASH(table_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '代码生成业务表';

-- ----------------------------
-- 19、代码生成业务表字段
-- ----------------------------
DROP TABLE IF EXISTS gen_table_column;
CREATE TABLE gen_table_column (
  column_id         BIGINT          NOT NULL                       COMMENT '编号',
  table_id          BIGINT                                         COMMENT '归属表编号',
  column_name       VARCHAR(200)                                   COMMENT '列名称',
  column_comment    VARCHAR(500)                                   COMMENT '列描述',
  column_type       VARCHAR(100)                                   COMMENT '列类型',
  java_type         VARCHAR(500)                                   COMMENT 'JAVA类型',
  java_field        VARCHAR(200)                                   COMMENT 'JAVA字段名',
  is_pk             CHAR(1)                                        COMMENT '是否主键（1是）',
  is_increment      CHAR(1)                                        COMMENT '是否自增（1是）',
  is_required       CHAR(1)                                        COMMENT '是否必填（1是）',
  is_insert         CHAR(1)                                        COMMENT '是否为插入字段（1是）',
  is_edit           CHAR(1)                                        COMMENT '是否编辑字段（1是）',
  is_list           CHAR(1)                                        COMMENT '是否列表字段（1是）',
  is_query          CHAR(1)                                        COMMENT '是否查询字段（1是）',
  query_type        VARCHAR(200)    DEFAULT 'EQ'                   COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  html_type         VARCHAR(200)                                   COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  dict_type         VARCHAR(200)    DEFAULT ''                     COMMENT '字典类型',
  sort              INT                                            COMMENT '排序',
  create_by         VARCHAR(64)     DEFAULT ''                     COMMENT '创建者',
  create_time       DATETIME                                       COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                     COMMENT '更新者',
  update_time       DATETIME                                       COMMENT '更新时间'
) UNIQUE KEY(column_id)
DISTRIBUTED BY HASH(column_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "3",
    "enable_unique_key_merge_on_write" = "true"
)
COMMENT = '代码生成业务表字段';

-- ----------------------------
-- 转换说明
-- ----------------------------
-- 1. 所有表都转换为Apache Doris的Unique Key模型
-- 2. 移除了MySQL特有的AUTO_INCREMENT属性
-- 3. 将LONGBLOB类型转换为TEXT类型
-- 4. 调整了数据类型以适配Doris（如bigint(20) -> BIGINT）
-- 5. 添加了Doris特有的分布式和副本配置
-- 6. 启用了写时合并优化（enable_unique_key_merge_on_write = true）
-- 7. 移除了MySQL的索引定义，Doris会自动优化查询
-- 8. 保持了原有的注释和字段含义
--
-- 使用说明：
-- 1. 在Doris中执行此脚本创建表结构
-- 2. 数据插入时，相同主键的记录会自动覆盖（Unique Key特性）
-- 3. 可以根据实际数据量调整BUCKETS数量
-- 4. 可以根据集群规模调整replication_num
-- ----------------------------
