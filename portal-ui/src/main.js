import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from "@/utils/ruoyi";
// vue-virtual-scroller 虚拟列表组件 
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import VueVirtualScroller from 'vue-virtual-scroller'

// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree

// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)

Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
Vue.use(VueVirtualScroller)
DictData.install()

// 全局插入前端数组排序方法
Array.prototype.quickSort = function () {
  const rec = (arr) => {
    // 预防数组是空的或者只有一个元素, 当所有元素都大于等于基准值就会产生空的数组
    if (arr.length === 1 || arr.length === 0) { return arr; }
    const left = [];
    const right = [];
    //以第一个元素作为基准值   
    const mid = arr[0];
    //小于基准值的放左边，大于基准值的放右边
    for (let i = 1; i < arr.length; ++i) {
      if (Number(arr[i]) < (mid)) {
        left.push(arr[i]);
      } else {
        right.push(arr[i]);
      }
    }
    //递归调用，最后放回数组
    return [...rec(left), mid, ...rec(right)];
  };
  const res = rec(this);
  res.forEach((n, i) => { this[i] = n; })
}

/**
 * v-drag dom拖拽方法
 */
Vue.directive('drag', {
  bind(el, binding, vnode) {
    const gap = 100  //  边距小于gap时，则吸附
    const bodyDom = document.body  // body
    const dialogHeaderEl = el.querySelector('.my-dialog__info')   // 能拖拽的部分
    const dragDom = el   // 需要移动的dom
    if (!dialogHeaderEl) return   // 容错

    dialogHeaderEl.style.cursor = 'pointer'  // 此光标指示某对象可被移动

    dialogHeaderEl.onmousedown = (e) => {
      // 算出鼠标相对元素的位置
      const disX = e.clientX - dragDom.offsetLeft
      const disY = e.clientY - dragDom.offsetTop

      document.onmousemove = (e) => {
        binding.value = true
        // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
        let left = e.clientX - disX
        let top = e.clientY - disY
        // 左
        // if (left <= gap) {
        //     right = 0
        // }
        if (left >= bodyDom.offsetWidth - dragDom.offsetWidth - gap) {
          left = bodyDom.offsetWidth - dragDom.offsetWidth
        }

        // 上
        if (top <= gap) {
          top = 90
        }
        if (top >= bodyDom.offsetHeight - dragDom.offsetHeight - gap) {
          top = bodyDom.offsetHeight - dragDom.offsetHeight
        }

        el.style.left = left + 'px'
        el.style.top = top + 'px'
        el.style.zIndex = 9999
      }

      document.onmouseup = (e) => {
        document.onmousemove = null
        document.onmouseup = null
      }
    }
  }
})

Vue.directive('removeAriaHidden', {
  bind(el, binding) {
    let ariaEls = el.querySelectorAll('.el-radio__original');
    ariaEls.forEach((item) => {
      item.removeAttribute('aria-hidden');
    });
  }
});

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
