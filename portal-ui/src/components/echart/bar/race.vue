<template>
    <div :class="'echart-main ' + 'echart-main' + id"></div>
</template>

<script>
import * as echarts from 'echarts';
import { mapState } from 'vuex'

const data = [];
for (let i = 0; i < 1; ++i) {
    data.push(Math.round(Math.random() * 200));
}
let timer;
export default {
    name: "echart-bar-race",
    props: {
        id: {
            type: Number,
            required: false,
            default: () => '1'
        },
        titleText: {
            type: String,
            required: false,
            default: () => ""
        },
        titleColor: {
            type: String,
            required: false,
            default: () => "#FFFFFFF"
        },
        data: {
            type: Array,
            required: false,
            default: () => []
        },
        labels: {
            type: Array,
            required: false,
            default: () => []
        },
    },
    data() {
        return {
            myChart: null,
            option: {}, // 配置项
        };
    },
    computed: {
        ...mapState({
            sidebar: state => state.app.sidebar,
        }),
    },
    watch: {
        'sidebar.opened': function (val) {
            setTimeout(() => {
                // 监听左侧菜单栏开合，重绘页面图表
                this.resizeChart();
            }, 300)
        }
    },
    created() {
        window.addEventListener('resize', () => {
            // 窗口大小变化，重绘Echart图表
            this.resizeChart();
        });
    },
    mounted() {
        this.getEcharDom()
        const that = this
        timer = setInterval(function () {
            that.run();
        }, 1000);
    },
    methods: {
        resizeChart() {
            this.myChart.resize();
        },
        getEcharDom() {
            let chartDom = document.getElementsByClassName('echart-main' + this.id)[0];
            this.myChart = echarts.init(chartDom);
            if (this.myChart) this.setOption();
            this.option && this.myChart.setOption(this.option)
            this.run();
        },
        run() {
            this.myChart.setOption({
                series: [
                    {
                        type: 'bar',
                        data: this.data
                    }
                ]
            });
        },
        setOption() {
            this.option = {
                title: {
                    text: this.titleText,
                    textStyle: {
                        color: this.titleColor || '#FFFFFF'
                    }
                },
                tooltip : {
                    trigger : 'item',
                    formatter : function(params) {
                        return params.name+":"+params.value;
                    }
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    top: '20%',
                    bottom: '15%'
                },
                xAxis: {
                    max: 'dataMax',
                    axisLine: {
                        lineStyle: {
                            color: "rgba(256, 256, 256, 1)"
                        },
                    }
                },
                yAxis: {
                    type: 'category',
                    data: this.labels || [],
                    inverse: true,
                    animationDuration: 300,
                    animationDurationUpdate: 800,
                    max: 4,
                    axisLine: {
                        lineStyle: {
                            color: "rgba(256, 256, 256, 1)"
                        },
                    },
                    axisLabel: {
                        show: true,
                        fontSize: 10,
                        color: '#FFF',
                        // rich: {
                        //     flag: {
                        //         fontSize: 25,
                        //         padding: 5
                        //     }
                        // }
                    },
                },
                series: [
                    {
                        realtimeSort: true,
                        name: '',
                        type: 'bar',
                        size: ['100%', '100%'],
                        data: data,
                        label: {
                            color: '#FFFFFF',
                            show: true,
                            precision: 1,
                            position: 'right',
                            valueAnimation: true,
                            fontFamily: 'monospace',
                            fontSize: 10,
                            fontWeight: 'bold',
                        },
                        itemStyle: {
                            color: function (param) {
                                return '#5470c6'
                            }
                        },
                    }
                ],
                // legend: {
                //     show: false
                // },
                animationDuration: 0,
                animationDurationUpdate: 3000,
                animationEasing: 'linear',
                animationEasingUpdate: 'linear'
            }
        }
    },
    beforeDestroy() {
        clearInterval(timer)
    }
};
</script>

<style scoped lang="scss">
.echart-main {
    width: 100%;
    height: 100%;
}
</style>