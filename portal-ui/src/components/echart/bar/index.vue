<template>
  <div :class="'echart-main ' + 'echart-main' + id"></div>
</template>

<script>
import * as echarts from 'echarts';
import { type } from 'jquery';
import { mapState } from 'vuex'

export default {
  name: "echart-line",
  props: {
    id: {
      type: Number,
      required: false,
      default: () => 1
    },
    titleText: {
      type: String,
      required: false,
      default: () => ""
    },
    titleColor: {
      type: String,
      required: false,
      default: () => "#FFFFFFF"
    },
    titleFontSize: {
      type: String,
      required: false,
      default: () => ""
    },
    // 设置柱状图横向(valueY)、纵向(valueX)，默认横向
    barType: {
      type: String,
      required: false,
      default: () => "valueY"
    },
    // x轴名称
    xAxisName: {
      type: String,
      required: false,
      default: () => ''
    },
    // y轴名称
    yAxisName: {
      type: String,
      required: false,
      default: () => ''
    },
    // xAxis 是否剪切字符串长度
    xAxisFormatter: {
      type: Boolean,
      required: false,
      default: () => false
    },
    // xAxis 展示数据
    xAxisData: {
      type: Array,
      required: false,
      default: () => []
    },
    // yAxis 展示数据
    yAxisData: {
      type: Array,
      required: false,
      default: () => []
    },
    // x、y轴文字颜色
    axisLabelColor: {
      type: String,
      required: false,
      default: () => "#515a6e"
    },
    // series 展示数据
    seriesData: {
      type: Array,
      required: false,
      default: () => []
    },
    // 是否启用点击事件
    isClick: {
      type: Boolean,
      required: false,
      default: () => false
    },
  },
  data() {
    return {
      myChart: null,
      option: {}, // 折线图配置项
    };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  watch: {
    'sidebar.opened': function(val) {
      setTimeout(() => {
        // 监听左侧菜单栏开合，重绘页面图表
        this.resizeChart();
      }, 300)
    },
    'seriesData': function(val) {
      this.getEcharDom()
    }
  },
  created() {
    window.addEventListener('resize', () => {
      // 窗口大小变化，重绘Echart图表
      this.resizeChart();
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.getEcharDom()
    })
  },
  methods: {
    resizeChart() {
      this.myChart.resize();
    },
    getEcharDom() {
      let chartDom = document.getElementsByClassName('echart-main' + this.id)[0];
      this.myChart = echarts.init(chartDom);
      const that = this
      const { myChart } = this
      if (myChart) this.setOption();
      this.option && myChart.setOption(this.option)
      // 是否启用点击事件
      if (this.isClick) {
        this.myChart.on('click', function (params) {
          that.$emit('handleClick', params, '全国')
        })
      }
    },
    setOption() {
      const isFormatter = this.xAxisFormatter
      this.option = {
        title: {
          text: this.titleText,
          textStyle: {
            color : this.titleColor || "#FFFFFF",
            fontSize: this.titleFontSize
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: '{b} : {c}'
        },
        // legend: {},
        grid: {
          left: '3%',
          right: '10%',
          top: '25%',
          bottom: '7%',
          containLabel: true
        },
        xAxis: {
          name: this.xAxisName,
          type: this.barType == 'valueX' ? 'category' : 'value',
          data: this.xAxisData,
          boundaryGap: [0, 0.01],
          axisLine: {
              lineStyle: {
                  color: this.axisLabelColor || "rgba(256, 256, 256, 1)"
              },
          },
          axisLabel: {
            interval: 0,
            rotate: 30,
            formatter: function(val, index) {
              if (isFormatter) {
                if (index % 2 != 0) {
                  return val.substring(0, 2)+'..';
                }
              } else {
                return val
              }
            }
          }
        },
        yAxis: {
          name: this.yAxisName,
          type: this.barType == 'valueX' ? 'value' : 'category',
          axisLine: {
              lineStyle: {
                  color: this.axisLabelColor || "rgba(256, 256, 256, 1)"
              },
          },
          data: this.yAxisData
        },
        series: [
          {
            name: '',
            type: 'bar',
            data: this.seriesData || []
          }
        ]
      };
    },
  }
};
</script>

<style scoped lang="scss">
  .echart-main {
    width: 100%;
    height: 100%;
  }
</style>

