<template>
  <div :class="'echart-dbYaxis-line ' + 'echart-dbYaxis-line' + id"></div>
</template>

<script>
import * as echarts from 'echarts';
import { mapState } from 'vuex'

export default {
  name: "echart-dbYaxis-line",
  props: {
    // 设置 id
    id: {
      type: Number,
      required: false,
      default: () => 1
    },
    titleText: {
      type: String,
      required: false,
      default: () => ""
    },
    titleColor: {
      type: String,
      required: false,
      default: () => "#FFFFFFF"
    },
    // y轴名称
    yAxisLeftName: {
      type: String,
      required: false,
      default: () => ""
    },
    yAxisRightName: {
      type: String,
      required: false,
      default: () => ""
    },
    // xAxis数据
    xAxisData: {
      type: Array,
      required: false,
      default: () => []
    },
    // x、y轴文字颜色
    axisLabelColor: {
      type: String,
      required: false,
      default: () => "#515a6e"
    },
    // 设置tooltip的position位置
    tooltipPosition: {
      type: Array,
      required: false,
      default: () => []
    },
    // legend数据项
    legendData: {
      type: Array,
      required: false,
      default: () => []
    },
    // series数据项
    seriesData: {
      type: Array,
      required: false,
      default: () => []
    }
  },
  data() {
    return {
      myChart: null,
      option: {}, // 折线图配置项
    };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  watch: {
    'sidebar.opened': function (val) {
      setTimeout(() => {
        // 监听左侧菜单栏开合，重绘页面图表
        this.resizeChart();
      }, 300)
    },
    'seriesData': function (val) {
      this.getEcharDom();
    }
  },
  created() {
    window.addEventListener('resize', () => {
      // 窗口大小变化，重绘Echart图表
      this.resizeChart();
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.getEcharDom()
    })
  },
  methods: {
    resizeChart() {
      this.myChart.resize();
    },
    getEcharDom() {
      // this.$nextTick(() => {
      let chartDom = document.getElementsByClassName('echart-dbYaxis-line' + this.id)[0];
      this.myChart = echarts.init(chartDom);
      const { myChart } = this
      if (myChart) this.setOption();
      this.option && myChart.setOption(this.option)
      // })
    },
    setOption() {
      const that = this
      this.option = {
        title: {
          text: this.titleText,
          textStyle: {
            color : this.titleColor,
            fontSize: '12px'
          }
        },
        tooltip: {
            trigger: 'axis',
            position: this.tooltipPosition,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            formatter: function(params) {
              let dom = '', name = '--';
              for (let i=0; i<params.length; i++) {
                const isScore = params[i].seriesName.indexOf('分数') !== -1;
                name = params[i].name;
                dom += '<div class="fontStyle">'
                  + '<div style="background-color:' + params[i].color + '; width: 10px; height: 10px; border-radius: 10px; margin-right: 10px;"></div>'
                  + params[i].seriesName + ': ' + (isScore ? (params[i].value + '分') : (params[i].value + '%'))
                  + '</div>';
              }
              let start = dom.slice(0, dom.indexOf('<div class="fontStyle">'));
              let end = dom.slice(dom.indexOf('<div class="fontStyle">'));
              dom = start + '<div style="font-weight: bold;">' + name + '</div>' + end;
              return dom
            }
        },
        legend: {
          show: false,
          right: '2%',
          data: this.legendData
        },
        grid: {
          left: '10%',
          right: '13%',
          top: '30%',
          bottom: '12%'
        },
        xAxis: {
            type: 'category',
            data: this.xAxisData,
            axisLabel: {
              interval: 0,
              fontSize: "10px",
              formatter:function(value, index) {
                if (index % 2 != 0) {
                  return value.substring(0, 2)+'..';
                }
              },
            },
            axisLine: {
              lineStyle: {
                color: this.axisLabelColor || "rgba(256, 256, 256, 1)"
              },
            },
        },
        yAxis: [
            {
              type: 'value',
              name: this.yAxisLeftName,
              position: 'left',
              seriesIndex: 0, // 使用这个Y轴的系列
              axisLine: {
                  lineStyle: {
                      color: this.axisLabelColor || "rgba(256, 256, 256, 1)"
                  },
              },
            },
            {
              type: 'value',
              name: this.yAxisRightName,
              position: 'right',
              seriesIndex: 1, // 使用这个Y轴的系列
              axisLine: {
                  lineStyle: {
                      color: this.axisLabelColor || "rgba(256, 256, 256, 1)"
                  },
              },
            }
        ],
        series: this.seriesData
      };
    },
  }
};
</script>

<style scoped lang="scss">
.echart-dbYaxis-line {
  width: 100%;
  height: 100%;
}
::v-deep .fontStyle {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 12px;
  color: #d438ff;
  font-weight: bold;
}
</style>
