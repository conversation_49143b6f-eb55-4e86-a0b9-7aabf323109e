<template>
  <div :class="'echart-line ' + 'echart-line' + id"></div>
</template>

<script>
import * as echarts from 'echarts';
import { mapState } from 'vuex'

export default {
  name: "echart-line",
  props: {
    // 设置 id
    id: {
      type: Number,
      required: false,
      default: () => 1
    },
  },
  data() {
    return {
      myChart: null,
      option: {}, // 折线图配置项
    };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  watch: {
    'sidebar.opened': function (val) {
      setTimeout(() => {
        // 监听左侧菜单栏开合，重绘页面图表
        this.resizeChart();
      }, 300)
    }
  },
  created() {
    window.addEventListener('resize', () => {
      // 窗口大小变化，重绘Echart图表
      this.resizeChart();
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.getEcharDom()
    })
  },
  methods: {
    resizeChart() {
      this.myChart.resize();
    },
    getEcharDom() {
      // this.$nextTick(() => {
      let chartDom = document.getElementsByClassName('echart-line' + this.id)[0];
      this.myChart = echarts.init(chartDom);
      const { myChart } = this
      if (myChart) this.setOption();
      this.option && myChart.setOption(this.option)
      // })
    },
    setOption() {
      this.option = {
        title: {
          show: false,
          text: ''
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '13%',
          bottom: '12%'
        },
        tooltip: {
          trigger: 'axis',
          show: true,
          axisPointer: {
            type: 'line', // 或者 'cross' 为十字准线，还可以是 'shadow'
            lineStyle: {
              color: '#95ff5d' // 这里设置为红色
            }
          }
        },
        legend: {},
        backgroundColor: '#011a33',
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周天'],
          name: '(周)',
          axisLabel: {
            textStyle: {
              color: '#fff' // 修改为红色
            }
          }

        },
        yAxis: {
          name: '(辆)',
          type: 'value',
          splitNumber: 3,
          axisLabel: {
            textStyle: {
              color: '#fff' // 修改为红色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgb(70, 88, 105)'
            }
          }
        },
        series: [
          {
            type: 'line',
            data: [15000, 11000, 9000, 22000, 20000, 13000, 10000],
            lineStyle: {
              color: 'rgb(153, 0, 153)',
            },
            itemStyle: {
              color: 'rgb(153, 0, 153)', // 数据点颜色
            },
            markPoint: {
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' }
              ]
            }
          }
        ]
      };
    },
  }
};
</script>

<style scoped lang="scss">
.echart-line {
  width: 100%;
  height: 100%;
}
</style>
