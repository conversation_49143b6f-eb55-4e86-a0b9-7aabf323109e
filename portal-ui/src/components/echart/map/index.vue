<template>
  <div class="container">
    <div id="echarts-map"></div>
    <div class="chart01_text"></div>
    <div class="chart02_text"></div>
    <div class="chart03_text"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { mapState } from 'vuex'
import axios from 'axios'
import { mapsData, level3 } from './mock.js';

let level = 1; //记录地图层级
export default {
  name: "echart-map",
  props: [],
  data() {
    return {
      myChart: null,
      allData: [],
      current_obj: {},
      // 各省份地图JSON数据
      provinces: {
        '上海市': '../json/shanghai.json',
        '河北省': '../json/hebei.json',
        '山西省': '../json/shanxi.json',
        '内蒙古自治区': '../json/neimenggu.json',
        '辽宁省': '../json/liaoning.json',
        '吉林省': '../json/jilin.json',
        '黑龙江省': '../json/heilongjiang.json',
        '江苏省': '../json/jiangsu.json',
        '浙江省': '../json/zhejiang.json',
        '安徽省': '../json/anhui.json',
        '福建省': '../json/fujian.json',
        '江西省': '../json/jiangxi.json',
        '山东省': '../json/shandong.json',
        '河南省': '../json/henan.json',
        '湖北省': '../json/hubei.json',
        '湖南省': '../json/hunan.json',
        '广东省': '../json/guangdong.json',
        '广西壮族自治区': '../json/guangxi.json',
        '海南省': '../json/hainan.json',
        '四川省': 'sichuan',
        '贵州省': '../json/guizhou.json',
        '云南省': '../json/yunnan.json',
        '西藏自治区': '../json/xizang.json',
        '陕西省': '../json/shanxi1.json',
        '甘肃省': '../json/gansu.json',
        '青海省': '../json/qinghai.json',
        '宁夏回族自治区': '../json/ningxia.json',
        '新疆维吾尔自治区': '../json/xinjiang.json',
        '北京市': '../json/beijing.json',
        '天津市': '../json/tianjin.json',
        '重庆市': '../json/chongqing.json',
        '香港特别行政区': '../json/xianggang.json',
        '澳门特别行政区': '../json/aomen.json',
        '成都市': 'chengdu'
      },
    };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  watch: {
    'sidebar.opened': function (val) {
      setTimeout(() => {
        // 监听左侧菜单栏开合，重绘页面图表
        this.resizeChart();
      }, 300)
    }
  },
  created() {
    window.addEventListener('resize', () => {
      // 窗口大小变化，重绘Echart图表
      this.resizeChart();
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.creatMap()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart());
  },
  methods: {
    resizeChart() {
      this.myChart.resize();
    },
    creatMap() {
      let chartDom = document.getElementById('echarts-map');
      this.myChart = echarts.init(chartDom);
      const { myChart } = this;
      let option;
      let index = 0;
      level = 1; //记录地图层级
      //各省份的数据
      this.allData = mapsData();
      // allData.forEach(function(province) {
      //   province.value = Math.round(Math.random() * 100);
      // });
      this.loadMap('china', '中国', level);//初始化全国地图
      let timeFn = null;
      const that = this;
      let clickTimer;
      //单击切换到省级地图，当mapCode有值,说明可以切换到下级地图
      myChart.on('click', function (params) {
        // 清除之前的定时器
        clearTimeout(clickTimer);
        // 设置一个短暂的定时器
        clickTimer = setTimeout(function() {
          const key = params?.name;
          const api_name = that.provinces[key];
          // 遍历获取是否是区、县
          const arr = level3()
          let count = 0
          arr.forEach(item => {
            if (item.name == key) {
              level = 3
            } else {
              count++
            }
          })
          // 记录上一级地图
          if (arr.length == count) {
            this.current_obj = {
              api_name,
              key
            }
          }
          if (level == 2) {
            // level++
            that.$emit('handleMapEvent', 'jumpAmap', key, level)
            return
          }
          // 地图向下钻取
          if (api_name) {
            level++
            that.$emit('handleMapEvent', 'normal', key, level)
            that.loadMap(api_name, key, level);
          }
        }, 300); // 300毫秒内为单击事件，超过则为双击事件
      });
      // 绑定双击事件，返回全国地图
      myChart.on('dblclick', function (params) {
        // 清除单击事件的定时器
        clearTimeout(clickTimer);
        // 双击事件内容
        level = 1;
        that.loadMap('china', '中国', level);//初始化全国地图
        const key = params?.name;
        setTimeout(() => {
          that.$emit('handleMapEvent', 'all', key, level)
        }, 500)
        return
      });
    },
    /**
      获取对应的json地图数据，然后向echarts注册该区域的地图，最后加载地图信息
      @params {String} mapCode:json数据的地址
      @params {String} name: 地图名称
    */
    loadMap(api_name, name, level) {
      axios({
        method: 'post',
        url: 'https://mock.apipark.cn/m1/4707150-4359301-default/dev-api/' + api_name,
      }).then(res => {
        const { data } = res
        echarts.registerMap(name, data);
        var option = {
          tooltip: {
            show: true,
            formatter: function (params) {
              // 返回地图数据的名称和值
              return params.name + '：' + params.value + "辆";
            },
          },
          visualMap: {
            type: 'continuous',
            text: ['', ''],
            showLabel: true,
            top: '60%', // 垂直居中
            right: '10%',
            min: 0,
            max: 300000,
            inRange: {
              color: ['#edfbfb', '#b7d6f3', '#40a9ed', '#3598c1', '#215096',]
            },
            splitNumber: 0
          },
          series: [{
            name: 'MAP',
            type: 'map',
            mapType: name,
            layoutCenter: ['50%', '50%'], // 将地图定位在容器中心的上方
            layoutSize: '100%', // 地图大小
            selectedMode: 'false',//是否允许选中多个区域
            label: {
              normal: {
                show: true
              },
              emphasis: {
                show: true
              }
            },
            data: this.allData
          }]
        };
        this.myChart.setOption(option);
      }).catch(err => {
        level--
      })
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
}

#echarts-map {
  width: 100%;
  height: 100%;
}
</style>
