<template>
  <div :class="'echart-boxplot ' + 'echart-boxplot' + id"></div>
</template>

<script>
import * as echarts from 'echarts';
import { type } from 'jquery';
import { mapState } from 'vuex'

export default {
  name: "echart-boxplot",
  props: {
    // 设置 id
    id: {
      type: Number,
      required: false,
      default: () => 1
    },
    // 设置盒须图source数据
    source: {
      type: Array,
      require: false,
      default: () => []
    },
    // 设置盒须图y轴data
    yAxisData: {
      type: Array,
      require: false,
      default: () => []
    }
  },
  data() {
    return {
      myChart: null,
      option: {}, // 折线图配置项
    };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  watch: {
    'sidebar.opened': function(val) {
      setTimeout(() => {
        // 监听左侧菜单栏开合，重绘页面图表
        this.resizeChart();
      }, 300)
    }
  },
  created() {
    window.addEventListener('resize', () => {
      // 窗口大小变化，重绘Echart图表
      this.resizeChart();
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.getEcharDom()
    })
  },
  methods: {
    resizeChart() {
      this.myChart.resize();
    },
    getEcharDom() {
      // this.$nextTick(() => {
        let chartDom = document.getElementsByClassName('echart-boxplot' + this.id)[0];
        this.myChart = echarts.init(chartDom);
        const { myChart } = this
        if (myChart) this.setOption();
        this.option && myChart.setOption(this.option)
      // })
    },
    setOption() {
      const that = this
      this.option = {
        dataset: [
          {
            source: this.source
          },
          {
            transform: {
              type: 'boxplot',
              config: {
                itemNameFormatter: function (params) {
                  const i = params.value;
                  return that.yAxisData[i];
                }
              }
            }
          },
          {
            fromDatasetIndex: 1,
            fromTransformResult: 1
          }
        ],
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '5%',
          bottom: '10%'
        },
        yAxis: {
          type: 'category',
          boundaryGap: true,
          // data: this.yAxisData, // 类别数据
          nameGap: 30,
          splitArea: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        xAxis: {
          type: 'value',
          name: '数据占比(%)',
        },
        series: [
          {
            name: 'boxplot',
            type: 'boxplot',
            datasetIndex: 1,
            itemStyle: {
              color: '#409eff61'
            },
          },
          {
            name: '',
            type: 'scatter',
            encode: { x: 1, y: 0 },
            datasetIndex: 2,
          }
        ]
      };
    },
  }
};
</script>

<style scoped lang="scss">
  .echart-boxplot {
    width: 100%;
    height: 100%;
  }
</style>

