<template>
  <div :class="'echart-pie ' + 'echart-pie' + id"></div>
</template>

<script>
import * as echarts from 'echarts';
import { mapState } from 'vuex'

export default {
  name: "echart-pie",
  props: {
    id: {
      type: Number,
      required: false,
      default: () => 1
    },
    pieData: {
      type: Array,
      required: false,
      default: () => []
    },
  },
  data() {
    return {
      myChart: null,
      option: {}, // 折线图配置项
    };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  watch: {
    'sidebar.opened': function(val) {
      setTimeout(() => {
        // 监听左侧菜单栏开合，重绘页面图表
        this.resizeChart();
      }, 300)
    }
  },
  created() {
    window.addEventListener('resize', () => {
      // 窗口大小变化，重绘Echart图表
      this.resizeChart();
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.getEcharDom()
    })
  },
  methods: {
    resizeChart() {
      this.myChart.resize();
    },
    getEcharDom() {
      // this.$nextTick(() => {
        let chartDom = document.getElementsByClassName('echart-pie' + this.id)[0];
        this.myChart = echarts.init(chartDom);
        const { myChart } = this
        if (myChart) this.setOption();
        this.option && myChart.setOption(this.option)
      // })
    },
    setOption() {
      this.option = {
        title: {
          text: 'Referer',
          subtext: 'Data',
          left: 'center',
          show: false
        },
        tooltip: {
          trigger: 'item'
        },
        // legend: {
        //   orient: 'vertical',
        //   left: 'left'
        // },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '40%',
            data: this.pieData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            "label": {
              "normal": {
                "show": true,
                "textStyle": {
                  "fontSize": 12,
                }
              },
              "emphasis": {
                "show": true
              }
            },
          }
        ]
      };
    },
  }
};
</script>

<style scoped lang="scss">
  .echart-pie {
    width: 100%;
    height: 100%;
  }
</style>

