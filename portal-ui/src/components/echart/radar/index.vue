<template>
  <div :class="'echart-main ' + 'echart-main' + id"></div>
</template>

<script>
import * as echarts from 'echarts';
import { data } from 'jquery';
import { mapState } from 'vuex'

export default {
  name: "echart-radar",
  props: {
    // 设置 id
    id: {
      type: Number,
      required: false,
      default: () => 1
    },
    titleText: {
      type: String,
      required: false,
      default: () => ""
    },
    titleColor: {
      type: String,
      required: false,
      default: () => "#FFFFFFF"
    },
    // 背景色
    backgroundColor: {
      type: String,
      required: false,
      default: () => "#011a33"
    },
    // 数据节点名称
    indicator: {
      type: Array,
      required: false,
      default: () => [
        { name: '节点 1' },
        { name: '节点 2' },
        { name: '节点 3' },
        { name: '节点 4' },
        { name: '节点 5' },
        { name: '节点 6' }
      ]
    },
  },
  data() {
    return {
      myChart: null,
      data:[
        {
          value: [4200, 3000, 20000, 35000, 50000],
          name: '数据1'
        },
        {
          value: [5000, 14000, 28000, 26000, 42000],
          name: '数据2'
        }
      ],
      option: {}, // 折线图配置项
      indexPoint: '', // 当前选择的点下标
    };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  watch: {
    'sidebar.opened': function(val) {
      setTimeout(() => {
        // 监听左侧菜单栏开合，重绘页面图表
        this.resizeChart();
      }, 300)
    }
  },
  created() {
    window.addEventListener('resize', () => {
      // 窗口大小变化，重绘Echart图表
      this.resizeChart();
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.getEcharDom()
    })
  },
  methods: {
    resizeChart() {
      this.myChart.resize();
    },
    getEcharDom() {
      const that = this
      let chartDom = document.getElementsByClassName('echart-main' + this.id)[0];
      this.myChart = echarts.init(chartDom);
      const { myChart } = this
      if (myChart) this.setOption();
      this.option && myChart.setOption(this.option)
      myChart.on("mouseover", function(params){
        const __dimIdx = !['', undefined, null].includes(params.event.target.__dimIdx) ? params.event.target.__dimIdx.toString() : ''
        if (__dimIdx) {
          that.$set(that, 'indexPoint', __dimIdx)
        } else {
          that.$set(that, 'indexPoint', '')
        }
      });
    },
    setOption() {
      const that = this
      this.option = {
        backgroundColor: that.backgroundColor,
        title: {
          show: this.titleText ? true :  false,
          text: this.titleText,
          textStyle: {
            color : this.titleColor,
            fontSize: '12px'
          }
        },
        tooltip: {
          trigger: 'item',
          show: true,
          formatter: function(params) {
            const indexPoint = that.indexPoint
            if (indexPoint) {
              for (let i=0; i<that.data.length; i++) {
                if (that.data[i].name == params.name) {
                  return params.name + '<br/>' + that.indicator[indexPoint].name + ': ' + that.data[i].value[indexPoint]
                }
              }
            }
          }
        },
        toolbox: {
        },
        legend: {
          show: false,
        },
        radar: {
          // shape: 'circle',
          indicator: that.indicator,
          radius: '60%',// 半径
        },
        series: [
          {
            name: 'Budget vs spending',
            type: 'radar',
            data: this.data
          }
        ]
      };
    },
  }
};
</script>

<style scoped lang="scss">
  .echart-main {
    width: 100%;
    height: 100%;
  }
</style>

