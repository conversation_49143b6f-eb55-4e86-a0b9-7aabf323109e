<template>
    <div class="container">
        <el-table
          id="myTable"
          :data="tableData"
          stripe
          :height="isAutoHeight ? autoHeight : null"
        >
          <!-- 数据展示列 -->
          <el-table-column
            v-for="(item, index) in columns"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          >
            <template slot-scope="scope">
              <el-tag
                type="success"
                v-if="scope.column.property == 'vin'"
                @click="handleClickTag(scope.row)"
              >{{ scope.row.vin }}</el-tag>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column label="操作" :align="'center'" fixed="right" v-if="operate.length > 0">
            <template slot-scope="scope" v-for="el in operate">
              <div class="operate">
                <el-button v-show="el == '信息'" type="text" size="small" @click="handleDetail(scope.row)">详细信息</el-button>
                <el-button v-show="el == '轨迹'" type="text" size="small" @click="handlePath(scope.row)">轨迹</el-button>
                <el-button v-show="el == '定位'" type="text" size="small" @click="handleLocation(scope.row)">定位</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
        </div>
        <!-- 弹窗 -->
        <el-dialog
          :title="dialogTitle"
          :visible.sync="dialogVisible"
          width="80%"
          :before-close="handleClose"
        >
          <template v-if="dialogTitle == '车辆详情'">
            <div class="dialog-detail">
              <el-descriptions title="车辆信息">
                <el-descriptions-item label="燃料类型">{{ currentCar.epa_vehicle_type}}</el-descriptions-item>
                <el-descriptions-item label="车辆分类">{{ currentCar.veh_catalogue }}</el-descriptions-item>
                <el-descriptions-item label="车辆类别">{{ currentCar.veh_catalogue_detail }}</el-descriptions-item>
                <el-descriptions-item label="排放阶段">{{ currentCar.engine_emission_leveltype }}</el-descriptions-item>
                <el-descriptions-item label="车牌号">{{ currentCar.vehicle_license }}</el-descriptions-item>
                <el-descriptions-item label="车籍地">{{ currentCar.vehicle_domicile }}</el-descriptions-item>
                <el-descriptions-item label="备案协议类型">{{ currentCar.register_agreement }}</el-descriptions-item>
                <el-descriptions-item label="实际协议类型">{{ currentCar.actual_agreement }}</el-descriptions-item>
                <el-descriptions-item label="激活模式">{{ currentCar.veh_register_mode }}</el-descriptions-item>
                <el-descriptions-item label="车辆备案企业名称">{{ currentCar.company_name }}</el-descriptions-item>
                <el-descriptions-item label="车辆备案企业类型">{{ currentCar.company_type }}</el-descriptions-item>
                <el-descriptions-item label="企业平台名称">{{ currentCar.platform_name }}</el-descriptions-item>
                <el-descriptions-item label="平台建设方式">{{ currentCar.platform_mode }}</el-descriptions-item>
                <el-descriptions-item label="车辆生产企业名称">{{ currentCar.manufacturer_build_name }}</el-descriptions-item>
                <el-descriptions-item label="车辆底盘生产厂家">{{ currentCar.chassis_company_name }}</el-descriptions-item>
                <el-descriptions-item label="静态备案时间">{{ currentCar.register_time }}</el-descriptions-item>
                <el-descriptions-item label="车辆同步状态">{{ currentCar.sync_status }}</el-descriptions-item>
                <el-descriptions-item label="车辆同步时间">{{ currentCar.sync_time }}</el-descriptions-item>
                <el-descriptions-item label="终端激活状态">{{ currentCar.activation_status }}</el-descriptions-item>
                <el-descriptions-item label="终端激活时间">{{ currentCar.activation_time }}</el-descriptions-item>
                <el-descriptions-item label="首次上线时间">{{ currentCar.first_online_time }}</el-descriptions-item>
                <el-descriptions-item label="首次上线状态">{{ currentCar.first_online_status }}</el-descriptions-item>
                <el-descriptions-item label="车辆联网状态">
                  <el-tag size="small" type="success" v-if="currentCar.connect_status == '已联网'">{{ currentCar.connect_status }}</el-tag>
                  <el-tag size="small" type="danger" v-else>{{ currentCar.connect_status }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="车辆联网时间">{{ currentCar.connect_time }}</el-descriptions-item>
                <el-descriptions-item label="车辆型号">{{ currentCar.vehicle_model }}</el-descriptions-item>
                <el-descriptions-item label="车辆类型">{{ currentCar.vehicle_type }}</el-descriptions-item>
                <el-descriptions-item label="总质量">{{ currentCar.max_loads }}</el-descriptions-item>
                <el-descriptions-item label="发动机型号">{{ currentCar.engine_model }}</el-descriptions-item>
                <el-descriptions-item label="马力">{{ currentCar.engine_power }}</el-descriptions-item>
                <el-descriptions-item label="扭矩">{{ currentCar.engine_torque }}</el-descriptions-item>
                <el-descriptions-item label="发动机生产地址">{{ currentCar.engine_production_address }}</el-descriptions-item>
                <el-descriptions-item label="发动机生产厂家">{{ currentCar.engine_manufacturer_name }}</el-descriptions-item>
                <el-descriptions-item label="终端型号">{{ currentCar.tbox_model }}</el-descriptions-item>
                <el-descriptions-item label="终端生产厂家">{{ currentCar.tbox_manufacturer_name }}</el-descriptions-item>
                <el-descriptions-item label="三位标识符">{{ currentCar.chip_prefix }}</el-descriptions-item>
                <el-descriptions-item label="芯片型号">{{ currentCar.chip_model }}</el-descriptions-item>
                <el-descriptions-item label="芯片制造企业">{{ currentCar.chip_manufacturer_name }}</el-descriptions-item>
                <el-descriptions-item label="生产日期(国产)/进口日期(国产)">{{ currentCar.manufacture_date }}</el-descriptions-item>
                <el-descriptions-item label="国别">{{ currentCar.country }}</el-descriptions-item>
                <el-descriptions-item label="最大净功率">{{ currentCar.maximum_power }}</el-descriptions-item>
                <el-descriptions-item label="最大净功率转速">{{ currentCar.maximum_engine_rotationl_speed }}</el-descriptions-item>
                <el-descriptions-item label="额定功率">{{ currentCar.rated_capacity }}</el-descriptions-item>
                <el-descriptions-item label="额定功率转速">{{ currentCar.rated_capacity_rotationl_speed }}</el-descriptions-item>
                <el-descriptions-item label="最大净扭矩">{{ currentCar.maximum_torque }}</el-descriptions-item>
                <el-descriptions-item label="最大净扭矩转速">{{ currentCar.maximum_torque_rotationl_speed }}</el-descriptions-item>
                <el-descriptions-item label="燃料供给系统形式">{{ currentCar.fuel_feed_system }}</el-descriptions-item>
                <el-descriptions-item label="排气后处理系统形式">{{ currentCar.exhaust_aftertreatment }}</el-descriptions-item>
                <el-descriptions-item label="环保公开信息接收时间">{{ currentCar.public_time }}</el-descriptions-item>
              </el-descriptions>
            </div>
            <span slot="footer" class="dialog-footer">
              <el-button @click="dialogVisible = false">取 消</el-button>
              <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
            </span>
          </template>
          <template v-else>
            <div class="dialog-path-container">
              <move-trajectory
                ref="trajectory"
                v-if="isShow"
                :lineArr="lineArr"
              ></move-trajectory>
            </div>
          </template>
        </el-dialog>
    </div>
  </template>
  
  
  <script>
  // Api
  import { getTrajectoryData } from "@/api/maps/map";
  // 公共方法
  import { createGps } from "@/utils/transform"
  // 导入组件
  import moveTrajectory from '@/components/amap/move'
  // mock
  import gaopai_gj from './gaopai_gj.json'

  export default {
    name: "Table",
    components: {
      moveTrajectory,
    },
    props: {
      // 是否启用自动表格高度
      isAutoHeight: {
        type: Boolean,
        required: false,
        default: () => true
      },
      operate: {
        type: Array,
        required: false,
        default: () => []
      },
      // 表格列
      columns: {
        type: Array,
        required: false,
        default: () => [
          {
            prop: "id",
            label: "序号",
            width: "60"
          }
        ]
      },
      // 表格数据
      tableData: {
        type: Array,
        required: false,
        default: () => []
      },
      // 是否自定义操作列按钮跳转页面
      isCustomJump: {
        type: Boolean,
        required: false,
        default: () => false
      },
    },
    data() {
      return {
        loading: false,
        isShow: false,
        lineArr: [],
        // 表格
        autoHeight: 'auto',
        total: 0,
        currentPage: 1,
        pageSize: 10,
        // 弹窗
        dialogTitle: '车辆详情',
        dialogVisible: false,
        currentCar: {}
      };
    },
    created() {
      this.total = this.tableData.length;
    },
    mounted() {
      this.$nextTick(() => {
        this.getHeight()
        //  监听窗口大小变化
        window.addEventListener('resize', () => {
          this.getHeight()
        });
      })
    },
    methods: {
      getHeight() {
        let getHeightFromBottom = (element, variableHeight) => {
          const elementRect = element.getBoundingClientRect().top;
          const windowHeight = window.innerHeight;
          const elementHeightFromBottom = windowHeight - elementRect;
          const result = elementHeightFromBottom - variableHeight - 50;
          return result;
        }
        const element = document.getElementById('myTable');
        const variableHeight = 70; // 给定的变量高度 一般留于分页器高度
        this.autoHeight = getHeightFromBottom(element, variableHeight);
      },
      handleDetail(row) {
        if (this.isCustomJump) {
          this.$emit('handleClick', row)
          return
        }
        this.dialogTitle = '车辆详情'
        this.dialogVisible = true
      },
      handlePath(row) {
        this.dialogTitle = '车辆轨迹'
        this.loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.handleTrajectoryData(row)
      },
      handleLocation(row) {
        this.$emit('handleLocation', row)
      },
      handleClose() {
        if (this.$refs['trajectory']) {
          this.$refs['trajectory'].destroyMap();
        }
        this.dialogVisible = false
        this.isShow = false
      },
      handleSizeChange(e) {
        this.pageSize = e
      },
      handleCurrentChange(e) {
        this.currentPage = e
      },
      // 查询轨迹数据
      handleTrajectoryData(row) {
        this.lineArr = []
        if (row?.vin) {
          for (let i=0; i<gaopai_gj.length; i++) {
            if (gaopai_gj[i]?.vin == row?.vin) {
              const location = gaopai_gj[i]?.location
              for ( let j=0; j<location.length; j++) {
                this.lineArr.push(
                  [
                    createGps({ longitude: location[j].lon, latitude: location[j].lat })[0],
                    createGps({ longitude: location[j].lon, latitude: location[j].lat })[1],
                  ]
                )
              }
            }
          }
          this.isShow = true
          this.loading.close()
          this.dialogVisible = true
        }
        return
        getTrajectoryData({
          params1: 1,
          params2: 2,
          params3: 3
        }).then(res => {
          this.lineArr = res?.data || []
          this.isShow = true
          this.loading.close()
          this.dialogVisible = true
        }).catch(err => {
          this.loading.close()
        })
      },
      handleClickTag (row) {
        this.dialogTitle = '车辆详情'
        this.dialogVisible = true
        this.currentCar = {
          vin: 'LEFAEDC2XNHN10389',
          epa_vehicle_type: '柴油车',
          veh_catalogue: 'N2',
          veh_catalogue_detail: 'N2(非城市车辆)',
          engine_emission_leveltype: '国六',
          vehicle_license: '川A40FQ0',
          vehicle_domicile: '成都市',
          register_agreement: '1',
          actual_agreement: '1',
          veh_register_mode: '无需激活',
          company_name: '江铃汽车股份有限公司',
          company_type: 'NULL',
          platform_name: '江铃环保排放监控平台',
          platform_mode: 'NULL',
          manufacturer_build_name: '江铃汽车股份有限公司',
          chassis_company_name: 'NULL',
          register_time: '2022-01-31 08:00:05',
          sync_status: '已同步',
          sync_time: '2022-01-31 08:00:05',
          activation_status: 'NULL',
          activation_time: 'NULL',
          first_online_time: '2022-02-08 18:15:27',
          first_online_status: '已上线',
          connect_status: '已联网',
          connect_time: '2022-02-08 18:15:27',
          vehicle_model: 'JX1041TCF26',
          vehicle_type: '载货汽车',
          vehicle_loads: '4',
          max_loads: '1',
          engine_model: 'JX493ZLQ6A',
          engine_power: 'NULL',
          engine_torque: 'NULL',
          engine_production_address: 'NULL',
          engine_manufacturer_name: '江铃汽车股份有限公司',
          tbox_model: 'JMCTBOX2',
          tbox_manufacturer_name: '北京经纬恒润科技股份有限公司',
          chip_prefix: 'NULL',
          chip_model: 'NULL',
          chip_manufacturer_name: 'NULL',
          manufacture_date: '2022-01-28',
          country: '中国',
          maximum_power: '88',
          maximum_engine_rotationl_speed: '3200',
          rated_capacity: '90',
          rated_capacity_rotationl_speed: '3200',
          maximum_torque: '325',
          maximum_torque_rotationl_speed: '1600-2400',
          fuel_feed_system: '高压共轨',
          exhaust_aftertreatment: 'NULL',
          public_time: 'NULL'
        }
      }
    },
    beforeDestroy() {
      window.removeEventListener("resize",() => {
        this.getHeight()
      });
    }
  };
  </script>
  
  <style scoped lang="scss">
  .detail {
    // 表格
    .container {
      width: 100%;
      height: calc(100% - 52px);
    }
    .pagination {
      width: 100%;
      display: flex;
      justify-content: right;
      margin-top: 20px;
    }
    .operate {
      display: flex;
      flex-direction: column;
      .el-button {
        margin-left: 0px;
      }
    }
    .el-table__body-wrapper {
      width: 100%;
      height: calc(100% - 75px);
    }
    // 弹窗
    .dialog-detail {
      width: 100%;
      height: auto;
      .dialog-detail-row {
        width: 100%;
        height: auto;
        display: flex;
        justify-content: flex-start;
        margin-bottom: 10px;
        .dialog-detail-item {
          width: auto;
          margin-right: 10px;
          .dialog-detail-item-title {
            width: auto;
            font-size: 17px;
            font-weight: bold;
          }
          .dialog-detail-item-value {
            width: auto;
            min-width: 60px;
            font-size: 13px;
            display: inline-block;
          }
        }
      }
    }
    .dialog-path-container {
      width: 100%;
      height: 60vh;
    }
  }
  ::v-deep .el-tag {
    cursor: pointer;
  }
  </style>
  