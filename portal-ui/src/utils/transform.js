
//-------Start坐标系转换--------
 
// 定义一些常量
const x_PI = 3.14159265358979324 * 3000.0 / 180.0
const PI = 3.1415926535897932384626
const a = 6378245.0
const ee = 0.00669342162296594323
 
/**
 * 百度坐标系 (BD-09) 与 火星坐标系 (GCJ-02)的转换 / 即百度转谷歌、高德
 * @param { Number } bd_lon
 * @param { Number } bd_lat
 */
export function bd09togcj02 (bd_lon, bd_lat) {
  var x_pi = 3.14159265358979324 * 3000.0 / 180.0
  var x = bd_lon - 0.0065
  var y = bd_lat - 0.006
  var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi)
  var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi)
  var gg_lng = z * Math.cos(theta)
  var gg_lat = z * Math.sin(theta)
  return [gg_lng, gg_lat]
}
 
/**
 * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换 / 即谷歌、高德 转 百度
 * @param { Number } lng
 * @param { Number } lat
 */
export function gcj02tobd09 (lng, lat) {
  var z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI)
  var theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI)
  var bd_lng = z * Math.cos(theta) + 0.0065
  var bd_lat = z * Math.sin(theta) + 0.006
  return [bd_lng, bd_lat]
}
 
/**
 * WGS84坐标系转火星坐标系GCj02 / 即WGS84 转谷歌、高德
 * @param { Number } lng 
 * @param { Number } lat 
 */
export function wgs84togcj02 (lng, lat) {
  if (outOfChina(lng, lat)) {
    return [lng, lat]
  }
  else {
    var dlat = transformlat(lng - 105.0, lat - 35.0)
    var dlng = transformlng(lng - 105.0, lat - 35.0)
    var radlat = lat / 180.0 * PI
    var magic = Math.sin(radlat)
    magic = 1 - ee * magic * magic
    var sqrtmagic = Math.sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
    dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
    const mglat = lat + dlat
    const mglng = lng + dlng
    return [mglng, mglat]
  }
}
 
/**
 * GCJ02（火星坐标系） 转换为 WGS84 / 即谷歌高德转WGS84
 * @param { Number } lng 
 * @param { Number } lat 
 */
export function gcj02towgs84 (lng, lat) {
  if (outOfChina(lng, lat)) {
    return [lng, lat]
  }
  else {
    var dlat = transformlat(lng - 105.0, lat - 35.0)
    var dlng = transformlng(lng - 105.0, lat - 35.0)
    var radlat = lat / 180.0 * PI
    var magic = Math.sin(radlat)
    magic = 1 - ee * magic * magic
    var sqrtmagic = Math.sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
    dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
    const mglat = lat + dlat
    const mglng = lng + dlng
    return [lng * 2 - mglng, lat * 2 - mglat]
  }
}
 
/**
 * 百度坐标系转wgs84坐标系
 * @param {*} lng 
 * @param {*} lat 
 */
export function bd09towgs84 (lng, lat) {
  // 百度坐标系先转为火星坐标系
  const gcj02 = bd09togcj02(lng, lat)
  // 火星坐标系转wgs84坐标系
  const result = gcj02towgs84(gcj02[0], gcj02[1])
  return result
}
 
/**
 * wgs84坐标系转百度坐标系
 * @param {*} lng 
 * @param {*} lat 
 */
export function wgs84tobd09 (lng, lat) {
  // wgs84先转为火星坐标系
  const gcj02 = wgs84togcj02(lng, lat)
  // 火星坐标系转百度坐标系
  const result = gcj02tobd09(gcj02[0], gcj02[1])
  return result
}
 
/**
 * 经度转换
 * @param { Number } lng 
 * @param { Number } lat 
 */
function transformlat (lng, lat) {
  var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng))
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0
  ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0
  return ret
}
 
/**
 * 纬度转换
 * @param { Number } lng 
 * @param { Number } lat 
 */
function transformlng (lng, lat) {
  var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng))
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0
  ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0
  return ret
}
 
/**
 * 判断是否在国内，不在国内则不做偏移
 * @param {*} lng 
 * @param {*} lat 
 */
function outOfChina (lng, lat) {
  return (lng < 72.004 || lng > 137.8347) || ((lat < 0.8293 || lat > 55.8271) || false)
}
//-------End----------
export default class Gps {
    //构造函数
    constructor(obj = {}) {
        let { longitude, latitude } = obj;
        if (longitude === undefined || latitude === undefined) {
            return console.error("经纬度参数不能为空!");
        }
        this.PI = 3.14159265358979324;
        return this.getCoordinate(longitude, latitude);
    };
    //纬度转换
    transformLatitude(x, y) {
        let num = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        num += (20.0 * Math.sin(6.0 * x * this.PI) + 20.0 * Math.sin(2.0 * x * this.PI)) * 2.0 / 3.0;
        num += (20.0 * Math.sin(y * this.PI) + 40.0 * Math.sin(y / 3.0 * this.PI)) * 2.0 / 3.0;
        num += (160.0 * Math.sin(y / 12.0 * this.PI) + 320 * Math.sin(y * this.PI / 30.0)) * 2.0 / 3.0;
        return num;
    };
    //经度转换
    transformLongitude(x, y) {
        let num = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        num += (20.0 * Math.sin(6.0 * x * this.PI) + 20.0 * Math.sin(2.0 * x * this.PI)) * 2.0 / 3.0;
        num += (20.0 * Math.sin(x * this.PI) + 40.0 * Math.sin(x / 3.0 * this.PI)) * 2.0 / 3.0;
        num += (150.0 * Math.sin(x / 12.0 * this.PI) + 300.0 * Math.sin(x / 30.0 * this.PI)) * 2.0 / 3.0;
        return num;
    };
    // 坐标转换
    calculation(longitude, latitude) {
        let a = 6378245.0; // 卫星椭球坐标投影到平面地图坐标系的投影因子。
        let ee = 0.00669342162296594323; // 椭球的偏心率。
        let lat = this.transformLatitude(longitude - 105.0, latitude - 35.0);
        let lng = this.transformLongitude(longitude - 105.0, latitude - 35.0);
        let radLat = latitude / 180.0 * this.PI;
        let magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        let sqrtMagic = Math.sqrt(magic);
        lat = (lat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * this.PI);
        lng = (lng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * this.PI);
        return {
            'longitude': lng,
            'latitude': lat,
        };
    };
    // GPS坐标 转 高德坐标
    getCoordinate(longitude, latitude) {
        longitude = Number(longitude);
        latitude = Number(latitude);
        let obj = this.calculation(longitude, latitude);
        return [
            longitude + obj.longitude,
            latitude + obj.latitude,
        ];
    };
}

export function createGps(obj) {
    return new Gps(obj)
}

// 获取两点之间的中点经纬度
export function calculateMidpoint(lat1, lon1, lat2, lon2) {
    // 将经纬度转换为弧度
    var lat1Rad = lat1 * Math.PI / 180;
    var lon1Rad = lon1 * Math.PI / 180;
    var lat2Rad = lat2 * Math.PI / 180;
    var lon2Rad = lon2 * Math.PI / 180;

    // 计算中点经纬度
    var dLon = lon2Rad - lon1Rad;
    var Bx = Math.cos(lat2Rad) * Math.cos(dLon);
    var By = Math.cos(lat2Rad) * Math.sin(dLon);
    var lat3Rad = Math.atan2(Math.sin(lat1Rad) + Math.sin(lat2Rad), Math.sqrt((Math.cos(lat1Rad) + Bx) * (Math.cos(lat1Rad) + Bx) + By * By));
    var lon3Rad = lon1Rad + Math.atan2(By, Math.cos(lat1Rad) + Bx);

    // 将中点经纬度转换为度
    var lat3 = lat3Rad * 180 / Math.PI;
    var lon3 = lon3Rad * 180 / Math.PI;

    return [lat3, lon3];
}

// 通过指定半径画圆，计算边界所有经纬度
export function computedCircle(lnglat, radius) {
    // lnglat = [104.308173, 30.846514]
    // radius = 5000
    let r = 6371000.79
    let phase = 2 * Math.PI / 360
    let point = []
    for (let i = 0; i < 360; i++) {
        let dx = radius * Math.cos(i * phase)
        let dy = radius * Math.sin(i * phase)

        let lng = dx / (r * Math.cos(lnglat[1] * Math.PI / 180) * Math.PI / 180)
        let lat = dy / (r * Math.PI / 180)
        let newLng = lnglat[0] + lng
        // point.push([Number(newLng.toFixed(4)), Number((lnglat[1] + lat).toFixed(4))])
        point.push({
            lat: Number((lnglat[1] + lat).toFixed(6)),
            lng: Number(newLng.toFixed(6))
        })
    }
    return point
}

// t：半径
// e：中心点经纬度坐标[110,40]
// i： 圆上点的个数，默认15个，建议73个
export function countCircle(t, e, i) {
    for (
        var r = t / 6378137,
        n = [e[0], e[1]],
        o = [numberToRadius(n[1]), numberToRadius(n[0])],
        s = ((i = i || 15), []),
        a = 0;
        a < i;
        a++
    ) {
        var u = (2 * Math.PI * a) / i;
        var h = Math.asin(
            Math.sin(o[0]) * Math.cos(r) +
            Math.cos(o[0]) * Math.sin(r) * Math.cos(u)
        );
        var c =
            o[1] +
            Math.atan2(
                Math.sin(u) * Math.sin(r) * Math.cos(o[0]),
                Math.cos(r) - Math.sin(o[0]) * Math.sin(h)
            );
        s.push({ lng: numberToDegree(c), lat: numberToDegree(h) });
    }
    s.push(s[0])
    return s;
}
function numberToRadius(t) {
    return (t * Math.PI) / 180;
}
function numberToDegree(t) {
    return (180 * t) / Math.PI;
}

// 判断一个经纬度点，是否存在于围栏范围内
export function  isPointInPolygon(pts,point) {
    //pts [{lat:xxx,lng:xxx},{lat:xxx,lng:xxx}]
    //point {lat:xxx,lng:xxx}
    var N = pts.length;  
    var boundOrVertex = true; //如果点位于多边形的顶点或边上，也算做点在多边形内，直接返回true
    var intersectCount = 0;//cross points count of x
    var precision = 2e-10; //浮点类型计算时候与0比较时候的容差
    var p1, p2;//neighbour bound vertices
    var p = point; 
    p1 = pts[0];//left vertex
    for(var i = 1; i <= N; ++i){//check all rays
        if((p.lat==p1.lat)&&(p.lng==p1.lng)){
            return boundOrVertex;//p is an vertex
        }
        p2 = pts[i % N];//right vertex
        if(p.lat < Math.min(p1.lat, p2.lat) || p.lat > Math.max(p1.lat, p2.lat)){
            //ray is outside of our interests
            p1 = p2;
            continue;//next ray left point
        }
        if(p.lat > Math.min(p1.lat, p2.lat) && p.lat < Math.max(p1.lat, p2.lat)){
            //ray is crossing over by the algorithm (common part of)
            if(p.lng <= Math.max(p1.lng, p2.lng)){
                //x is before of ray
                if(p1.lat == p2.lat && p.lng >= Math.min(p1.lng, p2.lng)){
                    //overlies on a horizontal ray
                    return boundOrVertex;
                }
                if(p1.lng == p2.lng){//ray is vertical
                    if(p1.lng == p.lng){//overlies on a vertical ray
                        return boundOrVertex;
                    }else{//before ray
                        ++intersectCount;
                    }
                }else{//cross point on the left side
                        var xinters = (p.lat - p1.lat) * (p2.lng - p1.lng) / (p2.lat - p1.lat) + p1.lng;//cross point of lng
                        if(Math.abs(p.lng - xinters) < precision){//overlies on a ray
                            return boundOrVertex;
                        }
                        if(p.lng < xinters){//before ray
                        ++intersectCount;
                        }
                }
            }
        }else{
            //special case when ray is crossing through the vertex
            if(p.lat == p2.lat && p.lng <= p2.lng){
                //p crossing over p2
                var p3 = pts[(i+1) % N]; //next vertex
                if(p.lat >= Math.min(p1.lat, p3.lat) && p.lat <= Math.max(p1.lat, p3.lat)){
                    //p.lat lies between p1.lat & p3.lat
                    ++intersectCount;
                }else{
                    intersectCount += 2;
                }
            }
        }
        p1 = p2;//next ray left point
    }
    if(intersectCount % 2 == 0){//偶数在多边形外
        return false;
    } else { //奇数在多边形内
        return true;
    }
}

// 有经纬度点 A 和点 B，以及一个距离，求点 A 沿着 AB 方向移动指定距离后的新点坐标, distance单位为公里
export function movePoint(latA, lonA, latB, lonB, distance) {
    const totalDistance = calculateDistance(latA, lonA, latB, lonB);
    const bearing = calculateBearing(latA, lonA, latB, lonB);
    const newDistance = totalDistance - distance;
 
    const lat = Number(latA) + getFullNum((newDistance / 6371000) * (Math.cos(deg2rad(latB)) - Math.cos(deg2rad(latA))));
    const lon = Number(lonA) + getFullNum((newDistance / 6371000) * Math.cos(deg2rad(latA)) * Math.sin(deg2rad(lonB) - deg2rad(lonA)));

    return {
        lat: lat,
        lon: lon
    };
}

// 将科学计数法转为普通小数
function getFullNum(num){
    //处理非数字
    if(isNaN(num)){return num};
    //处理不需要转换的数字
    var str = ''+num;
    if(!/e/i.test(str)){return num;};
    return Number((num).toFixed(18).replace(/\.?0+$/, ""));
}

// 计算两个经纬度之间的距离，单位为米
export function calculateDistance(lat1, lon1, lat2, lon2) {
    const radLat1 = deg2rad(lat1);
    const radLat2 = deg2rad(lat2);
    const radLon1 = deg2rad(lon1);
    const radLon2 = deg2rad(lon2);
    const a = radLat1 - radLat2;
    const b = radLon1 - radLon2;
    const distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b/2), 2)));
    return distance * 6371000; // 返回单位为米
}
 
function deg2rad(deg) {
    return deg * (Math.PI/180);
}
 
// B 点相对于 A 点的角度
function calculateBearing(lat1, lon1, lat2, lon2) {
    const radLat1 = deg2rad(lat1);
    const radLat2 = deg2rad(lat2);
    const radLon1 = deg2rad(lon1);
    const radLon2 = deg2rad(lon2);
    const y = Math.sin(radLon2 - radLon1) * Math.cos(radLat2);
    const x = Math.cos(radLat1) * Math.sin(radLat2) - Math.sin(radLat1) * Math.cos(radLat2) * Math.cos(radLon2 - radLon1);
    const bearing = rad2deg(Math.atan2(y, x));
    return (bearing + 360) % 360;
}
 
function rad2deg(rad) {
    return rad / (Math.PI/180)
}

// 计算出 B 点相对于 A 点的角度
export function getBearing(lat1, lon1, lat2, lon2) {
    const phi1 = (lat1 * Math.PI) / 180;
    const phi2 = (lat2 * Math.PI) / 180;
    const lambda1 = (lon1 * Math.PI) / 180;
    const lambda2 = (lon2 * Math.PI) / 180;

    const y = Math.sin(lambda2 - lambda1) * Math.cos(phi2);
    const x = Math.cos(phi1) * Math.sin(phi2) -
              Math.sin(phi1) * Math.cos(phi2) * Math.cos(lambda2 - lambda1);

    let bearing = Math.atan2(y, x);
    bearing = (bearing * 180) / Math.PI;
    if (bearing < 0) {
        bearing += 360;
    }

    return bearing;
}

// 计算按照相对角度和指定距离延伸的终点
// A点的经纬度
const latitudeA = 30.0;
const longitudeA = 120.0;

// 假设朝着正东方向移动（方位角为90度，可根据实际B点相对A点的方向确定合适的方位角）
const bearing = 90;
// 假设要移动的距离（单位：千米，这里只是示例值，可根据实际需求修改）
const distanceToMove = 10;

// 地球半径（单位：千米），用于计算
const earthRadius = 6371;

// 将角度转换为弧度的函数
function toRadians(angle) {
    return angle * (Math.PI / 180);
}

// 计算新经纬度的函数
export function calculateNewLatLng(latitude, longitude, bearing, distance) {
    const lat1 = toRadians(latitude);
    const lon1 = toRadians(longitude);
    const angularDistance = distance / earthRadius;
    const bearingInRadians = toRadians(bearing);

    const lat2 = Math.asin(
        Math.sin(lat1) * Math.cos(angularDistance) +
        Math.cos(lat1) * Math.sin(angularDistance) * Math.cos(bearingInRadians)
    );

    const lon2 = lon1 + Math.atan2(
        Math.sin(bearingInRadians) * Math.sin(angularDistance) * Math.cos(lat1),
        Math.cos(angularDistance) - Math.sin(lat1) * Math.sin(lat2)
    );

    return {
        lat: lat2 * (180 / Math.PI),
        lng: lon2 * (180 / Math.PI)
    };
}

// const newLatLng = calculateNewLatLng(latitudeA, longitudeA, bearing, distanceToMove);