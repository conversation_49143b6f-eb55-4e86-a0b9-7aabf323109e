/**
 * Geometry 数据压缩/解压缩工具类（前端）
 * 
 * <AUTHOR>
 */

import pako from 'pako'; // 需要安装 pako 库：npm install pako

/**
 * 压缩标识前缀
 */
const COMPRESSED_PREFIX = 'GZIP_BASE64:';

/**
 * 解压缩字符串数据
 * 
 * @param {string} compressedData 压缩后的字符串数据
 * @returns {string} 解压缩后的原始字符串
 */
export function decompressGeometry(compressedData) {
  if (!compressedData || !compressedData.startsWith(COMPRESSED_PREFIX)) {
    return compressedData; // 未压缩的数据直接返回
  }

  try {
    // 移除压缩标识前缀
    const base64Data = compressedData.substring(COMPRESSED_PREFIX.length);
    
    // 将 Base64 字符串转换为字节数组
    const compressedBytes = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));
    
    // 使用 pako 解压缩
    const decompressedBytes = pako.ungzip(compressedBytes);
    
    // 将解压缩后的字节数组转换为字符串
    const decoder = new TextDecoder('utf-8');
    return decoder.decode(decompressedBytes);
    
  } catch (error) {
    console.error('Failed to decompress geometry data:', error);
    return compressedData; // 解压缩失败时返回原数据
  }
}

/**
 * 压缩字符串数据（前端一般不需要，但提供完整性）
 * 
 * @param {string} data 原始字符串数据
 * @returns {string} 压缩后的字符串
 */
export function compressGeometry(data) {
  if (!data || data.length < 10000) {
    return data; // 小数据不压缩
  }

  try {
    // 将字符串转换为字节数组
    const encoder = new TextEncoder();
    const inputBytes = encoder.encode(data);
    
    // 使用 pako 压缩
    const compressedBytes = pako.gzip(inputBytes);
    
    // 将压缩后的字节数组转换为 Base64 字符串
    const base64Data = btoa(String.fromCharCode(...compressedBytes));
    
    // 添加压缩标识前缀
    const result = COMPRESSED_PREFIX + base64Data;
    
    // 如果压缩后反而更大，则返回原数据
    if (result.length >= data.length) {
      return data;
    }
    
    return result;
    
  } catch (error) {
    console.error('Failed to compress geometry data:', error);
    return data; // 压缩失败时返回原数据
  }
}

/**
 * 检查字符串是否已压缩
 * 
 * @param {string} data 字符串数据
 * @returns {boolean} 是否已压缩
 */
export function isCompressed(data) {
  return data && data.startsWith(COMPRESSED_PREFIX);
}

/**
 * 批量解压缩对象中的 geometry 字段
 * 
 * @param {Object} obj 包含 geometry 字段的对象
 * @returns {Object} 解压缩后的对象
 */
export function decompressObjectGeometry(obj) {
  if (!obj) return obj;
  
  const result = { ...obj };
  
  // 解压缩所有 geometry 字段
  if (result.provinceGeometry) {
    result.provinceGeometry = decompressGeometry(result.provinceGeometry);
  }
  if (result.cityGeometry) {
    result.cityGeometry = decompressGeometry(result.cityGeometry);
  }
  if (result.districtGeometry) {
    result.districtGeometry = decompressGeometry(result.districtGeometry);
  }
  
  return result;
}

/**
 * 批量解压缩数组中所有对象的 geometry 字段
 * 
 * @param {Array} array 包含 geometry 字段的对象数组
 * @returns {Array} 解压缩后的对象数组
 */
export function decompressArrayGeometry(array) {
  if (!Array.isArray(array)) return array;
  
  return array.map(item => decompressObjectGeometry(item));
}

/**
 * 自动处理 API 响应中的压缩数据
 * 
 * @param {Object} response API 响应对象
 * @returns {Object} 处理后的响应对象
 */
export function processApiResponse(response) {
  if (!response || !response.data) return response;
  
  const result = { ...response };
  
  if (Array.isArray(result.data)) {
    // 处理数组数据
    result.data = decompressArrayGeometry(result.data);
  } else if (typeof result.data === 'object') {
    // 处理单个对象数据
    result.data = decompressObjectGeometry(result.data);
  }
  
  return result;
}

// 默认导出
export default {
  decompressGeometry,
  compressGeometry,
  isCompressed,
  decompressObjectGeometry,
  decompressArrayGeometry,
  processApiResponse
};
