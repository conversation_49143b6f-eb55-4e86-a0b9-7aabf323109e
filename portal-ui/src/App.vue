<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<!-- 百度地图 -->
<script type="text/javascript" src="http://api.map.baidu.com/api?v=3.0&ak=BPiUxXNVPT7NRftvyjwfPBnYLX1B94eT&callback=init"></script>
<script>
import ThemePicker from "@/components/ThemePicker";

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
