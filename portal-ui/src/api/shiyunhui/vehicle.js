import request from '@/utils/request'
import { processApiResponse } from '@/utils/geometryCompression'

/**
 * 获取车辆上线统计数据
 * @param {Object} query 查询参数
 * @returns {Promise} 请求结果
 */
export function getVehicleOnlineStats(query) {
  return request({
    url: '/shiyunhui/vehicle/online_stats',
    method: 'get',
    params: query
  }).then(response => {
    // 自动解压缩 geometry 数据
    return processApiResponse(response);
  });
}

/**
 * 获取单车每日行驶里程
 * @param {Object} query 查询参数
 * @returns {Promise} 请求结果
 */
export function getVehicleDriveOdometer(query) {
  return request({
    url: '/shiyunhui/vehicle/drive_odometer',
    method: 'get',
    params: query
  });
}

/**
 * 导出单车每日行驶里程数据
 * @param {Object} query 查询参数
 * @returns {Promise} 请求结果
 */
export function exportVehicleDriveOdometer(query) {
  return request({
    url: '/shiyunhui/vehicle/drive_odometer/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  });
}

/**
 * 获取热点时段车辆数
 * @param {Object} query 查询参数
 * @returns {Promise} 请求结果
 */
export function getVehicleHotHour(query) {
  return request({
    url: '/shiyunhui/vehicle/hot_hour',
    method: 'get',
    params: query
  });
}

/**
 * 获取热点区域车辆数
 * @param {Object} query 查询参数
 * @returns {Promise} 请求结果
 */
export function getVehicleHotArea(query) {
  return request({
    url: '/shiyunhui/vehicle/hot_area',
    method: 'get',
    params: query
  });
}

/**
 * 获取总行驶里程和超排车辆数统计
 * @param {Object} query 查询参数
 * @returns {Promise} 请求结果
 */
export function getTotalOdometer(query) {
  return request({
    url: '/shiyunhui/vehicle/total_odometer',
    method: 'get',
    params: query
  });
}

/**
 * 获取上线车辆数最多的城市
 * @param {Object} query 查询参数
 * @returns {Promise} 请求结果
 */
export function getHotAreaStats(query) {
  return request({
    url: '/shiyunhui/vehicle/hot_area_stats',
    method: 'get',
    params: query
  });
}

/**
 * 获取维度值列表
 * @param {Object} query 查询参数
 * @returns {Promise} 请求结果
 */
export function getDimensionValues(query) {
  return request({
    url: '/shiyunhui/vehicle/dimension_values',
    method: 'get',
    params: query
  });
}
