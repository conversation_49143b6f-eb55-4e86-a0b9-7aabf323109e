import request from '@/utils/request'
import {
  parseStrEmpty,
  paramsStr,
} from "@/utils/ruoyi";


/**
 * 
 * @param {province} 传入行政区代码 
 * @returns 返回指定行政区所有道路名称
 * 
 */
export function getRoadName(data) {
  return request({
    url: '/roadNet/road_name' + paramsStr(data),
    method: 'get',
    data: data
  })
}

/**
 * 
 * @param {road_name} 传入路名 
 * @returns 返回指定道路所有经纬度信息
 * 
 */
export function getRoadData(data) {
  return request({
    url: '/roadNet/road_data' + paramsStr(data),
    method: 'get',
    data: data
  })
}

// 成都市各区县行政范围
export function getChengduDistrict(data) {
  return request({
    url: '/area/chengdu-district',
    method: 'post',
    data: data
  })
}

//  查询热力图数据
export function  getHotMapData(data) {
  return request({
    url: '/hot-map-data',
    method: 'post',
    data: data
  })
}

//  查询单条轨迹数据
export function  getTrajectoryData(data) {
  return request({
    url: '/trajectory',
    method: 'post',
    data: data
  })
}

//  查询marker标记点数据
export function  getMarkerMapData(data) {
  return request({
    url: '/marker-map-data',
    method: 'post',
    data: data
  })
}

// 查询电子围栏重点区域
export function getFenceMapData(data) {
  return request({
    url: '/fence-map-data',
    method: 'post',
    data: data
  })
}

// 查询中国各省行政边界
export function getChinaArea(data) {
  return request({
    url: '/dashboard/map_data' + paramsStr(data),
    method: 'get',
    data: data
  })
}

// 查询行政区域
export function getBoundariesMapData(data) {
  return request({
    url: '/boundaries',
    method: 'post',
    data: data
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}

// 点聚合
export function clusterer(data) {
  return request({
    url: '/clusterer',
    method: 'post',
    data: data
  })
}