import request from '@/utils/request'
import {
  parseStrEmpty,
  paramsStr,
} from "@/utils/ruoyi";

//  联网（在线）车辆数
export function  getConnectedCars(data) {
  return request({
    url: '/dashboard/connected_cars',
    method: 'get',
    data: data
  })
}

//  当日上线车辆数+里程
export function  getCurDayMetrics(data) {
  return request({
    url: '/dashboard/cur_day_metrics' + paramsStr(data),
    method: 'get',
    data: data
  })
}

//  省上线车辆top5
export function  getProvinceTop(data) {
  return request({
    url: '/dashboard/province_top' + paramsStr(data),
    method: 'get',
    data: data
  })
}
// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}