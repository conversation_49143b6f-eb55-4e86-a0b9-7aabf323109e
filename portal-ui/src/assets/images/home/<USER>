<?xml version="1.0" encoding="UTF-8"?>
<svg width="279px" height="24px" viewBox="0 0 279 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>矩形</title>
    <defs>
        <polygon id="path-1" points="8 0 279 -8.52651283e-14 279 24 0 24 0 8.5"></polygon>
        <filter x="-3.6%" y="-41.7%" width="107.2%" height="183.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="10" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0941176471   0 0 0 0 0.42745098   0 0 0 0 0.650980392  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="数据大屏-排版1" transform="translate(-1149.000000, -24.000000)">
            <g id="矩形" transform="translate(1149.000000, 24.000000)">
                <use fill-opacity="0.5" fill="#0D1838" fill-rule="evenodd" xlink:href="#path-1"></use>
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
            </g>
        </g>
    </g>
</svg>