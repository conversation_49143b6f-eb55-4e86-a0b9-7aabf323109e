<?xml version="1.0" encoding="UTF-8"?>
<svg width="246px" height="198px" viewBox="0 0 246 198" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>数据质量分析</title>
    <defs>
        <polygon id="path-1" points="15.9536424 0 238 0 246 8 246 176 246 198 8 198 8.73967565e-13 190 -2.13370988e-16 15.9536424"></polygon>
        <filter x="-2.0%" y="-2.5%" width="104.1%" height="105.1%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0941176471   0 0 0 0 0.42745098   0 0 0 0 0.650980392  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <polygon id="path-3" points="15.9536424 0 238 0 246 8 246 176 246 198 8 198 6.55475674e-13 190 0 15.9536424"></polygon>
        <polygon id="path-5" points="15.9536424 0 238 0 246 8.2962963 246 24.4444444 246 40 8 40 0 40 0 16.544518"></polygon>
        <filter x="-6.1%" y="-37.5%" width="112.2%" height="175.0%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="15" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0941176471   0 0 0 0 0.42745098   0 0 0 0 0.650980392  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="元素" transform="translate(-521.000000, -220.000000)">
            <g id="编组-6" transform="translate(521.000000, 220.000000)">
                <g id="数据质量分析">
                    <g id="矩形">
                        <use fill-opacity="0.2" fill="#0D1838" fill-rule="evenodd" xlink:href="#path-1"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        <text x="55%" y="25" text-anchor="middle" font-family="Arial" font-size="15" font-weight="bold" fill="#fff">每日总行驶里程(*1000KM)</text>
                    </g>
                    <g id="网格-数据质量分析">
                        <mask id="mask-4" fill="white">
                            <use xlink:href="#path-3"></use>
                        </mask>
                        <use id="蒙版" fill-opacity="0.2" fill="#0D1838" xlink:href="#path-3"></use>
                        <g id="网格" mask="url(#mask-4)" stroke="#02285A" stroke-opacity="0.3" stroke-width="0.5">
                            <g transform="translate(0.000000, -0.000000)">
                                <rect id="矩形备份-25" x="0.25" y="190.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-24" x="0.25" y="180.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-23" x="0.25" y="170.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-22" x="0.25" y="160.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-21" x="0.25" y="150.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-20" x="0.25" y="140.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-19" x="0.25" y="130.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-18" x="0.25" y="120.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-17" x="0.25" y="110.25" width="245.5" height="9.5"></rect>
                                <path d="M387.75,145.25 L387.75,154.75 L88.25,154.75 L88.25,145.25 L387.75,145.25 Z" id="矩形备份-40" transform="translate(238.000000, 150.000000) rotate(-90.000000) translate(-238.000000, -150.000000) "></path>
                                <path d="M377.75,145.25 L377.75,154.75 L78.25,154.75 L78.25,145.25 L377.75,145.25 Z" id="矩形备份-39" transform="translate(228.000000, 150.000000) rotate(-90.000000) translate(-228.000000, -150.000000) "></path>
                                <path d="M367.75,145.25 L367.75,154.75 L68.25,154.75 L68.25,145.25 L367.75,145.25 Z" id="矩形备份-38" transform="translate(218.000000, 150.000000) rotate(-90.000000) translate(-218.000000, -150.000000) "></path>
                                <path d="M357.75,145.25 L357.75,154.75 L58.25,154.75 L58.25,145.25 L357.75,145.25 Z" id="矩形备份-37" transform="translate(208.000000, 150.000000) rotate(-90.000000) translate(-208.000000, -150.000000) "></path>
                                <path d="M347.75,145.25 L347.75,154.75 L48.25,154.75 L48.25,145.25 L347.75,145.25 Z" id="矩形备份-36" transform="translate(198.000000, 150.000000) rotate(-90.000000) translate(-198.000000, -150.000000) "></path>
                                <path d="M337.75,145.25 L337.75,154.75 L38.25,154.75 L38.25,145.25 L337.75,145.25 Z" id="矩形备份-35" transform="translate(188.000000, 150.000000) rotate(-90.000000) translate(-188.000000, -150.000000) "></path>
                                <path d="M327.75,145.25 L327.75,154.75 L28.25,154.75 L28.25,145.25 L327.75,145.25 Z" id="矩形备份-34" transform="translate(178.000000, 150.000000) rotate(-90.000000) translate(-178.000000, -150.000000) "></path>
                                <path d="M317.75,145.25 L317.75,154.75 L18.25,154.75 L18.25,145.25 L317.75,145.25 Z" id="矩形备份-33" transform="translate(168.000000, 150.000000) rotate(-90.000000) translate(-168.000000, -150.000000) "></path>
                                <path d="M307.75,145.25 L307.75,154.75 L8.25,154.75 L8.25,145.25 L307.75,145.25 Z" id="矩形备份-32" transform="translate(158.000000, 150.000000) rotate(-90.000000) translate(-158.000000, -150.000000) "></path>
                                <path d="M297.75,145.25 L297.75,154.75 L-1.75,154.75 L-1.75,145.25 L297.75,145.25 Z" id="矩形备份-31" transform="translate(148.000000, 150.000000) rotate(-90.000000) translate(-148.000000, -150.000000) "></path>
                                <path d="M287.75,145.25 L287.75,154.75 L-11.75,154.75 L-11.75,145.25 L287.75,145.25 Z" id="矩形备份-30" transform="translate(138.000000, 150.000000) rotate(-90.000000) translate(-138.000000, -150.000000) "></path>
                                <path d="M277.75,145.25 L277.75,154.75 L-21.75,154.75 L-21.75,145.25 L277.75,145.25 Z" id="矩形备份-29" transform="translate(128.000000, 150.000000) rotate(-90.000000) translate(-128.000000, -150.000000) "></path>
                                <path d="M267.75,145.25 L267.75,154.75 L-31.75,154.75 L-31.75,145.25 L267.75,145.25 Z" id="矩形备份-28" transform="translate(118.000000, 150.000000) rotate(-90.000000) translate(-118.000000, -150.000000) "></path>
                                <path d="M257.75,145.25 L257.75,154.75 L-41.75,154.75 L-41.75,145.25 L257.75,145.25 Z" id="矩形备份-27" transform="translate(108.000000, 150.000000) rotate(-90.000000) translate(-108.000000, -150.000000) "></path>
                                <path d="M247.75,145.25 L247.75,154.75 L-51.75,154.75 L-51.75,145.25 L247.75,145.25 Z" id="矩形备份-26" transform="translate(98.000000, 150.000000) rotate(-90.000000) translate(-98.000000, -150.000000) "></path>
                                <path d="M237.75,145.25 L237.75,154.75 L-61.75,154.75 L-61.75,145.25 L237.75,145.25 Z" id="矩形备份-25" transform="translate(88.000000, 150.000000) rotate(-90.000000) translate(-88.000000, -150.000000) "></path>
                                <path d="M227.75,145.25 L227.75,154.75 L-71.75,154.75 L-71.75,145.25 L227.75,145.25 Z" id="矩形备份-24" transform="translate(78.000000, 150.000000) rotate(-90.000000) translate(-78.000000, -150.000000) "></path>
                                <path d="M217.75,145.25 L217.75,154.75 L-81.75,154.75 L-81.75,145.25 L217.75,145.25 Z" id="矩形备份-23" transform="translate(68.000000, 150.000000) rotate(-90.000000) translate(-68.000000, -150.000000) "></path>
                                <path d="M207.75,145.25 L207.75,154.75 L-91.75,154.75 L-91.75,145.25 L207.75,145.25 Z" id="矩形备份-22" transform="translate(58.000000, 150.000000) rotate(-90.000000) translate(-58.000000, -150.000000) "></path>
                                <path d="M197.75,145.25 L197.75,154.75 L-101.75,154.75 L-101.75,145.25 L197.75,145.25 Z" id="矩形备份-21" transform="translate(48.000000, 150.000000) rotate(-90.000000) translate(-48.000000, -150.000000) "></path>
                                <path d="M187.75,145.25 L187.75,154.75 L-111.75,154.75 L-111.75,145.25 L187.75,145.25 Z" id="矩形备份-20" transform="translate(38.000000, 150.000000) rotate(-90.000000) translate(-38.000000, -150.000000) "></path>
                                <path d="M177.75,145.25 L177.75,154.75 L-121.75,154.75 L-121.75,145.25 L177.75,145.25 Z" id="矩形备份-19" transform="translate(28.000000, 150.000000) rotate(-90.000000) translate(-28.000000, -150.000000) "></path>
                                <path d="M167.75,145.25 L167.75,154.75 L-131.75,154.75 L-131.75,145.25 L167.75,145.25 Z" id="矩形备份-18" transform="translate(18.000000, 150.000000) rotate(-90.000000) translate(-18.000000, -150.000000) "></path>
                                <path d="M157.75,145.25 L157.75,154.75 L-141.75,154.75 L-141.75,145.25 L157.75,145.25 Z" id="矩形备份-17" transform="translate(8.000000, 150.000000) rotate(-90.000000) translate(-8.000000, -150.000000) "></path>
                                <rect id="矩形备份-16" x="0.25" y="100.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-15" x="0.25" y="90.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-14" x="0.25" y="80.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-13" x="0.25" y="70.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-12" x="0.25" y="60.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-11" x="0.25" y="50.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-10" x="0.25" y="40.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-9" x="0.25" y="30.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形备份-8" x="0.25" y="20.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形" x="0.25" y="10.25" width="245.5" height="9.5"></rect>
                                <rect id="矩形" x="0.25" y="0.25" width="245.5" height="9.5"></rect>
                            </g>
                        </g>
                    </g>
                    <g id="矩形">
                        <use fill-opacity="0.2" fill="#0D1838" fill-rule="evenodd" xlink:href="#path-5"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    </g>
                </g>
                <g id="title-icon" transform="translate(24.000000, 11.000000)">
                    <polygon id="路径" fill="#EC8D00" points="1.70530257e-13 16 1.70530257e-13 11.6666667 3.66682353 8.00047059 1.13686838e-13 4.33333333 1.98951966e-13 0 8 8"></polygon>
                    <polygon id="路径" fill="#FFB128" points="7 16 7 11.6666667 10.6668235 8.00047059 7 4.33333333 7 0 15 8"></polygon>
                    <polygon id="路径" fill="#F4D26E" points="14 16 14 11.6666667 17.6668235 8.00047059 14 4.33333333 14 0 22 8"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>