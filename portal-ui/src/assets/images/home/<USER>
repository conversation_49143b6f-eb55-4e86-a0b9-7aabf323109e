<?xml version="1.0" encoding="UTF-8"?>
<svg width="516px" height="63px" viewBox="0 0 516 63" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>顶部-绿色交通系统框</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#186DA6" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#186DA6" stop-opacity="0.503742351" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#7AA7EA" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#7AA7EA" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="数据大屏-排版1" transform="translate(-462.000000, 0.000000)">
            <g id="顶部-绿色交通系统框" transform="translate(462.000000, 0.000000)">
                <path d="M513.474938,1 L449.60397,61 L66.3960299,61 L2.52506212,1 L513.474938,1 Z" id="矩形" stroke="url(#linearGradient-2)" stroke-width="2" fill="url(#linearGradient-1)"></path>
                <polyline id="路径-7" stroke="#5FDEFF" stroke-width="2" stroke-linejoin="round" points="440 61.5 450 61.5 456 56"></polyline>
                <polyline id="路径-7" stroke="#5FDEFF" stroke-width="2" stroke-linejoin="round" transform="translate(68.000000, 58.750000) scale(-1, 1) translate(-68.000000, -58.750000) " points="60 61.5 70 61.5 76 56"></polyline>
                <rect id="矩形" fill="#5FDEFF" x="208" y="59.5" width="100" height="3"></rect>
                <text x="50%" y="50" text-anchor="middle" font-family="Arial" font-size="30" font-weight="bold" fill="#fff">移动数据源在线监控平台</text>
            </g>
        </g>
    </g>
</svg>