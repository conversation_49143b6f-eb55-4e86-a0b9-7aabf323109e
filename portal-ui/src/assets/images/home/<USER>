<?xml version="1.0" encoding="UTF-8"?>
<svg width="237px" height="29px" viewBox="0 0 237 29" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>title frame</title>
    <defs>
        <linearGradient x1="27.9543443%" y1="39.42%" x2="74.5364204%" y2="60.58%" id="linearGradient-1">
            <stop stop-color="#65B9FD" stop-opacity="0.549114948" offset="0%"></stop>
            <stop stop-color="#0D3964" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-2" points="-9.41954847e-14 0 26 0 50 23 24 23"></polygon>
        <filter x="-9.0%" y="-19.6%" width="118.0%" height="139.1%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.703067835   0 0 0 0 0.821840701   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-8.0%" y="-17.4%" width="116.0%" height="134.8%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.309803922   0 0 0 0 0.592156863  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="27.9543443%" y1="31.6828255%" x2="74.5364204%" y2="68.3171745%" id="linearGradient-5">
            <stop stop-color="#65B9FD" stop-opacity="0.549114948" offset="0%"></stop>
            <stop stop-color="#0D3964" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-6" points="33 0 47 0 71 23 57 23"></polygon>
        <filter x="-11.8%" y="-19.6%" width="123.7%" height="139.1%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.703067835   0 0 0 0 0.821840701   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-10.5%" y="-17.4%" width="121.1%" height="134.8%" filterUnits="objectBoundingBox" id="filter-8">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.309803922   0 0 0 0 0.592156863  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <rect id="path-9" x="24" y="22" width="207" height="1"></rect>
        <filter x="-2.2%" y="-450.0%" width="104.3%" height="1000.0%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.701960784   0 0 0 0 0.823529412   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="数据大屏-排版1" transform="translate(-279.000000, -246.000000)">
            <g id="title-frame" transform="translate(282.000000, 249.000000)">
                <g id="矩形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-2"></use>

                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-2"></use>
                    <path stroke="#62A2E1" stroke-width="1" d="M25.799097,0.5 L48.7556187,22.5 L24.200903,22.5 L1.24438131,0.5 L25.799097,0.5 Z" stroke-linejoin="square"></path>
                </g>
                <g id="矩形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                    <use fill="url(#linearGradient-5)" fill-rule="evenodd" xlink:href="#path-6"></use>

                    <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-6"></use>
                    <path stroke="#62A2E1" stroke-width="1" d="M34.2443813,0.5 L46.799097,0.5 L69.7556187,22.5 L57.200903,22.5 L34.2443813,0.5 Z" stroke-linejoin="square"></path>
                </g>
                <g id="矩形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                    <use fill="#62A2E1" fill-rule="evenodd" xlink:href="#path-9"></use>
                    <text x="70" y="18" font-family="Arial" font-size="15" font-weight="bold" fill="#fff">在线车辆数</text>
                    <!-- <text x="150" y="18" font-family="Arial" font-size="15" font-weight="bold" fill="#FFA500">2,491,473</text> -->
                </g>
            </g>
        </g>
    </g>
</svg>