<?xml version="1.0" encoding="UTF-8"?>
<svg width="884px" height="118px" viewBox="0 0 884 118" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>中央数据框</title>
    <defs>
        <path d="M6.82121026e-13,94 L4,97.3333333 L4,105.999 L12.6666667,106 L16,110 L4,109.999 L4,110 L6.82121026e-13,110 L6.82121026e-13,94 Z" id="path-1"></path>
        <filter x="-37.5%" y="-37.5%" width="175.0%" height="175.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.29836128   0 0 0 0 0.713195974   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M0,0 L4,3.33333333 L4,11.999 L12.6666667,12 L16,16 L4,15.999 L4,16 L0,16 L0,0 Z" id="path-3"></path>
        <filter x="-37.5%" y="-37.5%" width="175.0%" height="175.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.29836128   0 0 0 0 0.713195974   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M860,94 L864,97.3333333 L864,105.999 L872.666667,106 L876,110 L864,109.999 L864,110 L860,110 L860,94 Z" id="path-5"></path>
        <filter x="-37.5%" y="-37.5%" width="175.0%" height="175.0%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.29836128   0 0 0 0 0.713195974   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M860,2.27373675e-13 L864,3.33333333 L864,12 L872.666667,12 L876,16 L860,16 L860,2.27373675e-13 Z" id="path-7"></path>
        <filter x="-37.5%" y="-37.5%" width="175.0%" height="175.0%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.29836128   0 0 0 0 0.713195974   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="21856.3417%" gradientTransform="translate(0.500000,0.500000),scale(0.002347,1.000000),translate(-0.500000,-0.500000)" id="radialGradient-9">
            <stop stop-color="#7AA7EA" offset="0%"></stop>
            <stop stop-color="#5599FF" stop-opacity="0" offset="100%"></stop>
        </radialGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="数据大屏-排版1" transform="translate(-278.000000, -102.000000)">
            <g id="中央数据框" transform="translate(282.000000, 106.000000)">
                <g id="形状结合">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#0A4B77" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
                <g id="形状结合" transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000) ">
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <use fill="#0A4B77" fill-rule="evenodd" xlink:href="#path-3"></use>
                </g>
                <g id="形状结合" transform="translate(868.000000, 102.000000) scale(-1, 1) translate(-868.000000, -102.000000) ">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use fill="#0A4B77" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
                <g id="形状结合" transform="translate(868.000000, 8.000000) scale(-1, 1) rotate(-270.000000) translate(-868.000000, -8.000000) ">
                    <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                    <use fill="#0A4B77" fill-rule="evenodd" xlink:href="#path-7"></use>
                </g>
                <polygon id="路径-2" fill="url(#radialGradient-9)" fill-rule="nonzero" points="864 0 864 2 12 2 12 0"></polygon>
                <polygon id="路径-2" fill="url(#radialGradient-9)" fill-rule="nonzero" points="864 108 864 110 12 110 12 108"></polygon>
<!--                <text x="20" y="35" font-family="Verdana" font-size="16" fill="white" font-weight="bold">总行驶里程（公里）</text>-->
<!--                <text x="180" y="35" font-family="Verdana" font-size="16" fill="white" font-weight="bold">总行驶时间（小时）</text>-->
                <text x="40" y="35" font-family="Verdana" font-size="16" fill="white" font-weight="bold">车辆总数（辆）</text>
                <text x="390" y="35" font-family="Verdana" font-size="16" fill="white" font-weight="bold">当日上线总数（辆）</text>
                <text x="700" y="35" font-family="Verdana" font-size="16" fill="white" font-weight="bold">当日总行驶里程（公里）</text>
                <!-- 值文本 -->
<!--                <text id="mileage" x="40" y="70" font-family="Verdana" font-size="18" fill="orange" font-weight="bold">17,139,400</text>-->
<!--                <text id="time" x="200" y="70" font-family="Verdana" font-size="18" fill="orange" font-weight="bold">327,320</text>-->
                <text id="nox" x="80" y="70" font-family="Verdana" font-size="18" fill="orange" font-weight="bold">3,942,814</text>
                <text id="fuel" x="400" y="70" font-family="Verdana" font-size="18" fill="orange" font-weight="bold">1,374,351</text>
                <text id="urea" x="720" y="70" font-family="Verdana" font-size="18" fill="orange" font-weight="bold">880,582,309.20</text>

                <!-- 动画效果 -->
                <script type="text/ecmascript">
                    <![CDATA[
            // countTo 动画函数
            function countTo(target, start, end, duration) {
                var range = end - start;
                var minTimer = 50;
                var stepTime = Math.abs(Math.floor(duration / range));
                stepTime = Math.max(stepTime, minTimer);
                var startTime = new Date().getTime();
                var endTime = startTime + duration;
                var timer;

                function run() {
                    var now = new Date().getTime();
                    var remaining = Math.max((endTime - now) / duration, 0);
                    var value = Math.round(end - (remaining * range));
                    target.textContent = value;
                    if (value == end) {
                        clearInterval(timer);
                    }
                }

                timer = setInterval(run, stepTime);
                run();
            }

            // 页面加载完毕后，开始动画
            window.onload = function() {
<!--                countTo(document.getElementById('mileage'), 0, 1713940, 2000);-->
<!--                countTo(document.getElementById('time'), 0, 142131231, 2000);-->
                countTo(document.getElementById('nox'), 0, 2124342, 2000);
                countTo(document.getElementById('fuel'), 0, 12345, 2000);
                countTo(document.getElementById('urea'), 0, 67890, 2000);
            };
        ]]>
                </script>
            </g>
        </g>
    </g>
</svg>