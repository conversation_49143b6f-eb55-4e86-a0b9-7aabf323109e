<template>
  <div class="home-co2">
    <!-- 底层地图 -->
    <div class="view-map">
      <my-map
        v-if="isShowHome"
        id="1"
        :isDeep="true"
        :isZoomEnable="true"
        :isResizeEnable="false"
        :isDragEnable="true"
        :isLocation="true"
        :isMunicipalityMask="isMunicipalityMask"
        :defaultCenter="[104.065735, 30.689462]"
        :defaultZoom="4"
        :isPathSimplifier="false"
        :pathSimplifierData="pathSimplifierData"
      ></my-map>
    </div>
    <!-- 标题 -->
    <div class="head">
      <div class="title-box">
        <span class="main-title">成都市碳排放在线监控平台</span>
        <span id="clock" class="today"></span>
      </div>
    </div>
    <!-- 左侧图表 -->
    <div class="view-left">
      <div class="echart-box view-summary no_bg_color">
        <div class="data-sum-col">
          <div class="data-sum-title">车辆总数</div>
          <div class="data-sum-title">(辆)</div>
          <div class="data-sum-value">{{ allCarCount }}</div>
        </div>
        <div class="data-sum-col">
          <div class="data-sum-title">当日上线总数</div>
          <div class="data-sum-title">(辆)</div>
          <div class="data-sum-value">{{ onlineCarSumCount }}</div>
        </div>
        <div class="data-sum-col">
          <div class="data-sum-title">当日总行驶里程</div>
          <div class="data-sum-title">(公里)</div>
          <div class="data-sum-value">{{ oneDayCarSumMileage }}</div>
        </div>
      </div>
      <!-- echart展示项 -->
      <div class="echart-box">
        <echartRace :id="1" titleText="Top5省上线车辆数" titleColor="#FFFFFF" :data="carsNumData" :labels="carsLabels">
        </echartRace>
      </div>
      <div class="echart-box">
        <echart-bar :id="2" barType="valueX" :xAxisData="xAxisData" :seriesData="seriesData" titleText="Top5总油耗"
          titleColor="#FFFFFF" axisLabelColor="#FFFFFF"></echart-bar>
      </div>
    </div>
    <!-- 右侧轮播表格 -->
    <div class="view-right">
      <div class="echart-box" style="overflow-x: hidden;">
        <div class="ecahrt-box-title">NOX高排车辆数</div>
        <!-- 滚动列表 -->
        <div class="echart-box-list" @mouseover="handleStop" @mouseleave="handleRun">
          <div :class="{ anim: animate === true }">
            <div v-for="(item, index) in list" :key="index" class="list-item">
              <div class="list-item-date">{{ '2024-10-28' }}</div>
              <div class="list-item-label">
                <el-tooltip class="item" effect="dark" :content="item.name" placement="left">
                  <span>{{ item.name }}</span>
                </el-tooltip>
              </div>
              <div class="list-item-value">{{ item.cnt }}(辆)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      title="区域交通健康详情"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :before-close="handleClose"
      :destroy-on-close="true"
      width="90%"
    >
      <!-- 地图详情 -->
      <div
        v-loading="loading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        class="dialog-container"
      >
        <!-- 健康 -->
        <div class="dialog-container-title">
          <i class="el-icon-first-aid-kit"></i>
          健康详情
        </div>
        <div class="dialog-container-echarts">
          <div class="dialog-echarts-query">
            <span>按时间查询：</span>
            <el-radio-group v-model="targetQuery.date">
              <el-radio label="24h">近24小时</el-radio>
              <el-radio label="7d">近7天</el-radio>
            </el-radio-group>
          </div>
          <div class="dialog-echarts-query">
            <span>按指标查询：</span>
            <el-radio-group v-model="targetQuery.target">
              <el-radio label="all">当日上线总数(辆)</el-radio>
              <el-radio label="nox">Nox高排车辆数</el-radio>
              <el-radio label="oil">总油耗</el-radio>
            </el-radio-group>
          </div>
          <div class="dialog-echarts-data">
            <div class="echarts-line">
              <echart-line></echart-line>
            </div>
            <div class="echarts-radar">
              <echart-radar
                :id="3"
                titleText="雷达图"
                titleColor="#fff"
              ></echart-radar>
            </div>
          </div>
        </div>
        <!-- 区域 -->
        <div class="dialog-container-title">
          <i class="el-icon-medal-1"></i>
          区域当日上线车辆排名
        </div>
        <div class="dialog-container-table">
          <div class="dialog-table-data">
            <div class="table-container">
              <!-- 表头 -->
              <div class="dialog-table-head">
                <div class="dialog-table-column">排名</div>
                <div class="dialog-table-column">区域</div>
                <div class="dialog-table-column">在线车辆数</div>
                <div class="dialog-table-column">Nox排放量</div>
                <div class="dialog-table-column">碳排放量</div>
              </div>
              <!-- 数据 -->
              <div class="dialog-table-body">
                <div
                  v-for="(item, index) in areaList"
                  :class="selectedId == (index+1) ? 'dialog-table-body-row active' : 'dialog-table-body-row'"
                  :key="index"
                  @click="handleClickRow(index+1, item)"
                >
                <div class="dialog-table-column">{{ item.id || '--' }}</div>
                  <div class="dialog-table-column">{{ item.name || '--' }}</div>
                  <div class="dialog-table-column">{{ item.count || '--' }}</div>
                  <div class="dialog-table-column">{{ item.nox || '--' }}%</div>
                  <div class="dialog-table-column">{{ item.co2 || '--' }}%</div>
                </div>
              </div>
            </div>
            <div class="table-async-map">
              <my-map
                :isShow="isShow"
                id="2"
                :isZoomEnable="true"
                :isResizeEnable="true"
                :isDragEnable="true"
                :isMenu="false"
                :isLocation="false"
                :autoBackCenter="autoBackCenter"
                :autoBackZoom="autoBackZoom"
                :isFence="isFence"
                :fenceData="fenceData"
                :isMaker="isMaker"
                :markerData="markerData"
                :defaultCenter="defaultCenter"
                :defaultZoom="9"
              ></my-map>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>


<script>
// Api
import {
  getChinaArea,
 } from "@/api/maps/map";
// 公共方法
import { qian } from "@/utils/index"
import { mapState } from 'vuex'
// 导入组件
import echartRadar from '@/components/echart/radar/index'
import echartLine from '@/components/echart/line/index'
import echartPie from '@/components/echart/pie/index'
import echartBar from '@/components/echart/bar/index'
import echartRace from '@/components/echart/bar/race'
import echartMap from '@/components/echart/map/index'
import myMap from '@/components/amap/myMap'
import moveTrajectory from '@/components/amap/move'
// mock数据
import provinceMileage from '../../mock/carMileage/province.json' // 省-里程
import cityMileage from '../../mock/carMileage/city.json' // 市-里程
import provinceCount from '../../mock/carCount/province.json' // 省-数量
import cityCount from '../../mock/carCount/city.json' // 市-数量
import gaopaiSheng from '../../mock/gaopai/gaopai_sheng.json' // 省-高排
import gaopaiShi from '../../mock/gaopai/gaopai_shi.json' // 市-高排
import gaopaiQu from '../../mock/gaopai/gaopai_qu.json' // 区-高排
import jinniu from '../../mock/area/jinniu.json'
import jinniu2 from '../../mock/area/jinniu2.json'
import chenghua from '../../mock/area/chenghua.json'
import jingjiang from '../../mock/area/jingjiang.json'
import qingyang from '../../mock/area/qingyang.json'
import wuhou from '../../mock/area/wuhou.json'
import longquanyi from '../../mock/area/longquanyi.json'
import roadPath2 from "@/components/amap/test.json"

const defaultQuery = {
  date: '24h',
  target: 'all'
}
export default {
  name: "Index",
  components: {
    echartRadar,
    echartLine,
    echartPie,
    echartBar,
    echartRace,
    echartMap,
    myMap,
    moveTrajectory
  },
  data() {
    return {
      clock: null,
      allCarCount: 0, // 车辆总数
      onlineCarSumCount: 0, // 当日上线总数
      oneDayCarSumMileage: 0, // 当日总行驶里程
      // 高排滚动列表
      list: [],
      animate: false,
      intervalId: null,
      loading: false,
      isShowHome: false,
      isShow: false,
      // 是否显示围栏区域
      isFence: true,
      // 围栏区域
      fenceData: [],
      // 是否显示marker标记点
      isMaker: true,
      // marker标记点数据
      markerData: [],
      // 是否是通过遮罩展示指定行政区域
      isMunicipalityMask: true,
      // 省市Marker点
      areaMarkerData: [],
      // 地图中心位置坐标 
      defaultCenter: [],
      // 是否自动回到预设中心点
      autoBackCenter: true,
      // 是否自动返回预设缩放层级
      autoBackZoom: true,
      // 全国行政边界
      nationwide: {},
      // 弹窗
      dialogVisible: false,
      targetQuery: Object.assign({}, defaultQuery),
      areaList: [
        { id: 1, name: '锦江区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 2, name: '武侯区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 3, name: '金牛区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 4, name: '成华区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 5, name: '青羊区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 6, name: '高新区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 7, name: '龙泉驿区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 8, name: '郫都区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 9, name: '新都区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 10, name: '温江区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 11, name: '新津区', count: 30300, nox: '29.32', co2: '36.12' },
      ],
      selectedId: '', // 当前选中行
      // TOP5省上线车辆数
      carsNumData: [],
      carsLabels: [],
      // TOP5总油耗
      xAxisData: ['企业1', '企业2', '企业3', '企业4', '企业5'],
      seriesData: [100, 200, 300, 400, 500],
    };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  created() {
    this.intervalId = setInterval(this.scroll, 1500);
    // 处理本地 mock 数据
    this.handleMockData('all');
    this.handleArea();
    this.getRoadWeb();
    // TOP5省上线车辆数
    provinceCount.forEach(el => {
      this.carsNumData.push(el?.cnt)
      this.carsLabels.push(el?.name)
    })
  },
  mounted() {
    this.numberScroll('allCarCount', 3942714, 3942814, 300)
    // 启用动态时钟
    this.clock = setInterval(this.updateClock, 1000);
  },
  methods: {
    getRoadWeb() {
      let arr = []
      let traffic = this.setTrafficStatus(this.getRandomInt(0, 400000))
      // 海量路网
      roadPath2.forEach(el => {
        el.line_string.forEach((el2, index) => {
          if (index % 1000 == 0) {
            traffic = this.setTrafficStatus(this.getRandomInt(0, 400000))
          }
          const obj = {
            vin: el.name,
            name: el.name,
            traffic,
            path: el2
          }
          arr.push(obj)
        })
      })
      this.pathSimplifierData = arr
      this.handleAreaMarker();
    },
    getRandomInt(min, max) {
      min = Math.ceil(min);
      max = Math.floor(max);
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    setTrafficStatus(data) {
      let status = '#67C23A'
      if (Number(data) > 0 && Number(data) <= 50000) {
        status = '#67C23A'
      }
      if (Number(data) > 50000 && Number(data) <= 100000) {
        status = '#E6A23C'
      }
      if (Number(data) > 100000 && Number(data) <= 149999) {
        status = '#F56C6C'
      }
      if (Number(data) > 150000 && Number(data) <= 299999) {
        status = 'rgb(245, 30, 30)'
      }
      if (Number(data) > 300000) {
        status = 'rgb(110, 1, 1)'
      }
      return status
    },
    // 获取全国省市Marker位置和信息 
    handleAreaMarker() {
      for (let i=0; i<provinceCount.length; i++) {
        if (provinceCount[i].lngLat) {
          this.areaMarkerData.push(
            {
              lng: provinceCount[i].lngLat[0],
              lat: provinceCount[i].lngLat[1],
              en_name: provinceCount[i].en_name,
              title: provinceCount[i].name,
              cnt: provinceCount[i].cnt
            }
          )
        }
      }
      // 获取全国行政区域边界
      getChinaArea({
        type: 'all'
      }).then(res => {
        this.nationwide = res
      }).catch(err => {

      }).finally(() => {
        this.isShowHome = true
      })
    },
    // 获取行政区域
    handleArea() {
      // 各个行政区域数据标记
      this.markerData = [
        { lon: 104.083802, lat: 30.64321, title: '锦江区:30300' },
        { lon: 104.100854, lat: 30.676437, title: '成华区:30300' },
        { lon: 104.044868, lat: 30.703611, title: '金牛区:30300' },
        { lon: 104.042251, lat: 30.677282, title: '青羊区:30300' },
        { lon: 104.020217, lat: 30.640092, title: '武侯区:30300' },
        { lon: 104.248317, lat: 30.598428, title: '龙泉驿区:30300' },
      ]
      // 行政区域边界
      this.fenceData.push(jinniu.coordinates)
      this.fenceData.push(jinniu2.coordinates)
      this.fenceData.push(chenghua.coordinates)
      this.fenceData.push(jingjiang.coordinates)
      this.fenceData.push(qingyang.coordinates)
      this.fenceData.push(wuhou.coordinates)
      this.fenceData.push(longquanyi.coordinates)
      this.defaultCenter = [104.065989,30.65827]
      // this.isShow = true
    },
    // 处理本地mock 数据
    handleMockData(range, key, level) {
      // 汇总车辆总数、总行驶里程
      this.handleSum('onlineCarCount', range, key, level)
      this.handleSum('carSumMileage', range, key, level)
    },
    handleSum(type, range, key, level) {
      // 高排列表
      if (range == 'all') {
        this.list = gaopaiSheng
      } else {
        if (level == 2) {
          this.list = []
          gaopaiShi.forEach(el => {
            if (el.parent == key) {
              this.list.push(el)
            }
          })
        }
        if (level == 3) {
          this.list = []
          gaopaiQu.forEach(el => {
            if (el.parent == key) {
              this.list.push(el)
            }
          })
        }
      }
      // 根据层级获取省、市
      let defaultCount = provinceCount
      let defaultMileage = provinceMileage
      let level_ = level
      if (level > 1) level_ = level - 1
      switch (level_) {
        case 1:
          defaultCount = provinceCount
          defaultMileage = provinceMileage
          break;
        case 2:
          defaultCount = cityCount
          defaultMileage = cityMileage
          break;
        // case 3:
        //   defaultCount = districtCount
        //   defaultMileage = districtMileage
        //   break;
      }
      if (level == 4) return
      // 获取对应上线数、总里程
      if (type == 'onlineCarCount') {
        this.onlineCarSumCount = 0 // 当日上线总数
        defaultCount.forEach(item => {
          // 全国当日上线总数
          if (range == 'all') {
            this.onlineCarSumCount += Number(item?.cnt)
          }
          // 当前 省市 当日上线总数
          if (range == 'normal') {
            if (key == item.name) {
              this.onlineCarSumCount = item?.cnt
            }
          }
        })
        this.numberScroll('onlineCarSumCount', (this.onlineCarSumCount-100), this.onlineCarSumCount, 300)
      }
      // 汇总当日行驶总里程
      if (type == 'carSumMileage') {
        this.oneDayCarSumMileage = 0 // 当日总行驶里程
        defaultMileage.forEach(item => {
          // 全国当日行驶总里程
          if (range == 'all') {
            this.oneDayCarSumMileage += Number(item?.cnt.toFixed(2))
          }
          // 当前 省市 当日行驶总里程
          if (range == 'normal') {
            if (key == item?.name) {
              this.oneDayCarSumMileage = Number(item?.cnt.toFixed(2))
            }
          }
        })
        this.numberScroll('oneDayCarSumMileage', (this.oneDayCarSumMileage-100), this.oneDayCarSumMileage, 300)
      }
    },
    // 滚动列表
    scroll() {
      this.animate = true
      setTimeout(() => {
        this.list.push(this.list[0])
        this.list.shift()
        this.animate = false
      }, 500)
    },
    handleRun() {
      this.intervalId = setInterval(this.scroll, 1500)
    },
    handleStop() {
      this.animate = false
      clearInterval(this.intervalId)
    },
    handleMapEvent(range, key, level) {
      // 打开弹窗，查看详情行政区县地图
      this.loading = true
      this.dialogVisible = true
      if(this.sidebar.opened) {
        this.$store.dispatch('app/toggleSideBar')
      }
      setTimeout(() => {
        this.isShow = true
        this.loading = false
      }, 100)
    },
    handleClose() {
      this.dialogVisible = false;
      this.isShow = false
    },
    // 数字滚动
    numberScroll(key, startNumber, endNumber, duration) {
      let currentNumber = startNumber;
      const increment = (endNumber - startNumber) / duration;
      const interval = setInterval(() => {
        currentNumber += increment;
        this[key] = qian(Math.round(currentNumber))
        if (currentNumber >= endNumber) {
          clearInterval(interval);
        }
      }, 1);
    },
    // 动态时钟
    updateClock() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      month = month < 10 ? '0' + month : month
      day = day < 10 ? '0' + day : day
      hours = hours < 10 ? '0' + hours : hours;
      minutes = minutes < 10 ? '0' + minutes : minutes;
      seconds = seconds < 10 ? '0' + seconds : seconds;
      let time = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
      document.getElementById('clock').textContent = time;
    },
    // dialog 弹窗中，选中行
    handleClickRow(index, item) {
      if (this.selectedId == index) {
        index = ''
      }
      this.selectedId = index
      // 重置选中记录
      const dom = document.getElementsByClassName('dialog-table-body-row')
      // 地图联动
      this.fenceData = []
      this.markerData = []
      if (this.selectedId == '') {
        this.handleArea()
        return
      }
      switch (item.name) {
        case '锦江区':
          this.fenceData.push(jingjiang.coordinates)
          this.defaultCenter = [104.083802,30.64321]
          this.markerData = [
            { lon: 104.083802, lat: 30.64321, title: '锦江区:30300' }
          ]
          break;
        case '成华区':
          this.fenceData.push(chenghua.coordinates)
          this.defaultCenter = [104.100854,30.676437]
          this.markerData = [
            { lon: 104.100854, lat: 30.676437, title: '成华区:30300' }
          ]
          break;
        case '金牛区':
          this.fenceData.push(jinniu.coordinates)
          this.fenceData.push(jinniu2.coordinates)
          this.defaultCenter = [104.044868,30.703611]
          this.markerData = [
            { lon: 104.044868, lat: 30.703611, title: '金牛区:30300' }
          ]
          break;
        case '青羊区':
          this.fenceData.push(qingyang.coordinates)
          this.defaultCenter = [104.042251,30.677282]
          this.markerData = [
            { lon: 104.042251, lat: 30.677282, title: '青羊区:30300' }
          ]
          break;
        case '武侯区':
          this.fenceData.push(wuhou.coordinates)
          this.defaultCenter = [104.020217,30.640092]
          this.markerData = [
            { lon: 104.020217, lat: 30.640092, title: '武侯区:30300' }
          ]
          break;
        case '龙泉驿区':
          this.fenceData.push(longquanyi.coordinates)
          this.defaultCenter = [104.248317,30.598428]
          this.markerData = [
            { lon: 104.248317, lat: 30.598428, title: '龙泉驿区:30300' }
          ]
          break;
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.intervalId)
    clearInterval(this.clock)
  }
};
</script>

<style scoped lang="scss">
.home-co2 {
  width: 100%;
  min-width: 520px;
  height: calc(100vh - 84px);
  // height: calc(100vh + 100px);
  overflow-x: auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-top: 10px;
  padding-right: 20px;
  padding-left: 20px;
  background: -webkit-radial-gradient(50% 35%, farthest-corner, #034f8e, #034987, #02366d, #002353);
  position: relative;
  z-index: 0;
  // 禁止选中文字
  -webkit-user-select: none; /* Chrome, Safari, Opera */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;

  // 底层地图背景
  .view-map {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    background-color: #1a2028;
  }
  // 标题
  .head {
    width: 100%;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;

    .title-box {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .main-title {
        width: 100%;
        height: 50px;
        line-height: 50px;
        font-weight: 600;
        text-align: center;
        letter-spacing: 0.2rem;
        font-size: 1.6rem;
        background-image: -webkit-linear-gradient(left, #147B96, #90e1ff 25%, #147B96 50%, #90e1ff 75%, #147B96);
        -webkit-text-fill-color: transparent;
        -webkit-background-clip: text;
        -webkit-background-size: 200% 100%;
        text-shadow: 3px 3px 6px rgba(20, 123, 150, 0.5);
      }
      .today {
        color: #90e1ff;
        font-weight: bold;
        font-size: 1.2rem;
        font-family: Arial, "Times New Roman", Times, Helvetica, "\5b8b\4f53", sans-serif;
      }
    }
  }
  // 右侧图表
  .view-right {
    width: 300px;
    height: auto;
    padding-top: 10px;
    padding-left: 10px;
    padding-right: 10px;
    box-sizing: border-box;
    position: absolute;
    bottom: 20px;
    right: 0px;
    // position: fixed;
    // bottom: 20px;
    // right: 0px;

    // 图表
    .echart-box {
      width: 100%;
      height: 300px;
      padding: 5px 5px;
      box-sizing: border-box;
      border-radius: 6px;
      margin-bottom: 10px;
      box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
      background-color: rgba(256, 256, 256, 0.1);

      .ecahrt-box-title {
        width: 100%;
        height: 35px;
        line-height: 35px;
        color: #FFF;
        font-weight: bold;
        font-size: 17px;
        padding-left: 5px;
        box-sizing: border-box;
        margin-bottom: 5px;
      }

      // 滚动列表
      .echart-box-list {
        width: calc(100% + 15px);
        height: calc(100% - 40px);
        overflow-y: auto;

        .list-item {
          margin-bottom: 4px;
          width: 97%;
          height: 35px;
          line-height: 35px;
          font-size: 12px;
          color: #fff;
          padding-left: 20px;
          padding-right: 40px;
          box-sizing: border-box;
          border-radius: 4px;
          display: flex;
          justify-content: space-between;

          .list-item-label,
          .list-item-date,
          .ist-item-value {
            width: calc(100% / 3);
            overflow: hidden; /* 确保溢出的内容会被隐藏 */
            white-space: nowrap; /* 确保文本在一行内显示，不换行 */
            text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
          }
          .list-item-value {
            color: rgba(216, 100, 248, 0.7);
            font-weight: bold;
          }
        }
        .list-item:hover {
          cursor: pointer;
          box-shadow: 3px 3px 5px rgba(230, 159, 249, 0.3);
          background: linear-gradient(to right, rgba(216, 100, 248, 0.1) 30%, rgba(216, 100, 248, 0.7) 50%) right;
          background-size: 200%;
          transition: .5s ease-in-out;
          font-weight: bold;
          font-size: 13px;
          .list-item-value {
            color: #fff;
          }
        }

        .anim {
          transition: all 0.5s;
          margin-top: -41px;
        }
      }
    }
  }
  // 左侧图表
  .view-left {
    width: 450px;
    height: auto;
    padding-top: 50px;
    padding-left: 10px;
    padding-right: 10px;
    box-sizing: border-box;
    position: absolute;
    bottom: 20px;
    left: 0px;
    // position: fixed;
    // bottom: 20px;
    // left: 50px;

    // 数据总汇
    .view-summary {
      width: 100%;
      height: 100px !important;
      line-height: 20px;
      display: flex;
      justify-content: center;
      .data-sum-col {
        width: calc(500px / 3);
        border-radius: 6px;
        box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
        background-color: rgba(256, 256, 256, 0.1);
        text-align: center;
        padding: 10px 10px;
        padding-top: 20px;
        box-sizing: border-box;
        .data-sum-title {
          color: #FFF;
          font-weight: bold;
          font-size: 0.8rem;
        }
        .data-sum-value {
          font-size: 1.1rem;
          font-weight: bold;
          color: rgba(216, 100, 248, 1);
        }
      }
      .data-sum-col:nth-child(1), .data-sum-col:nth-child(2) {
        margin-right: 10px;
      }
    }
    // 图表
    .echart-box {
      width: 100%;
      height: calc((100vh - 100px) / 3);
      padding: 5px 5px;
      box-sizing: border-box;
      border-radius: 6px;
      margin-bottom: 10px;
      box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
      background-color: rgba(256, 256, 256, 0.1);

      .ecahrt-box-title {
        width: 100%;
        height: 35px;
        line-height: 35px;
        color: #FFF;
        font-weight: bold;
        font-size: 17px;
        padding-left: 5px;
        box-sizing: border-box;
      }
    }
    .no_bg_color {
      padding: 0px 0px;
      background-color: transparent;
    }
  }
  // 弹窗样式
  .dialog-container {
    width: 100%;
    height: calc(100vh - 200px);
    padding-right: 20px;
    box-sizing: border-box;
    overflow-y: auto;
    .dialog-container-title {
      width: 100%;
      border-bottom: 2px #319ee4 solid;
      color: white;
      font-size: 16px;
      padding-bottom: 5px;
      box-sizing: border-box;
    }
    .dialog-container-echarts,
    .dialog-container-table {
      width: 100%;
      height: auto;
      margin-top: 10px;
      margin-bottom: 10px;
      .dialog-echarts-data {
        width: 100%;
        height: 210px;
        margin-top: 10px;
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        .echarts-line {
          width: 65%;
          height: 100%;
          margin-right: 10px;
          border-radius: 6px;
          overflow: hidden;
        }
        .echarts-radar {
          width: 35%;
          height: 100%;
          border-radius: 6px;
          overflow: hidden;
        }
      }
      .dialog-table-data {
        width: 100%;
        height: auto;
        margin-top: 10px;
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        .table-async-map {
          width: 400px;
          height: 400px;
        }
        .table-container {
          width: calc(100% - 400px);
          height: 600px;
          margin-right: 20px;
          color: white;
          font-size: 13px;
          font-weight: bold;
          .dialog-table-head {
            width: 100%;
            height: 43px;
            line-height: 43px;
            text-align: center;
            background: #1f486e;
            border: 1px #06c solid;
            display: flex;
            justify-content: flex-start;
          }
          .dialog-table-body {
            width: 100%;
            height: auto;
            .dialog-table-body-row {
              width: 100%;
              height: 43px;
              line-height: 43px;
              text-align: center;
              border-bottom: 1px #013968 solid;
              border-left: 1px #013968 solid;
              border-right: 1px #013968 solid;
              display: flex;
              justify-content: flex-start;
            }
            .dialog-table-body-row:hover {
              cursor: pointer;
              box-shadow: 3px 3px 5px #1f486e;
              background: linear-gradient(to right, #011a33 30%, #bcdfff77 50%) right;
              background-size: 200%;
              transition: .5s ease-in-out;
            }
            .active {
              color: #319ee4;
              font-size: 15px;
            }
          }
          .dialog-table-column {
            width: calc(100% / 5);
            height: 100%;
          }
        }
      }
    }
    .dialog-echarts-query {
      width: 100%;
      height: auto;
      color: #fff;
      padding-top: 5px;
      padding-bottom: 5px;
      box-sizing: border-box;
      ::v-deep .el-radio {
        color: #fff;
      }
    }
  }
  
  ::v-deep .el-dialog {
    background-color: #011a33;
  }
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
  }
  ::v-deep .el-dialog__title {
    font-weight: bold;
    color: #319ee4;
  }
  ::v-deep .el-dialog__headerbtn .el-dialog__close {
    color: #ffffff;
  }
  ::v-deep .el-dialog__headerbtn .el-dialog__close:hover {
    transform: rotate(20deg);
  }
  /* 修改滚动条滑块颜色 */
  ::v-deep .dialog-container::-webkit-scrollbar-thumb {
    background-color: #319ee4;
  }
  /* 修改滚动条轨道背景色 */
  ::v-deep .dialog-container::-webkit-scrollbar-track {
    background-color: #4b6a89;
    border-radius: 3px;
  }
  ::v-deep .el-statistic .title {
    font-size: 21px;
    font-weight: bold;
    color: #e6e6e6;
  }

  ::v-deep .el-statistic .number {
    font-size: 22px;
    color: #ffc800;
  }

  ::v-deep .el-dialog {
    width: 80%;
  }
}
</style>