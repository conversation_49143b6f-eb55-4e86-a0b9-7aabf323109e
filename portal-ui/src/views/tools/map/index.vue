<template>
    <div class="tools-map-container" v-loading="laoding" element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
        <div class="left">
            <!-- 地图展示 -->
            <div class="map">
                <my-map ref="myMap" v-if="isShow" :defaultZoom="Number(defaultZoom)" :defaultCenter="defaultCenter"
                    :isDeep="mapBgColor" :isLimitRadius="isLimitRadius" :limitRadius="limitRadius"
                    :limitCenter="limitCenter" :isLocation="true" :toolpageType="type" :isToolsPage="true"
                    :polyLineArr="polyLineArr" :markerData="markerData" :radius="Number(heatmapRadius)"
                    :hotMapData="hotMapData" :heatmapMaxVal="heatmapMaxVal" :outsideColor="outsideColor"
                    :insideColor="insideColor" :coveColor="coveColor" :isFence="true" :isEditFence="isEditFence"
                    previousPage="fence" :fenceData="fenceData" :lnglatType="lnglatType"
                    :limitRadiusType="limitRadiusType" :isPointSimplifier="isPointSimplifier"
                    :pointSimplifierData="pointSimplifierData" @getCurrentLnglat="getCurrentLnglat"
                    @getPolygonLnglat="getPolygonLnglat"></my-map>
                <el-empty class="empty-cover" v-else description="暂无数据"></el-empty>
                <!-- 右下角colorbar -->
                <div class="colorbar" v-if="fenceData.length > 0">
                    <div class="colorbar-item">
                        <div class="title">{{ curDateStr }}</div>
                    </div>
                    <div class="colorbar-item" v-for="(item, index) in intervals" :key="index">
                        <div :class="getColor(index)"></div>
                        <div class="number">{{ formatStr(item) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right">
            <el-divider content-position="left">拾取经纬度</el-divider>
            <div class="search-item">
                <div class="search-title">经纬度:</div>
                <div :style="'margin-left: 10px; color: #fff;'">{{ pickupLnglat || '--' }}</div>
                <el-button type="primary" size="mini" style="margin-left: 10px;" :disabled="!pickupLnglat"
                    v-clipboard:copy="pickupLnglat" @click="copySuccess">复制</el-button>
            </div>
            <el-divider content-position="left">地图样式</el-divider>
            <div class="search-item">
                <div class="search-title">地图颜色:</div>
                <el-radio-group v-model="mapBgColor" size="mini" v-removeAriaHidden>
                    <el-radio :label="false">浅色</el-radio>
                    <el-radio :label="true">深色</el-radio>
                </el-radio-group>
            </div>
            <el-divider class="el-divider-mt" content-position="left">限制区域</el-divider>
            <div class="search-item">
                <div class="search-title">是否启用:</div>
                <el-radio-group v-model="isLimitRadius" size="mini" v-removeAriaHidden>
                    <el-radio :label="false">否</el-radio>
                    <el-radio :label="true">是</el-radio>
                </el-radio-group>
            </div>
            <div class="search-item" v-if="isLimitRadius">
                <div class="search-title">区域类型:</div>
                <el-radio-group v-model="limitRadiusType" size="mini" @input="handleEditFence">
                    <el-radio label="circular">圆形</el-radio>
                    <el-radio label="polygon">不规则多边形</el-radio>
                </el-radio-group>
            </div>
            <div class="search-item" v-if="isLimitRadius && limitRadiusType == 'circular'">
                <div class="search-title">半径:</div>
                <el-input v-model="limitRadius" size="mini" placeholder="计量单位(米)" @blur="handleBlur"></el-input>
            </div>
            <div class="search-item" v-if="isLimitRadius">
                <div class="search-title">中心点坐标:</div>
                <el-input v-model="limitCenter" size="mini" placeholder="104.308173, 30.846514"
                    @blur="handleBlur"></el-input>
            </div>
            <div class="search-item" v-if="isLimitRadius && limitRadiusType == 'circular'">
                <div class="search-title">外圈颜色:</div>
                <el-color-picker v-model="outsideColor"></el-color-picker>
                <div :style="'margin-left: 10px; color:' + outsideColor + ';'">{{ outsideColor }}</div>
            </div>
            <div class="search-item" v-if="isLimitRadius && limitRadiusType == 'circular'">
                <div class="search-title">内填充色:</div>
                <el-color-picker v-model="insideColor"></el-color-picker>
                <div :style="'margin-left: 10px; color:' + insideColor + ';'">{{ insideColor }}</div>
            </div>
            <div class="search-item" v-if="isLimitRadius">
                <div class="search-title">坐标系:</div>
                <el-radio-group v-model="lnglatType" size="mini" v-removeAriaHidden @input="handleBlur">
                    <el-radio label="amap">高德坐标系</el-radio>
                    <el-radio label="84">84坐标系</el-radio>
                </el-radio-group>
            </div>
            <div class="search-item" v-if="isLimitRadius">
                <div class="search-title">获取结果:</div>
                <el-button type="primary" size="mini" style="margin-left: 10px;" v-clipboard:copy="fenceLnglat"
                    @click="copySuccess">复制</el-button>
                <el-button type="primary" size="mini" style="margin-left: 10px;" @click="downloadLatlng">下载</el-button>
            </div>
            <el-divider class="el-divider-mt" content-position="left">地图覆盖物标记</el-divider>
            <div class="search-item">
                <div class="search-title">类型:</div>
                <el-select class="input" v-model="type" placeholder="请选择" size="mini">
                    <el-option v-for="item in typeOption" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div class="search-item" v-if="['lines'].includes(type)">
                <div class="search-title">折线颜色:</div>
                <el-color-picker v-model="coveColor"></el-color-picker>
                <div :style="'margin-left: 10px; color:' + coveColor + ';'">{{ coveColor }}</div>
            </div>
            <div class="search-item" v-if="type == 'heatmap'">
                <div class="search-title">地图缩放层级:</div>
                <el-input v-model="defaultZoom" size="mini"></el-input>
            </div>
            <div class="search-item" v-if="type == 'heatmap'">
                <div class="search-title">热力图单个圆圈半径:</div>
                <el-input v-model="heatmapRadius" size="mini" placeholder="例:输入7,代表半径7px单个热力图色谱"></el-input>
            </div>
            <div class="tips">
                数据格式例如：
                <template v-if="type == 'lines'">
                    [
                    {
                    "name": "1",
                    "line":["116.407394, 39.904211", "114.530235, 38.037433"]
                    },
                    {
                    "name":"2",
                    "line":["116.407394,39.904211", "112.562678,37.873499"]
                    }
                    ]
                </template>
                <template v-else-if="type == 'marker' || type == 'massmarks'">
                    [
                    {
                    "en_name": "1",
                    "title": "1",
                    "cnt": "",
                    "marker":[116.407394, 39.904211]
                    },
                    {
                    "en_name":"2",
                    "title": "2",
                    "cnt": "",
                    "marker":[116.407394, 40.904211]
                    }
                    ]
                </template>
                <template v-else-if="type == 'heatmap'">
                    [
                    {"lng":110.2090187,"lat":18.64292989,"count":4},
                    {"lng":110.4305156,"lat":19.47845427,"count":4}
                    ]
                </template>
            </div>
            <!-- JSON输入框 -->
            <div class="json-place">
                <json-editor class="jsonEditor" v-model="jsonData" mode="code" @input="onJsonChange"
                    @has-error="onJsonChange"></json-editor>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex'
// 公共方法
import { createGps } from "@/utils/transform"
// 导入组件
import Clipboard from 'clipboard';
import myMap from '@/components/amap/myMap'
import JsonEditor from 'vue-json-editor'

export default {
    name: "toolsMap",
    components: {
        myMap,
        JsonEditor,
    },
    computed: {
        ...mapState({
            sidebar: state => state.app.sidebar,
        }),
    },
    data() {
        return {
            // 搜索
            mapBgColor: false,
            isLimitRadius: false,
            limitRadiusType: 'circular',
            limitRadius: '5000',
            limitCenter: '',
            type: 'lines',
            typeOption: [
                {
                    label: '折线',
                    value: 'lines',
                },
                {
                    label: '点',
                    value: 'marker',
                },
                {
                    label: '海量点',
                    value: 'massmarks',
                },
                {
                    label: '热力图',
                    value: 'heatmap',
                }
            ],
            lnglatType: 'amap',
            // json-editor
            jsonData: [],
            // 地图
            laoding: false,
            isShow: true,
            polyLineArr: [], // 折线
            markerData: [], // marker标记点位
            hotMapData: [], // 热力图数据
            heatmapMaxVal: 0, // 设置热力图区间最大值
            heatmapRadius: 16, // 热力图单个光斑半径
            defaultZoom: 9, // 地图缩放层级,
            baseFenceData: [],
            fenceData: [],
            fenceNames: {},
            defaultCenter: [103.963851, 30.764763],
            outsideColor: '#FF0000',
            insideColor: '#FF0000',
            coveColor: '#FF0000',
            colorBarMaxVal: 0,
            intervals: [],
            pickupLnglat: '',
            fenceLnglat: '',
            isEditFence: false,
            isPointSimplifier: false,
            pointSimplifierData: [],
            // 热力图轮播
            timer: null,
            curDate: '',
            curIndex: 0,
            curDateStr: '',
            categorizedData: {},
            keysName: []
        };
    },
    watch: {
        'sidebar.opened': function (val) {
            setTimeout(() => {
                // 监听左侧菜单栏开合，重绘页面图表
                this.resizeChart();
            }, 300)
        },
        'jsonData': function (val) {
            if (['', undefined, null].includes(val) || val.length == 0) {
                // this.isShow = false
            } else if (typeof (val) !== 'object') {
                // this.isShow = false
            } else {
                this.getRoadWeb()
            }
        },
        'type': function (key) {
            switch (key) {
                case 'lines':
                case 'marker':
                case 'massmarks':
                case 'heatmap':
                    this.jsonData = []
                    this.polyLineArr = []
                    this.pointSimplifierData = []
                    // this.isShow = false
                    break;
            }
        },
        'pickupLnglat': function (val) {
            if (this.limitRadiusType == 'polygon') {
                this.$refs.myMap.clearMap()
                this.isEditFence = true
                this.limitCenter = this.pickupLnglat
            }
        }
    },
    created() {
        // 窗口大小变化，重绘Echart图表
        window.addEventListener('resize', () => {
            this.resizeChart();
        });
    },
    beforeDestroy() {
        clearTimeout(this.timer)
    },
    mounted() {
        // 热力图
        // this.heatmapChange()
        // 行政边界图
        // this.setFence()
    },
    methods: {
        getDigitCount(num) {
            return num.toString().length;
        },
        getFirstDigit(num) {
            return parseInt(num.toString()[0], 10);
        },
        formatStr(data) {
            let one = this.getFirstDigit(data[0])
            if (one !== 0) {
                for (let i = 0; i < this.getDigitCount(data[0]) - 1; i++) {
                    one += '0'
                }
            }
            let two = this.getFirstDigit(data[1])
            for (let i = 0; i < this.getDigitCount(data[1]) - 1; i++) {
                two += '0'
            }
            return one + '--' + two + '(小时)'
        },
        // 拾取经纬度
        getPolygonLnglat(data) {
            const obj = {
                "type": "MultiPolygon",
                "coordinates": [
                    [
                        data
                    ]
                ]
            }
            this.fenceLnglat = JSON.stringify(obj)
        },
        getCurrentLnglat(data) {
            if (data) {
                this.pickupLnglat = data.lng + ',' + data.lat
            } else {
                this.pickupLnglat = ''
            }
        },
        // 复制经纬度
        copySuccess() {
            this.$message.success('复制成功')
        },
        // 下载
        downloadLatlng() {
            const that = this;
            this.$prompt('请输入文件名称', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPattern: /^\S+$/,
                inputErrorMessage: '文件名不能为空'
            }).then(({ value }) => {
                const name = value;
                const jsonData = that.fenceLnglat || [];
                const blob = new Blob([jsonData], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = name + '.json';
                document.body.appendChild(a);
                a.click();
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '取消输入'
                });
            });
            // this.fenceLnglat
        },
        // 行政边界
        setFence() {
            const arr = []
            const { features } = data_amap_copy;
            for (let i = 0; i < features.length; i++) {
                arr.push(
                    {
                        adcode: features[i].properties.adcode,
                        name: features[i].properties.name,
                        coordinates: features[i].geometry.coordinates
                    }
                )
            }
            for (let i = 0; i < arr.length; i++) {
                const coordinates = arr[i].coordinates
                for (let j = 0; j < coordinates.length; j++) {
                    this.fenceData.push({
                        name: arr[i].name,
                        coordinates: coordinates[j],
                    })
                }
            }
            this.baseFenceData = JSON.parse(JSON.stringify(this.fenceData))
            // work_time
            const workTime = [];
            for (let i = 0; i < work_time.length; i++) {
                if (work_time[i].region_name == 'area') continue;
                const obj = work_time[i];
                obj.dateStr = work_time[i].y + '-' + work_time[i].m;
                obj.timestamp = new Date(work_time[i].y + '-' + work_time[i].m).getTime();
                workTime.push(obj)
            }
            // 数据分类
            this.categorizedData = workTime.reduce((acc, item) => {
                const { dateStr, region_name } = item;
                if (!acc[dateStr]) {
                    acc[dateStr] = [];
                }
                acc[dateStr].push(item);
                return acc;
            }, {});
            // console.log(this.fenceData)
            // console.log(this.categorizedData)
            for (let key in this.categorizedData) {
                this.keysName.push(key)
            }
            this.handleLoopData()

        },
        handleLoopData() {
            const baseArr = JSON.parse(JSON.stringify(this.baseFenceData))
            this.timer = setTimeout(() => {
                this.curDateStr = this.keysName[this.curIndex]
                const max_val = Math.max(...this.categorizedData[this.keysName[this.curIndex]].map(item => Number(item.day_working_time)));
                this.splitIntoFive(max_val)
                // 增加颜色
                const arr = this.categorizedData[this.keysName[this.curIndex]];
                for (let i = 0; i < arr.length; i++) {
                    this.intervals.forEach((item, index) => {
                        if (Number(arr[i].day_working_time) >= item[0] && Number(arr[i].day_working_time) <= item[1]) {
                            const region_name = arr[i].region_name;
                            arr[i].color = this.getColor1(index);
                        } else if (Number(arr[i].day_working_time) >= item[0] && Number(arr[i].day_working_time) > item[1]) {
                            arr[i].color = this.getColor1(index);
                        }
                    })
                }
                for (let i = 0; i < baseArr.length; i++) {
                    for (let j = 0; j < arr.length; j++) {
                        if (baseArr[i].name == arr[j].region_name) {
                            baseArr[i].color = arr[j].color;
                        }
                    }
                }
                this.fenceData = baseArr
                if (this.curIndex < this.keysName.length - 1) {
                    this.curIndex++;
                    this.handleLoopData();
                } else {
                    this.curIndex = 0;
                }
            }, 3000)
        },
        // 热力图变化
        heatmapChange() {
            let arr = [];
            for (let i = 0; i < heatmapData.length; i++) {
                const obj = heatmapData[i];
                obj.dateStr = heatmapData[i].y + '-' + heatmapData[i].m + '-' + heatmapData[i].d;
                obj.date = Number(new Date(heatmapData[i].y + '-' + heatmapData[i].m + '-' + heatmapData[i].d).getTime());
                obj.lng = createGps({ longitude: heatmapData[i].last_lng, latitude: heatmapData[i].last_lat })[0];
                obj.lat = createGps({ longitude: heatmapData[i].last_lng, latitude: heatmapData[i].last_lat })[1];
                obj.count = heatmapData[i].day_working_time;
                if (heatmapData[i].y == 2023 && heatmapData[i].m == 9) {
                } else {
                    arr.push(obj);
                }
            }
            arr = arr.sort((a, b) => a.date - b.date);
            let categorizedArr = arr.reduce((acc, obj) => {
                let key = obj.date;
                if (!acc[key]) {
                    acc[key] = [];
                }
                acc[key].push(obj);
                return acc;
            }, {});
            this.jsonData = [];
            this.setTimer(categorizedArr);
        },
        setTimer(arr) {
            this.type = 'heatmap'
            let keys = Object.keys(arr)
            let lastKey = Object.keys(arr).pop();
            this.timer = setTimeout(() => {
                this.jsonData = []
                const index = keys[this.curIndex];
                this.jsonData = arr[index];
                this.curDate = arr[index][0].date;
                this.curDateStr = arr[index][0].dateStr;
                this.curIndex = (this.curIndex + 1) % keys.length;
                this.setTimer(arr)
                if (lastKey == this.curIndex) {
                    // this.curIndex = 0;
                    clearTimeout(this.timer);
                }
            }, 1000)
        },
        splitIntoFive(number) {
            let interval = number / 5;
            let intervals = [];
            for (let i = 0; i < 5; i++) {
                let start = i * interval;
                let end = start + interval;
                intervals.push([parseInt(start), parseInt(end)]);
            }
            this.intervals = intervals;
        },
        getColor1(index) {
            index = index + 1
            let text = '#fffebb'
            if (index == 1) {
                text = '#fffebb'
            }
            if (index == 2) {
                text = '#fac36c'
            }
            if (index == 3) {
                text = '#ec868a'
            }
            if (index == 4) {
                text = '#bc5e9e'
            }
            if (index == 5) {
                text = '#81598a'
            }
            if (index == 6) {
                text = '#4f4f4f'
            }
            return text
        },
        getColor(index) {
            index = index + 1
            let text = 'color blue'
            if (index == 1) {
                text = 'color blue'
            }
            if (index == 2) {
                text = 'color green'
            }
            if (index == 3) {
                text = 'color yellow'
            }
            if (index == 4) {
                text = 'color red1'
            }
            if (index == 5) {
                text = 'color red2'
            }
            if (index == 6) {
                text = 'color red3'
            }
            return text
        },
        resizeChart() {
            this.myChart.resize();
        },
        onJsonChange() {
            // const strJson = JSON.stringify(this.jsonData)
            // if (['', null, undefined, '[]', '{}'].includes(strJson)) {
            //     this.polyLineArr = []
            //     this.isShow = false
            // }
        },
        getRoadWeb() {
            const { jsonData } = this
            let arr = []
            if (this.type == 'lines') { // 折线
                this.polyLineArr = []
                for (let i = 0; i < jsonData.length; i++) {
                    const point1 = createGps({ longitude: jsonData[i].line[0].split(',')[0], latitude: jsonData[i].line[0].split(',')[1] })[0] + ',' + createGps({ longitude: jsonData[i].line[0].split(',')[0], latitude: jsonData[i].line[0].split(',')[1] })[1]
                    const point2 = createGps({ longitude: jsonData[i].line[1].split(',')[0], latitude: jsonData[i].line[1].split(',')[1] })[0] + ',' + createGps({ longitude: jsonData[i].line[1].split(',')[0], latitude: jsonData[i].line[1].split(',')[1] })[1]
                    arr.push([point1, point2])
                }
                this.polyLineArr = arr
            } else if (this.type == 'marker') { // 标记点;
                // push 数据展示
                this.markerData = []
                for (let i = 0; i < jsonData.length; i++) {
                    arr.push(
                        {
                            lng: createGps({ longitude: jsonData[i].marker[0], latitude: jsonData[i].marker[1] })[0],
                            lat: createGps({ longitude: jsonData[i].marker[0], latitude: jsonData[i].marker[1] })[1],
                            en_name: jsonData[i].en_name,
                            title: jsonData[i].title,
                            cnt: jsonData[i].cnt
                        }
                    )
                }
                this.markerData = arr || []
            } else if (this.type == 'heatmap') { // 热力图
                let arr = JSON.parse(JSON.stringify(jsonData))
                arr = arr.sort((a, b) => {
                    return a.count - b.count
                })
                this.hotMapData = arr || []
                arr = arr.slice(1, arr.length - 1)
                this.heatmapMaxVal = Math.max(...arr.map(item => Number(item.count))) + (Math.max(...arr.map(item => Number(item.count))) * 0.3)
                this.colorBarMaxVal = parseInt(this.heatmapMaxVal)
                this.splitIntoFive(this.colorBarMaxVal);
            } else if (this.type == 'massmarks') {
                for (let i = 0; i < jsonData.length; i++) {
                    this.pointSimplifierData.push(
                        createGps({ longitude: jsonData[i].marker[0], latitude: jsonData[i].marker[1] })[0] +
                        ',' +
                        createGps({ longitude: jsonData[i].marker[0], latitude: jsonData[i].marker[1] })[1]
                    )
                }
                this.isPointSimplifier = true
            }
            this.isShow = true
            this.$refs.myMap.handleSetFitView()
        },
        setTrafficStatus(data) {
            let status = '#67C23A'
            if (Number(data) > 0 && Number(data) <= 50000) {
                status = '#67C23A'
            }
            if (Number(data) > 50000 && Number(data) <= 100000) {
                status = '#E6A23C'
            }
            if (Number(data) > 100000 && Number(data) <= 149999) {
                status = '#F56C6C'
            }
            if (Number(data) > 150000 && Number(data) <= 299999) {
                status = 'rgb(245, 30, 30)'
            }
            if (Number(data) > 300000) {
                status = 'rgb(110, 1, 1)'
            }
            return status
        },
        getRandomInt(min, max) {
            min = Math.ceil(min);
            max = Math.floor(max);
            return Math.floor(Math.random() * (max - min + 1)) + min;
        },
        handleBlur() {
            const bool = this.limitCenter && this.limitCenter.indexOf(',') !== -1
            if (this.limitCenter == '') return
            if (bool) {
                setTimeout(() => {
                    let lngLatData = sessionStorage.getItem('fenceLnglat') || [];
                    if (lngLatData) {
                        lngLatData = JSON.parse(lngLatData)
                    }
                    this.$refs.myMap.handleSetFitView()
                    const obj = {
                        "type": "MultiPolygon",
                        "coordinates": [
                            [
                                lngLatData
                            ]
                        ]
                    }
                    // 移除 Q、R 属性
                    const { coordinates } = obj
                    const data = coordinates[0][0];
                    const newdata = {
                        "type": "MultiPolygon",
                        "coordinates": [[[]]]
                    }
                    // 去除 data 中每个对象的 Q 和 R 属性
                    data.forEach(item => {
                        newdata.coordinates[0][0].push([item[0], item[1]])
                    })
                    this.fenceLnglat = JSON.stringify(newdata);
                    // this.fenceLnglat = JSON.stringify(obj)
                }, 0)
            }
        },
        handleEditFence() {
            if (this.limitRadiusType == 'polygon') {
                this.isEditFence = true
                this.$nextTick(() => {
                    this.$refs.myMap.createMap()
                })
            } else {
                this.isEditFence = false
            }
        }
    }
};
</script>

<style scoped lang="scss">
.tools-map-container {
    width: 100%;
    height: calc(100vh - 85px);
    background-color: #011a33;
    box-sizing: border-box;
    padding: 10px 10px;
    display: flex;
    justify-content: center;

    .left {
        width: calc(100% - 400px);
        height: 100%;
        padding-right: 10px;
        box-sizing: border-box;

        .map {
            width: 100%;
            height: 100%;
            box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.3);
            border-radius: 6px;
            overflow: hidden;

            .empty-cover {
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.3);
            }

            .colorbar {
                padding: 10px 10px;
                background-color: rgb(0 0 0 / 80%);
                border-radius: 4px 4px;
                -webkit-box-shadow: 3px 3px 3px rgb(0 0 0 / 60%);
                box-shadow: 3px 3px 3px rgb(0 0 0 / 60%);
                position: absolute;
                top: 90px;
                right: 630px;
                color: #fff;

                .colorbar-item {
                    font-size: 12px;
                    font-weight: bold;
                    display: flex;
                    justify-content: left;
                    margin-top: 5px;

                    // 蓝色
                    .blue {
                        background-color: #fffebb;
                    }

                    // 绿色
                    .green {
                        background-color: #fffebb;
                    }

                    // 黄色
                    .yellow {
                        background-color: #ec868a;
                    }

                    // 红色
                    .red1 {
                        background-color: #bc5e9e;
                    }

                    .red2 {
                        background-color: #81598a;
                    }

                    .red3 {
                        background-color: #81598a;
                    }

                    .title {
                        font-size: 13px;
                    }

                    .color {
                        width: 20px;
                        height: 10px;
                        border-radius: 3px;
                    }

                    .color:hover {
                        cursor: pointer;
                    }

                    .number {
                        width: auto;
                        height: 10px;
                        line-height: 10px;
                        text-align: center;
                        margin-left: 5px;
                    }
                }
            }
        }
    }

    .right {
        width: 400px;
        height: 100%;
        overflow: auto;
        box-sizing: border-box;
        padding: 10px 10px;
        // display: flex;
        // flex-direction: column;
        // justify-content: center;
        // align-items: center;

        .el-divider-mt {
            margin-top: 50px;
        }

        .search-item {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 10px;

            .el-input,
            .el-select {
                width: calc(100% - 140px)
            }

            .el-radio {
                color: #fff;
            }

            .search-title {
                width: 90px;
                font-size: 14px;
                font-weight: bold;
                text-align: right;
                color: #fff;
                box-sizing: border-box;
                padding-right: 5px;
            }
        }

        .tips {
            width: 100%;
            height: auto;
            color: rgba(255, 255, 255, 0.5);
            font-size: 13px;
        }

        .json-place {
            width: 100%;
            height: 600px;
            padding: 10px 10px;
            box-sizing: border-box;
        }
    }
}

::v-deep .jsonEditor {
    width: 100%;
    height: 100%;
}

::v-deep .jsoneditor-vue {
    width: 100%;
    height: 100%;
}

::v-deep .jsoneditor-poweredBy {
    display: none;
}

// 设置背景地图左右下角标文字颜色
::v-deep #location,
::v-deep .amap-copyright {
    // color: #ffffff;
    display: none;
}
</style>