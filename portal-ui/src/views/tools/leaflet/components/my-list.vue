<template>
    <div class="list-continer">
        <div
            class="list-group"
            v-for="(arr, index) in checkboxList"
            :key="index"
        >
            <RecycleScroller
                class="scroller"
                :items="arr"
                :item-size="10"
                key-field="id"
                v-slot="{ item }"
            >
                <div class="user">
                    <el-checkbox>{{ item.name || '无名路' }}</el-checkbox>
                </div>
            </RecycleScroller>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
    name: "my-checkbox",
    components: {

    },
    computed: {
        ...mapState({
            sidebar: state => state.app.sidebar,
        }),
    },
    props: ['checkboxList'],
    watch: {
        'checkboxList': function(val) {
            Object.assign(this.defaultData, val)
            console.log(this.defaultData)
        }
    },
    data() {
        return {
            defaultData: [],
        }
    },
    created() {
    },
    beforeDestroy() {

    },
    mounted() {
    },
    methods: {

    }
};
</script>

<style scoped lang="scss">
.list-continer {
    width: 100%;
    height: 100%;
    padding: 10px 10px;

    .list-group {
        width: 200px;
        display: flex;
        justify-content: flex-start;
        justify-items: center;
        flex-direction: column;
    }
}

.cl-checkbox {
    position: relative;
    display: inline-block;
}

/* Input */
.cl-checkbox>input {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    z-index: -1;
    position: absolute;
    left: -10px;
    top: -8px;
    display: block;
    margin: 0;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.6);
    box-shadow: none;
    outline: none;
    opacity: 0;
    transform: scale(1);
    pointer-events: none;
    transition: opacity 0.3s, transform 0.2s;
}

/* Span */
.cl-checkbox>span {
    display: inline-block;
    width: 100%;
    cursor: pointer;
}

/* Box */
.cl-checkbox>span::before {
    content: "";
    display: inline-block;
    box-sizing: border-box;
    margin: 3px 11px 3px 1px;
    border: solid 2px;
    /* Safari */
    border-color: rgba(0, 0, 0, 0.6);
    border-radius: 2px;
    width: 18px;
    height: 18px;
    vertical-align: top;
    transition: border-color 0.2s, background-color 0.2s;
}

/* Checkmark */
.cl-checkbox>span::after {
    content: "";
    display: block;
    position: absolute;
    top: 4px;
    left: 2px;
    width: 10px;
    height: 5px;
    border: solid 2px transparent;
    border-right: none;
    border-top: none;
    transform: translate(3px, 4px) rotate(-45deg);
}

/* Checked */
.cl-checkbox>input:checked,
.cl-checkbox>input:indeterminate {
    background-color: #13ce66;
}

.cl-checkbox>input:checked+span::before,
.cl-checkbox>input:indeterminate+span::before {
    border-color: #13ce66;
    background-color: #13ce66;
}

.cl-checkbox>input:checked+span::after,
.cl-checkbox>input:indeterminate+span::after {
    border-color: #fff;
}

.cl-checkbox>input:indeterminate+span::after {
    border-left: none;
    transform: translate(4px, 3px);
}

/* Hover, Focus */
.cl-checkbox:hover>input {
    opacity: 0;
}

.cl-checkbox>input:focus {
    opacity: 0;
}

.cl-checkbox:hover>input:focus {
    opacity: 0;
}

/* Active */
.cl-checkbox>input:active {
    opacity: 1;
    transform: scale(0);
    transition: transform 0s, opacity 0s;
}

.cl-checkbox>input:active+span::before {
    border-color: #85b8b7;
}

.cl-checkbox>input:checked:active+span::before {
    border-color: transparent;
    background-color: rgba(0, 0, 0, 0.6);
}

/* Disabled */
.cl-checkbox>input:disabled {
    opacity: 0;
}

.cl-checkbox>input:disabled+span {
    color: rgba(0, 0, 0, 0.38);
    cursor: initial;
}

.cl-checkbox>input:disabled+span::before {
    border-color: currentColor;
}

.cl-checkbox>input:checked:disabled+span::before,
.cl-checkbox>input:indeterminate:disabled+span::before {
    border-color: transparent;
    background-color: currentColor;
}
</style>
