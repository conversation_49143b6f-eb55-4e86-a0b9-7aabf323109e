/**
 * polyline 负责地图折线、多线段绘制
 */
export const polyline = {
    methods: {
        /**
         * @param {*} jsonData
         * @description 绘制折线
         */
        handlePolyline() {
            const { map } = this
            if (this.current.length > 0) {
                // this.clearMap();
            }

            // 判断数据来源格式
            var latlngs = [];
            // 传入数组
            if (this.isArray(this.jsonData)) {
                if (this.isTwoDimensionalArray(this.jsonData) && this.jsonData.length > 1) {
                    // 二维数组
                    latlngs = this.jsonData;
                } else {
                    // 一维数组
                    if (this.jsonData.length > 0 && this.jsonData[0].hasOwnProperty('coordinates')) {
                        let key = ''
                        for (let i = 0; i < this.jsonData.length; i++) {
                            if (this.jsonData[i].hasOwnProperty('coordinates')) {
                                key = 'coordinates'
                            }
                            let coordinates = this.jsonData[i]['coordinates'];
                            // 检查数组深度，如果大于 2，则降低深度，如果不足 2，则增加深度
                            if (this.getArrayDepth(coordinates) > 2) {
                                coordinates = coordinates.flat();
                            }
                            // 遍历输入值，整理数据格式
                            for (let j = 0; j < coordinates.length; j++) {
                                if (this.isLongitude(coordinates[j][0]) && this.isLatitude(coordinates[j][1])) {
                                    latlngs = this.changeLatLng(coordinates);
                                } else {
                                    latlngs.push(coordinates[j]);
                                }
                            }
                            const polyline = L.polyline(latlngs, { color: 'rgba(255, 0, 0, 0.8)', weight: 3, fill: false }).addTo(map);
                            map.fitBounds(polyline.getBounds());
                        }
                        return
                    } else {
                        return this.$message.error('多段折线数据格式错误');
                    }
                }
            }
            // 传入对象
            if (this.isObject(this.jsonData)) {
                let key = ''
                if (this.jsonData.hasOwnProperty('coordinates')) {
                    key = 'coordinates'
                }
                let coordinates = this.jsonData[key];
                // 检查数组深度，如果大于 2，则降低深度，如果不足 2，则增加深度
                if (this.getArrayDepth(coordinates) > 2) {
                    coordinates = coordinates.flat();
                } else if (this.getArrayDepth(coordinates) == 1) {
                    coordinates = [coordinates];
                }
                // 遍历输入值，整理数据格式
                for (let j = 0; j < coordinates.length; j++) {
                    if (this.isLongitude(coordinates[j][0]) && this.isLatitude(coordinates[j][1])) {
                        latlngs = this.changeLatLng(coordinates);
                    } else {
                        latlngs.push(coordinates[j]);
                    }
                }
            }

            var polyline = L.polyline(latlngs, { color: 'rgba(255, 0, 0, 0.8)', weight: 3, fill: false }).addTo(map);
            // this.current.push(polyline);
            map.fitBounds(polyline.getBounds());
        }
    },
}