<template>
    <div class="detail">
      <!-- 搜索条件 -->
      <div class="search-info">
        <div class="search-row">
          <!-- 车牌VIN码 -->
          <div class="search-item">
            <div class="search-title">车辆VIN码:</div>
            <el-input
              class="w_200"
              v-model="tableQuery.carVIN"
              size="small"
              placeholder="请输入"
            />
          </div>
          <!-- 日期 -->
          <div class="search-item">
            <div class="search-title">日期:</div>
            <el-date-picker
                class="w_200"
                v-model="tableQuery.date"
                size="small"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
          </div>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleSearch">查询</el-button>
          <el-button type="primary" icon="el-icon-refresh-left" size="small" @click="handleReset">重置</el-button>
          <el-button v-if="activeName == 'editFence'" type="primary" icon="el-icon-plus" size="small" @click="handleAddFence">新增围栏</el-button>
          <el-button v-if="activeName == 'editFence'" type="primary" icon="el-icon-upload2" size="small">导入围栏</el-button>
        </div>
      </div>
      <div class="separator">详情信息</div>
      <!-- 高排区域地图 -->
      <el-tabs v-model="activeName" @tab-click="handleClick" type="card" style="height: 100%;">
        <el-tab-pane label="电子围栏" name="fence" style="height: 100%;">
          <div
            class="map-container"
            v-loading="loading"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
          >
            <my-map
              v-if="isShow && activeName !== 'editFence'"
              ref="myMap"
              previousPage="fence"
              :isPolyline="isPolyline"
              :polyLineArr="polyLineArr"
              :isFence="isFence"
              :fenceData="fenceData"
              :defaultCenter="defaultCenter"
              :defaultZoom="11"
              @handleGetFence="handleGetFence"
            ></my-map>
          </div>
        </el-tab-pane>
        <el-tab-pane label="车辆列表" name="list" style="height: 100%;">
          <my-table
            v-if="activeName !== 'fence'"
            :columns="columns"
            :tableData="tableData"
            :operate="operate"
            @handleLocation="handleLocation"
          ></my-table>
        </el-tab-pane>
        <el-tab-pane label="编辑电子围栏" name="editFence" style="height: 100%;">
          <div class="edit-fence-tab">
            <div
              class="edit-fence-container"
              v-loading="loading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.8)"
            >
              <my-map
                v-if="isShow && activeName == 'editFence'"
                ref="myMap"
                previousPage="fence"
                :autoBackCenter="false"
                :isEditFence="isEditFence"
                :isFence="isFence"
                :fenceData="fenceData"
                :defaultCenter="defaultCenter"
                :defaultZoom="9"
              ></my-map>
            </div>
            <div class="fence-list">
              <div class="fenc-item">
                <span>围栏 A</span>
                <div class="btns">
                  <el-button type="primary" size="mini" @click="handleEditFence('1', 'edit')">编辑</el-button>
                  <el-button type="success" size="mini" @click="handleEditFence('1', 'commit')">完成</el-button>
                </div>
              </div>
              <div class="fenc-item">
                <span>围栏 B</span>
                <div class="btns">
                  <el-button type="primary" size="mini" @click="handleEditFence('2', 'edit')">编辑</el-button>
                  <el-button type="success" size="mini" @click="handleEditFence('2', 'commit')">完成</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- 弹窗 -->
      <el-dialog
        title="新增围栏"
        :visible.sync="dialogVisible"
        width="60%"
        :before-close="handleClose"
      >
        <el-form ref="form" :model="form" label-width="80px">
          <el-form-item label="围栏名称">
            <el-input class="form-item-input" v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="行政区域">
            <el-cascader
              class="form-item-input"
              v-model="form.area"
              :collapse-tags="true"
              filterable
              :options="areaOption"
              :props="{
                multiple: true,
                checkStrictly: true,
                value: 'code',
                label: 'name',
                children: 'children',
              }"
              clearable
              @change="handleBlur"
            ></el-cascader>
          </el-form-item>
          <el-form-item>
            <div class="addFenceMap">
              <my-map
                id="2"
                ref="myMap2"
                :destroy-on-close="true"
                previousPage="fence"
                :autoBackCenter="false"
                :isEditFence="true"
                :isFence="true"
                :fenceData="newFence"
                :defaultCenter="newFenceCenter"
                :defaultZoom="11"
              ></my-map>
            </div>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleCommit">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </template>
  
  
  <script>
  // 公共方法
  import { getTime } from "@/utils/index"
  import { createGps } from "@/utils/transform"
  // Api
  import { getFenceMapData } from "@/api/maps/map";
  // 导入组件
  import myMap from '@/components/amap/myMap'
  import myTable from '@/components/table/index'
  // mock
  import fence from './fence.json'
  import fence2 from './fence2.json'
  import path from './path.json'
  import country from '../../mock/country.json'
  
  
  const defaultDate = [new Date(getTime('start')), getTime()]
  const defaultQuery = {
    carVIN: '',
    date: defaultDate
  }
  export default {
    name: "Fence",
    components: {
      myMap,
      myTable
    },
    data() {
      return {
        loading: false,
        tableQuery: Object.assign({}, defaultQuery),
        columns: [
          { prop: 'vin', label: 'VIN', width: 200 },
        ],
        tableData: [],
        operate: ['定位'],
        // Tabs 标签页
        activeName: 'fence',
        // 地图 电子围栏 
        isShow: false,
        isEditFence: false,
        isPolyline: true,
        polyLineArr: [],
        isFence: true,
        fenceData: [],
        defaultCenter: [],
        isLocation: false, // 为 true 表示当前是从车辆列表跳转到电子围栏页面，则watch 中不更新数据
        // dialog新增围栏
        dialogVisible: false,
        form: {
          name: '',
          area: '',
        },
        areaOption: country,
        newFence: [],
        newFenceCenter: [],
        isNewFenceShow: false,
      };
    },
    watch: {
      'activeName': function(val) {
        if (val == 'editFence') {
          this.isLocation = false
        }
        if (val !== 'fence' && val !== 'editFence') {
          this.isShow = false
        } else {
          if (!this.isLocation) {
            this.handleSearch()
          }
          this.isShow = true
        }
      }
    },
    created() {
      this.handleSearch()
    },
    mounted() {
    },
    methods: {
      handleSearch() {
        this.handleGetFence()
        this.handlePolyLine()
        this.handleTableData()
      },
      handleReset() {
        this.isLocation = false
        this.tableQuery = Object.assign({}, defaultQuery)
        this.handleSearch()
      },
      handleTableData() {
        let path_ = JSON.parse(JSON.stringify(path))
        this.tableData = [] // 重置 table
        for (let i=0; i<path_.length; i++) {
          this.tableData.push(
            {
              vin: path_[i]?.vin
            }
          )
        }
      },
      // 查询电子围栏数据
      handleGetFence() {
        this.loading = true
        this.fenceData = [] // 重置围栏
        getFenceMapData({
          type: '1',
          params2: 2,
          params3: 3
        }).then(res => {
          // this.fenceData = res?.data
          const coordinates = JSON.parse(JSON.stringify(fence.features[0].geometry.coordinates[0]))
          this.defaultCenter = coordinates[0]
          this.fenceData.push([coordinates])
          this.fenceData.push([fence2.coordinates])
          this.loading = false
          this.isShow = true
        }).catch(err => {
          this.loading = false
        })
      },
      // 编辑电子围栏
      handleEditFence(id, type) {
        if (type == 'edit') {
          this.fenceData = []
          const coordinates = JSON.parse(JSON.stringify(fence.features[0].geometry.coordinates[0]))
          this.isEditFence = true
          if (id == 1) {
            this.defaultCenter = coordinates[0]
            this.fenceData.push([coordinates])
            this.$nextTick(() => {
              this.$refs.myMap.createMap()
            })
          }
          if (id == 2) {
            this.defaultCenter = coordinates[0]
            this.fenceData.push([fence2.coordinates])
            this.$nextTick(() => {
              this.$refs.myMap.createMap()
              //
              setTimeout(() => {
                const dom = document.getElementsByClassName('amap-icon')
                for (let i=0; i<dom.length; i++) {
                  dom[i].getElementsByTagName('img')[0].style.width = '10px'
                  dom[i].getElementsByTagName('img')[0].style.height = 'auto'
                }
              }, 300)
            })
          }
          return
        }
        if (type == 'commit') {
          this.isEditFence = false
          const coordinates = JSON.parse(JSON.stringify(fence.features[0].geometry.coordinates[0]))
          this.defaultCenter = coordinates[0]
          this.fenceData.push([coordinates])
          this.fenceData.push([fence2.coordinates])
          return
        }
      },
      // 进入电子围栏的的车辆
      handlePolyLine() {
        let path_ = JSON.parse(JSON.stringify(path))
        this.polyLineArr = [] // 重置车辆路径
        for (let i=0; i<path_.length; i++) {
          let arr = []
          for (let j=0; j<path_[i].location.length; j++) {
            arr.push(
              [
                createGps(
                  { longitude: path_[i].location[j].lon, latitude: path_[i].location[j].lat }
                )[0],
                createGps(
                  { longitude: path_[i].location[j].lon, latitude: path_[i].location[j].lat }
                )[1]
              ]
            )
          }
          path_[i].location = arr
          this.polyLineArr.push(arr)
        }
      },
      handleLocation(row) {
        this.isLocation = true
        let path_ = JSON.parse(JSON.stringify(path))
        this.polyLineArr = []
        for (let i=0; i<path_.length; i++) {
          let arr = []
          if (path_[i]?.vin !== row?.vin) continue
          for (let j=0; j<path_[i].location.length; j++) {
            arr.push(
              [
                createGps(
                  { longitude: path_[i].location[j].lon, latitude: path_[i].location[j].lat }
                )[0],
                createGps(
                  { longitude: path_[i].location[j].lon, latitude: path_[i].location[j].lat }
                )[1]
              ]
            )
          }
          path_[i].location = arr
          this.polyLineArr.push(arr)
          setTimeout(() => {
            this.$refs.myMap.runMarker()
          }, 500)
        }
        this.activeName = 'fence'
        this.isShow = true
      },
      handleClick(e) {
        // console.log(e)
      },
      handleAddFence() {
        this.dialogVisible = true
        this.$nextTick(() => {
          this.$refs.myMap2.createMap()
        })
      },
      handleClose() {
        this.tableQuery = Object.assign({}, defaultQuery);
        this.form = {
          name: '',
          area: '',
        };
        this.$nextTick(() => {
          this.newFence = [];
          this.newFenceCenter = [];
          this.isNewFenceShow = false
          this.dialogVisible = false
        })
      },
      handleBlur() {
        this.isNewFenceShow = true
        const newFence = [
          [116.362209, 39.887487],
          [116.422897, 39.878002],
          [116.372105, 39.90651]
        ];
        this.newFence = [newFence];
        this.newFenceCenter = newFence[0]
        this.$nextTick(() => {
          this.$refs.myMap2.createMap()
        })
      },
      handleCommit() {
        this.handleClose()
      }
    }
  };
  </script>
  
  <style scoped lang="scss">
  .detail {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 20px 20px;
    display: flex;
    flex-direction: column;
    .search-info {
      width: 100%;
      height: auto;
      box-sizing: border-box;
      margin-bottom: 10px;
      .search-row {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        margin-bottom: 10px;
      }
      .search-item {
        width: auto;
        display: flex;
        align-items: center;
        margin-right: 10px;
        .search-title {
          width: auto;
          min-width: 100px;
          font-size: 13px;
          text-align: right;
          color: #000;
          box-sizing: border-box;
          padding-right: 5px;
        }
      }
    }
    .separator {
      width: 100%;
      font-size: 13px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .edit-fence-tab {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      padding-left: 5px;
      padding-right: 5px;
      padding-bottom: 10px;
      box-sizing: border-box;
      .fence-list {
        width: 300px;
        height: calc(100vh - 290px);
        overflow-y: auto;
        margin-left: 10px;
        border-radius: 6px;
        box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.3);
        .fenc-item {
          width: 100%;
          padding: 10px 10px;
          box-sizing: border-box;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
          display: flex;
          justify-content: space-between;
          .btns {
            display: flex;
            justify-content: flex-end;
          }
        }
        .fenc-item:hover {
          cursor: pointer;
          box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.1);
        }
      }
    }
    .map-container {
      width: 100%;
      height: calc(100vh - 290px);
      box-sizing: border-box;
    }
    .edit-fence-container {
      width: 100%;
      height: calc(100vh - 290px);
      box-sizing: border-box;
      overflow: hidden;
      border-radius: 6px;
      box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.3);
    }
    .w_200 {
      width: 200px;
    }
    // 新增围栏
    .form-item-input {
      width: 220px;
    }
    .addFenceMap {
      width: 100%;
      height: 400px;
    }
  }
  .map-container ::v-deep .amap-icon img, .amap-marker-content img {
      width: 25px;
      height: 20px;
  }
  // ::v-deep .amap-icon img, .amap-marker-content img {
  //     width: 10px;
  //     height: auto;
  // }
  </style>
  <style>
    .el-tabs__content {
      width: 100%;
      height: calc(100% - 60px);
    }
  </style>
  