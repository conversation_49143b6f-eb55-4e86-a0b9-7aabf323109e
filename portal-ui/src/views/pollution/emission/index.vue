<template>
  <div class="container">
    <div class="left">
      <div
        class="map"
        v-loading="loading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <my-map
          v-if="isShow"
          :radius="20"
          :isHeatmap="isHeatmap"
          :hotMapData="hotMapData"
          @handleGetHotMap="handleGetHotMap"
        ></my-map>
      </div>
    </div>
    <div class="right">
      <div class="car-list-title">碳排放热点道路列表</div>
      <!-- 筛选条件 -->
      <div class="filiter-item">
        <el-date-picker
          class="el-date-picker"
          v-model="date"
          size="mini"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>
      <!-- 车辆列表 -->
      <div class="car-list">
        <el-table
          :data="tableData"
          style="width: 100%">
          <el-table-column
            prop="road"
            label="道路名"
          ></el-table-column>
          <el-table-column
            prop="rate"
            label="流量(%)"
          ></el-table-column>
        </el-table>
        <!-- <div
          class="car-list-item"
          v-for="item in carArr"
          :key="item.id"
        >
          <div>{{ '车辆-' + item.label }}</div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
// Api
import {
  getHotMapData,
  getTrajectoryData
} from "@/api/maps/map";
// 公共方法
import { getTime } from "@/utils/index"
import { createGps } from "@/utils/transform"
// 导入组件
import myMap from '@/components/amap/myMap'

const defaultDate = [new Date(getTime('start')), getTime()]
export default {
  name: "trajectory",
  components: {
    myMap
  },
  data() {
    return {
      loading: false,
      // 热力图数据
      isHeatmap: true,
      hotMapData: [],
      // 轨迹点
      lineArr: [],
      // 是否展示菜单
      isShow: false,
      // 筛选条件-日期
      date: defaultDate,
      // 重点通道列表
      tableData: [
        {
          id: 123,
          rate: '36%',
          road: '北三环'
        },
        {
          id: 456,
          rate: '26%',
          road: '东三环'
        },
        {
          id: 789,
          rate: '16%',
          road: '西三环'
        },
        {
          id: 456,
          rate: '26%',
          road: '东三环'
        },
        {
          id: 789,
          rate: '16%',
          road: '西三环'
        },
        {
          id: 456,
          rate: '26%',
          road: '东三环'
        },
        {
          id: 789,
          rate: '16%',
          road: '西三环'
        },
        {
          id: 456,
          rate: '26%',
          road: '东三环'
        },
        {
          id: 789,
          rate: '16%',
          road: '西三环'
        },
        {
          id: 456,
          rate: '26%',
          road: '东三环'
        },
        {
          id: 789,
          rate: '16%',
          road: '西三环'
        },
        {
          id: 456,
          rate: '26%',
          road: '东三环'
        },
        {
          id: 789,
          rate: '16%',
          road: '西三环'
        },
        {
          id: 456,
          rate: '26%',
          road: '东三环'
        },
        {
          id: 789,
          rate: '16%',
          road: '西三环'
        },
        {
          id: 456,
          rate: '26%',
          road: '东三环'
        },
        {
          id: 789,
          rate: '16%',
          road: '西三环'
        }
      ],
      // 当前车辆详细信息
      currentCarInfo: {}
    };
  },
  created() {
    this.handleGetHotMap();
    this.handleTrajectoryData();
  },
  mounted() {
  },
  methods: {
    // 查询热力图层数据
    handleGetHotMap(data) {
      this.loading = true
      getHotMapData({
        params1: 1,
        params2: 2,
        params3: 3
      }).then(res => {
        this.hotMapData = res?.data
        this.loading = false
        this.isShow = true
      }).catch(err => {
        this.loading = false
      })
    },
    // 查询轨迹数据
    handleTrajectoryData(data) {
      getTrajectoryData({
        params1: 1,
        params2: 2,
        params3: 3
      }).then(res => {
        this.lineArr = res?.data || []
        this.isShow = true
      }).catch(err => {

      })
    }
  }
};
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: calc(100vh - 85px);
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px 20px;
  display: flex;
  justify-content: center;
  .left {
    width: calc(100% - 300px);
    height: 100%;
    padding-right: 10px;
    box-sizing: border-box;
    .map {
      width: 100%;
      height: 100%;
      margin-bottom: 10px;
      box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.3);
    }
  }
  .right {
    width: 300px;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.3);
    .car-list-title {
      width: 100%;
      height: 32px;
      line-height: 32px;
      color: #fff;
      text-align: center;
      background-color: rgba(64, 158, 255, 1);
      margin-bottom: 5px;
    }
    .filiter-item {
      width: 100%;
      height: auto;
      .el-date-picker {
        width: 100%;
      }
    }
    .car-list {
      width: 100%;
      height: 100%;
      padding: 10px 10px;
      box-sizing: border-box;
      overflow-y: auto;
      .car-list-item {
        width: 100%;
        height: auto;
        font-size: 13px;
        padding: 10px 10px;
        box-sizing: border-box;
      }
      .car-list-item:hover {
        cursor: pointer;
        color: #fff;
        padding-left: 5px;
        background-color: rgba(64, 158, 255, 0.4);
        box-shadow: 5px 5px 5px rgba(64, 158, 255, 0.6);
        
      }
    }
  }
}
</style>