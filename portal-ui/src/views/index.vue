<template>
  <div class="home">
    <!-- 底层地图 -->
    <div class="view-map">
      <my-map
        v-if="isShowHome"
        id="1"
        :isDeep="true"
        :isZoomEnable="false"
        :isResizeEnable="false"
        :isDragEnable="true"
        :isLocation="true"
        :isDashboard="isDashboard"
        :areaMarkerData="areaMarkerData"
        :nationwide="nationwide"
        :defaultCenter="defaultCenter"
        :defaultZoom="defaultZoom"
        @handleMapEvent="handleMapEvent"
      ></my-map>
    </div>
    <!-- 标题 -->
    <div class="head">
      <div class="title-box">
        <span class="main-title">成都创新研究院移动源远程在线监控平台</span>
        <span id="clock" class="today"></span>
      </div>
    </div>
    <!-- 左侧图表 -->
    <div class="view-left">
      <div class="echart-box view-summary no_bg_color">
        <div class="data-sum-col">
          <div class="data-sum-title">车辆总数</div>
          <div class="data-sum-title">(辆)</div>
          <div class="data-sum-value">{{ allCarCount }}</div>
        </div>
        <div class="data-sum-col">
          <div class="data-sum-title">当日上线总数</div>
          <div class="data-sum-title">(辆)</div>
          <div class="data-sum-value">{{ onlineCarSumCount }}</div>
        </div>
        <div class="data-sum-col">
          <div class="data-sum-title">当日总行驶里程</div>
          <div class="data-sum-title">(公里)</div>
          <div class="data-sum-value">{{ oneDayCarSumMileage }}</div>
        </div>
      </div>
      <!-- echart展示项 -->
      <div class="echart-box">
        <echartRace
        v-if="isOnlineTop"
        :id="1"
        titleText="Top5省上线车辆数"
        titleColor="#FFFFFF"
        :data="carsNumData"
        :labels="carsLabels"
        ></echartRace>
        <el-empty v-else description="暂无数据"></el-empty>
      </div>
      <div class="echart-box">
        <echart-bar :id="2" barType="valueX" :xAxisData="xAxisData" :seriesData="seriesData" titleText="Top5碳排放"
          titleColor="#FFFFFF" axisLabelColor="#FFFFFF"></echart-bar>
      </div>
    </div>
    <!-- 右侧轮播表格 -->
    <div class="slider-date">
      <el-slider
        v-model="sliderValue"
        :min="1"
        :max="7"
        show-stops
        :show-tooltip="false"
        :marks="marks"
        :format-tooltip="formatTooltip"
        @change="handleSliderChange"
      >
      </el-slider>
    </div>
    <div class="view-right" v-if="false">
      <div class="echart-box" style="overflow-x: hidden; padding: 0px 0px;">
        <template v-if="list && list.length > 0">
          <div class="ecahrt-box-title">NOX高排车辆数</div>
          <!-- 滚动列表 -->
          <div class="echart-box-list"
            @mouseover="handleStop"
            @mouseleave="handleRun"
          >
            <div :class="{ anim: animate === true }">
              <div v-for="(item, index) in list" :key="index" class="list-item">
                <div class="list-item-date">{{ '2024-10-28' }}</div>
                <div class="list-item-label">
                  <el-tooltip class="item" effect="dark" :content="item.name" placement="left">
                    <span>{{ item.name }}</span>
                  </el-tooltip>
                </div>
                <div class="list-item-value">{{ item.cnt }}(辆)</div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <el-empty description="暂无数据"></el-empty>
        </template>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      title="区域交通健康详情"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :before-close="handleClose"
      :destroy-on-close="true"
      width="90%"
    >
      <!-- 地图详情 -->
      <div
        v-loading="loading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        class="dialog-container"
      >
        <!-- 健康 -->
        <div class="dialog-container-title">
          <i class="el-icon-first-aid-kit"></i>
          健康详情
        </div>
        <div class="dialog-container-echarts">
          <div class="dialog-echarts-query" v-if="false">
            <span>按时间查询：</span>
            <el-radio-group v-model="targetQuery.date">
              <el-radio label="24h">近24小时</el-radio>
              <el-radio label="7d">近7天</el-radio>
            </el-radio-group>
          </div>
          <div class="dialog-echarts-query">
            <span>按指标查询：</span>
            <el-radio-group v-model="targetQuery.target">
              <el-radio label="all">近7日上线总数(辆)</el-radio>
              <el-radio label="nox">Nox高排车辆数</el-radio>
              <el-radio label="oil">总油耗</el-radio>
            </el-radio-group>
          </div>
          <div class="dialog-echarts-data">
            <div class="echarts-line">
              <echart-line></echart-line>
            </div>
            <div class="echarts-radar">
              <echart-radar
                :id="3"
                titleText="雷达图"
                titleColor="#fff"
              ></echart-radar>
            </div>
          </div>
        </div>
        <!-- 区域 -->
        <div class="dialog-container-title">
          <i class="el-icon-medal-1"></i>
          区域当日上线车辆排名
        </div>
        <div class="dialog-container-table">
          <div class="dialog-table-data">
            <div class="table-container">
              <!-- 表头 -->
              <div class="dialog-table-head">
                <div class="dialog-table-column">排名</div>
                <div class="dialog-table-column">区域</div>
                <div class="dialog-table-column">在线车辆数</div>
                <div class="dialog-table-column">Nox排放量</div>
                <div class="dialog-table-column">碳排放量</div>
              </div>
              <!-- 数据 -->
              <div class="dialog-table-body">
                <div
                  v-for="(item, index) in areaList"
                  :class="selectedId == (index+1) ? 'dialog-table-body-row active' : 'dialog-table-body-row'"
                  :key="index"
                  @click="handleClickRow(index+1, item)"
                >
                <div class="dialog-table-column">{{ item.id || '--' }}</div>
                  <div class="dialog-table-column">{{ item.name || '--' }}</div>
                  <div class="dialog-table-column">{{ item.count || '--' }}</div>
                  <div class="dialog-table-column">{{ item.nox || '--' }}%</div>
                  <div class="dialog-table-column">{{ item.co2 || '--' }}%</div>
                </div>
              </div>
            </div>
            <div class="table-async-map">
              <my-map
                :isShow="isShow"
                id="2"
                :isZoomEnable="true"
                :isResizeEnable="true"
                :isDragEnable="true"
                :isMenu="false"
                :isLocation="false"
                :autoBackCenter="autoBackCenter"
                :autoBackZoom="autoBackZoom"
                :isFence="isFence"
                :fenceData="fenceData"
                :isMaker="isMaker"
                :markerData="markerData"
                :defaultCenter="defaultCenter"
                :defaultZoom="9"
              ></my-map>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 贴边按钮 -->
    <div
      class="my-dialog"
      v-show="!drawer"
      v-drag
      @mousemove="handleMove"
      @click="handleDrawer"
    >
      <div class="my-dialog__bg">
        <div class="my-dialog__info">筛选</div>
      </div>
    </div>
    <el-drawer
      title="筛选"
      size="300px"
      :visible.sync="drawer"
      direction="rtl"
      :modal="true"
      :wrapperClosable="false"
      :modal-append-to-body="false"
      :destroy-on-close="true"
      :before-close="handleClose"
    >
      <!-- 日期 -->
      <div class="search-item">
        <div class="search-title">日期:</div>
        <el-date-picker
          class="input"
          v-model="pt_date"
          value-format="yyyy-MM-dd"
          size="small"
          type="date"
          placeholder="选择日期"
          :clearable="false"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
      </div>
      <!-- 查询 -->
      <div class="search-item" style="display: flex; justify-content: flex-end;">
        <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
      </div>
    </el-drawer>
  </div>
</template>


<script>
// 图片资源
import summarySVG from '../assets/images/home/<USER>'
// Api
import {
  getConnectedCars,
  getCurDayMetrics,
  getProvinceTop,
} from "@/api/dashboard/index";
import {
  getChinaArea,
} from "@/api/maps/map";
// 公共方法
import { qian } from "@/utils/index"
import { parseTime } from "@/utils/ruoyi"
import { createGps } from "@/utils/transform"
import { mapState } from 'vuex'
// 导入组件
import echartRadar from '@/components/echart/radar/index'
import echartLine from '@/components/echart/line/index'
import echartPie from '@/components/echart/pie/index'
import echartBar from '@/components/echart/bar/index'
import echartRace from '@/components/echart/bar/race'
import echartMap from '@/components/echart/map/index'
import myMap from '@/components/amap/myMap'
import moveTrajectory from '@/components/amap/move'
// mock数据
import provinceMileage from './mock/carMileage/province.json' // 省-里程
import cityMileage from './mock/carMileage/city.json' // 市-里程
import provinceCount from './mock/carCount/province.json' // 省-数量
import cityCount from './mock/carCount/city.json' // 市-数量
import gaopaiSheng from './mock/gaopai/gaopai_sheng.json' // 省-高排
import gaopaiShi from './mock/gaopai/gaopai_shi.json' // 市-高排
import gaopaiQu from './mock/gaopai/gaopai_qu.json' // 区-高排
import jinniu from './mock/area/jinniu.json'
import jinniu2 from './mock/area/jinniu2.json'
import chenghua from './mock/area/chenghua.json'
import jingjiang from './mock/area/jingjiang.json'
import qingyang from './mock/area/qingyang.json'
import wuhou from './mock/area/wuhou.json'
import longquanyi from './mock/area/longquanyi.json'

const defaultDate = parseTime(new Date() - 7 * 24 * 3600 * 1000, '{y}-{m}-{d}')
const defaultQuery = {
  date: '24h',
  target: 'all'
}
const defaultMarks = {
  1: '',
  2: '',
  3: '',
  4: '',
  5: '',
  6: '',
  7: '',
}
let sliderTimer = null
export default {
  name: "Index",
  components: {
    echartRadar,
    echartLine,
    echartPie,
    echartBar,
    echartRace,
    echartMap,
    myMap,
    moveTrajectory
  },
  data() {
    return {
      summarySVG,
      clock: null,
      pt_date: defaultDate,
      pickerOptions: {
        disabledDate(time) {
          const now = new Date();
          const pastDate = new Date(now.getTime() - 7 * 24 * 3600 * 1000); // 7天前的日期
          return time.getTime() > pastDate.getTime();
        },
      },
      allCarCount: 0, // 车辆总数
      onlineCarSumCount: 0, // 当日上线总数
      oneDayCarSumMileage: 0, // 当日总行驶里程
      // 高排滚动列表
      list: [],
      animate: false,
      intervalId: null,
      loading: false,
      isShowHome: false,
      isShow: false,
      // 是否显示围栏区域
      isFence: true,
      // 围栏区域
      fenceData: [],
      // 是否显示marker标记点
      isMaker: true,
      // marker标记点数据
      markerData: [],
      // 是否是首页省市Marker
      isDashboard: true,
      // 省市Marker点
      areaMarkerData: [],
      // 地图中心位置坐标 
      defaultCenter: [],
      // 地图默认缩放层级 
      defaultZoom: 4,
      // 是否自动回到预设中心点
      autoBackCenter: true,
      // 是否自动返回预设缩放层级
      autoBackZoom: true,
      // 全国行政边界
      nationwide: [],
      // 弹窗
      dialogVisible: false,
      drawer: false,
      targetQuery: Object.assign({}, defaultQuery),
      areaList: [
        { id: 1, name: '锦江区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 2, name: '武侯区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 3, name: '金牛区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 4, name: '成华区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 5, name: '青羊区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 6, name: '高新区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 7, name: '龙泉驿区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 8, name: '郫都区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 9, name: '新都区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 10, name: '温江区', count: 30300, nox: '29.32', co2: '36.12' },
        { id: 11, name: '新津区', count: 30300, nox: '29.32', co2: '36.12' },
      ],
      selectedId: '', // 当前选中行
      // TOP5省上线车辆数
      carsNumData: [],
      carsLabels: [],
      isOnlineTop: false,
      // TOP5总油耗
      xAxisData: ['企业1', '企业2', '企业3', '企业4', '企业5'],
      seriesData: [100, 200, 300, 400, 500],
      isDragging: false,
      clickTimer: null,
      sliderValue: 1,
      marks: Object.assign({}, defaultMarks),
    };
  },
  watch: {
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  created() {
    this.intervalId = setInterval(this.scroll, 1500);
    this.handleSearch();
  },
  mounted() {
    // 启用动态时钟
    this.clock = setInterval(this.updateClock, 1000);

    // 添加拖拽结束的监听事件
    document.addEventListener('mouseup', this.handleMouseUp);
  },
  methods: {
    formatTooltip(index) {
      const date = this.getLast7DaysDates().reverse()
      this.marks = Object.assign({}, defaultMarks);
      this.marks[index] = date[index - 1];
      return date[index - 1]
    },
    getLast7DaysDates() {
      let dates = [];
      for (let i = 0; i < 7; i++) {
          let date = new Date(this.pt_date);
          date.setDate(date.getDate() - i);
          dates.push(date.toISOString().split('T')[0]);
      }
      return dates;
    },
    handleSliderChange(index) {
      this.sliderValue = index
      clearTimeout(sliderTimer);
      this.handleAreaMarker();
    },
    handleSearch() {
      // 重置条件
      clearTimeout(sliderTimer);
      this.sliderValue = 1
      // 查询
      this.handleSummaryData();
      this.handleOnlineTop5();
      this.handleAreaMarker();
      this.handleArea();
    },
    // 获取全国省市Marker位置和信息 
    handleAreaMarker() {
      const { pt_date } = this
      const date = this.getLast7DaysDates().reverse()
      // 获取全国行政区域边界
      this.getLast7DaysDates().reverse()[0]
      getChinaArea({
        pt_date: date[this.sliderValue - 1]
      }).then(res => {
        this.nationwide = []
        this.areaMarkerData = []
        this.defaultCenter = [101.778916, 36.623178]
        const base = res?.data || []
        for (let i=0; i<base.length; i++) {
          const center = JSON.parse(base[i].center)
          if (center && center.length > 0) {
            this.nationwide.push(base[i])
            this.areaMarkerData.push(
              {
                lng: center[0],
                lat: center[1],
                en_name: base[i].name,
                title: base[i].name,
                cnt: base[i].online_num,
              }
            )
          }
        }
        // 轮询查询其他日期
        sliderTimer = setTimeout(() => {
          if (this.sliderValue < 7) {
            this.sliderValue++
            this.handleAreaMarker();
          } else {
            this.sliderValue = 1
            this.handleAreaMarker();
          }
        }, 10000)
      }).catch(err => {
        this.$message.error(err)
      }).finally(() => {
        this.isShowHome = true
      })
    },
    // 获取行政区域
    handleArea() {
      // 各个行政区域数据标记
      this.markerData = [
        { lon: 104.083802, lat: 30.64321, title: '锦江区:30300' },
        { lon: 104.100854, lat: 30.676437, title: '成华区:30300' },
        { lon: 104.044868, lat: 30.703611, title: '金牛区:30300' },
        { lon: 104.042251, lat: 30.677282, title: '青羊区:30300' },
        { lon: 104.020217, lat: 30.640092, title: '武侯区:30300' },
        { lon: 104.248317, lat: 30.598428, title: '龙泉驿区:30300' },
      ]
      // 行政区域边界
      this.fenceData.push(jinniu.coordinates)
      this.fenceData.push(jinniu2.coordinates)
      this.fenceData.push(chenghua.coordinates)
      this.fenceData.push(jingjiang.coordinates)
      this.fenceData.push(qingyang.coordinates)
      this.fenceData.push(wuhou.coordinates)
      this.fenceData.push(longquanyi.coordinates)
    },
    handleSummaryData() {
      const { pt_date } = this
      // 车辆总数
      getConnectedCars().then(res => {
        this.allCarCount = res?.data || 0
        if (this.allCarCount == 0) return
        this.numberScroll('allCarCount', (this.allCarCount-100), this.allCarCount, 300)
      }).catch(err => {
        this.$message.error(err)
      })
      // 当日上线总数、当日总行驶里程
      getCurDayMetrics({
        pt_date,
      }).then(res => {
        this.onlineCarSumCount = res?.data?.online_cars || 0
        this.numberScroll('onlineCarSumCount', (this.onlineCarSumCount > 0 ? this.onlineCarSumCount-100 : 0), this.onlineCarSumCount, 300)

        this.oneDayCarSumMileage =  res?.data?.drive_odomoter || 0
        this.numberScroll('oneDayCarSumMileage', (this.oneDayCarSumMileage > 0 ? this.oneDayCarSumMileage-100 : 0), this.oneDayCarSumMileage, 300)
      }).catch(err => {
        this.$message.error(err)
      })
    },
    // TOP5省上线车辆数
    handleOnlineTop5() {
      const { pt_date } = this
      this.carsNumData = []
      this.carsLabels = []
      this.isOnlineTop = false
      getProvinceTop({
        pt_date,
      }).then(res => {
        if (res?.data && res?.data.length == 0) return
        for (let i=0; i<res.data.length; i++) {
          this.carsNumData.push(res.data[i]?.cnt)
          this.carsLabels.push(res.data[i]?.province_name)
        }
        this.isOnlineTop = true
      }).catch(err => {
        this.$message.error(err)
      })
    },
    // 滚动列表
    scroll() {
      this.animate = true
      setTimeout(() => {
        this.list.push(this.list[0])
        this.list.shift()
        this.animate = false
      }, 500)
    },
    handleRun() {
      this.intervalId = setInterval(this.scroll, 1500)
    },
    handleStop() {
      this.animate = false
      clearInterval(this.intervalId)
    },
    // 筛选按钮拖拽
    handleMove() {
      // 设置拖拽状态为true
      this.isDragging = true;
    },
    handleDrawer() {
      // 创建计时器，如果是点击，则执行点击事件
      this.clickTimer = setTimeout(() => {
        if (!this.isDragging) {
          // 点击事件的处理逻辑
          this.drawer = true
        }
      }, 100); // 设置点击的最大延迟时间
    },
    handleMouseUp() {
      // 当拖拽操作结束时，清除计时器并设置拖拽状态为false
      clearTimeout(this.clickTimer);
      this.isDragging = false;
    },
    // 弹窗
    handleMapEvent(range, key, level) {
      // 打开弹窗，查看详情行政区县地图
      this.loading = true
      this.dialogVisible = true
      if(this.sidebar.opened) {
        this.$store.dispatch('app/toggleSideBar')
      }
      setTimeout(() => {
        this.isShow = true
        this.loading = false
      }, 100)
    },
    handleClose() {
      this.dialogVisible = false;
      this.drawer = false;
      this.isShow = false
    },
    // 数字滚动
    numberScroll(key, startNumber, endNumber, duration) {
      let currentNumber = startNumber;
      const increment = (endNumber - startNumber) / duration;
      const interval = setInterval(() => {
        currentNumber += increment;
        this[key] = qian(Math.round(currentNumber))
        if (currentNumber >= endNumber) {
          clearInterval(interval);
        }
      }, 1);
    },
    // 动态时钟
    updateClock() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      month = month < 10 ? '0' + month : month
      day = day < 10 ? '0' + day : day
      hours = hours < 10 ? '0' + hours : hours;
      minutes = minutes < 10 ? '0' + minutes : minutes;
      seconds = seconds < 10 ? '0' + seconds : seconds;
      let time = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
      document.getElementById('clock').textContent = time;
    },
    // dialog 弹窗中，选中行
    handleClickRow(index, item) {
      if (this.selectedId == index) {
        index = ''
      }
      this.selectedId = index
      // 重置选中记录
      const dom = document.getElementsByClassName('dialog-table-body-row')
      // 地图联动
      this.fenceData = []
      this.markerData = []
      if (this.selectedId == '') {
        this.handleArea()
        return
      }
      switch (item.name) {
        case '锦江区':
          this.fenceData.push(jingjiang.coordinates)
          this.defaultCenter = [104.083802,30.64321]
          this.markerData = [
            { lon: 104.083802, lat: 30.64321, title: '锦江区:30300' }
          ]
          break;
        case '成华区':
          this.fenceData.push(chenghua.coordinates)
          this.defaultCenter = [104.100854,30.676437]
          this.markerData = [
            { lon: 104.100854, lat: 30.676437, title: '成华区:30300' }
          ]
          break;
        case '金牛区':
          this.fenceData.push(jinniu.coordinates)
          this.fenceData.push(jinniu2.coordinates)
          this.defaultCenter = [104.044868,30.703611]
          this.markerData = [
            { lon: 104.044868, lat: 30.703611, title: '金牛区:30300' }
          ]
          break;
        case '青羊区':
          this.fenceData.push(qingyang.coordinates)
          this.defaultCenter = [104.042251,30.677282]
          this.markerData = [
            { lon: 104.042251, lat: 30.677282, title: '青羊区:30300' }
          ]
          break;
        case '武侯区':
          this.fenceData.push(wuhou.coordinates)
          this.defaultCenter = [104.020217,30.640092]
          this.markerData = [
            { lon: 104.020217, lat: 30.640092, title: '武侯区:30300' }
          ]
          break;
        case '龙泉驿区':
          this.fenceData.push(longquanyi.coordinates)
          this.defaultCenter = [104.248317,30.598428]
          this.markerData = [
            { lon: 104.248317, lat: 30.598428, title: '龙泉驿区:30300' }
          ]
          break;
      }
    },
    getRandomInt(min, max) {
      min = Math.ceil(min);
      max = Math.floor(max);
      return Math.floor(Math.random() * (max - min + 1)) + min;
    }
  },
  beforeDestroy() {
    clearInterval(this.intervalId)
    clearInterval(this.clock)
    clearTimeout(sliderTimer)

    // 清除计时器和移除监听事件
    clearTimeout(this.clickTimer);
    document.removeEventListener('mouseup', this.handleMouseUp);
  }
};
</script>

<style scoped lang="scss">
.slider-date {
  width: 300px;
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translate(-50%);
}
::v-deep .el-slider__runway {
  background-color: rgba(0, 0, 0, 0.3);
}
::v-deep .el-slider__runway:hover {
  background-color: rgba(0, 0, 0, 0.5);
}
::v-deep .el-slider__bar {
  background-color: rgba(24, 144, 255, 0.4);
}
::v-deep .el-slider__bar:hover {
  background-color: rgba(24, 144, 255, 0.5);
}
::v-deep .el-slider__marks-text {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  width: 100px;
  color: #fff;
  font-weight: bold;
  text-align: center;
}
::v-deep .el-slider__stop {
  background-color: transparent;
}
.home {
  width: 100%;
  min-width: 520px;
  height: calc(100vh - 84px);
  // height: calc(100vh + 100px);
  overflow-x: auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  // padding-top: 10px;
  // padding-right: 20px;
  // padding-left: 20px;
  background: -webkit-radial-gradient(50% 35%, farthest-corner, #034f8e, #034987, #02366d, #002353);
  position: relative;
  z-index: 0;
  // 禁止选中文字
  -webkit-user-select: none; /* Chrome, Safari, Opera */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;

  // 底层地图背景
  .view-map {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    background-color: #1a2028;
  }
  // 标题
  .head {
    width: 100%;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    background-color: rgba(255, 255, 255, 0.2);
    box-sizing: border-box;
    padding-bottom: 5px;

    .title-box {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .main-title {
        width: 100%;
        height: 50px;
        line-height: 50px;
        font-weight: 600;
        text-align: center;
        letter-spacing: 0.2rem;
        font-size: 1.6rem;
        background-image: -webkit-linear-gradient(left, #147B96, #90e1ff 25%, #147B96 50%, #90e1ff 75%, #147B96);
        -webkit-text-fill-color: transparent;
        -webkit-background-clip: text;
        -webkit-background-size: 200% 100%;
        text-shadow: 3px 3px 6px rgba(20, 123, 150, 0.5);
      }
      .today {
        color: #90e1ff;
        font-weight: bold;
        font-size: 1.2rem;
        font-family: Arial, "Times New Roman", Times, Helvetica, "\5b8b\4f53", sans-serif;
      }
    }
  }
  // 右侧图表
  .view-right {
    width: 300px;
    height: auto;
    padding-top: 10px;
    padding-left: 10px;
    padding-right: 10px;
    box-sizing: border-box;
    position: absolute;
    bottom: 20px;
    right: 0px;
    // position: fixed;
    // bottom: 20px;
    // right: 0px;

    // 图表
    .echart-box {
      width: 100%;
      height: 300px;
      padding: 5px 5px;
      box-sizing: border-box;
      border-radius: 6px;
      margin-bottom: 10px;
      box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
      background-color: rgba(256, 256, 256, 0.1);

      .ecahrt-box-title {
        width: 100%;
        height: 35px;
        line-height: 35px;
        color: #FFF;
        font-weight: bold;
        font-size: 17px;
        padding-left: 5px;
        box-sizing: border-box;
        margin-bottom: 5px;
      }

      // 滚动列表
      .echart-box-list {
        width: calc(100% + 15px);
        height: calc(100% - 40px);
        overflow-y: auto;

        .list-item {
          margin-bottom: 4px;
          width: 97%;
          height: 35px;
          line-height: 35px;
          font-size: 12px;
          color: #fff;
          padding-left: 20px;
          padding-right: 40px;
          box-sizing: border-box;
          border-radius: 4px;
          display: flex;
          justify-content: space-between;

          .list-item-label,
          .list-item-date,
          .ist-item-value {
            width: calc(100% / 3);
            overflow: hidden; /* 确保溢出的内容会被隐藏 */
            white-space: nowrap; /* 确保文本在一行内显示，不换行 */
            text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
          }
          .list-item-value {
            color: rgba(216, 100, 248, 0.7);
            font-weight: bold;
          }
        }
        .list-item:hover {
          cursor: pointer;
          box-shadow: 3px 3px 5px rgba(230, 159, 249, 0.3);
          background: linear-gradient(to right, rgba(216, 100, 248, 0.1) 30%, rgba(216, 100, 248, 0.7) 50%) right;
          background-size: 200%;
          transition: .5s ease-in-out;
          font-weight: bold;
          font-size: 13px;
          .list-item-value {
            color: #fff;
          }
        }

        .anim {
          transition: all 0.5s;
          margin-top: -41px;
        }
      }
    }
  }
  // 左侧图表
  .view-left {
    width: 450px;
    height: auto;
    padding-top: 50px;
    padding-left: 10px;
    padding-right: 10px;
    box-sizing: border-box;
    position: absolute;
    bottom: 20px;
    left: 0px;
    // position: fixed;
    // bottom: 20px;
    // left: 50px;

    // 数据总汇
    .view-summary {
      width: 100%;
      height: 100px !important;
      line-height: 20px;
      display: flex;
      justify-content: center;
      .data-sum-col {
        width: calc(500px / 3);
        border-radius: 6px;
        box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
        background-color: rgba(256, 256, 256, 0.1);
        text-align: center;
        padding: 10px 10px;
        padding-top: 20px;
        box-sizing: border-box;
        .data-sum-title {
          color: #FFF;
          font-weight: bold;
          font-size: 0.8rem;
        }
        .data-sum-value {
          font-size: 1.1rem;
          font-weight: bold;
          color: rgba(216, 100, 248, 1);
        }
      }
      .data-sum-col:nth-child(1), .data-sum-col:nth-child(2) {
        margin-right: 10px;
      }
    }
    // 图表
    .echart-box {
      width: 100%;
      height: calc((100vh - 100px) / 3);
      padding: 5px 5px;
      box-sizing: border-box;
      border-radius: 6px;
      margin-bottom: 10px;
      box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
      background-color: rgba(256, 256, 256, 0.1);

      .ecahrt-box-title {
        width: 100%;
        height: 35px;
        line-height: 35px;
        color: #FFF;
        font-weight: bold;
        font-size: 17px;
        padding-left: 5px;
        box-sizing: border-box;
      }
    }
    .no_bg_color {
      padding: 0px 0px;
      background-color: transparent;
    }
  }
  // 弹窗样式
  .dialog-container {
    width: 100%;
    height: calc(100vh - 200px);
    padding-right: 20px;
    box-sizing: border-box;
    overflow-y: auto;
    .dialog-container-title {
      width: 100%;
      border-bottom: 2px #319ee4 solid;
      color: white;
      font-size: 16px;
      padding-bottom: 5px;
      box-sizing: border-box;
    }
    .dialog-container-echarts,
    .dialog-container-table {
      width: 100%;
      height: auto;
      margin-top: 10px;
      margin-bottom: 10px;
      .dialog-echarts-data {
        width: 100%;
        height: 210px;
        margin-top: 10px;
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        .echarts-line {
          width: 65%;
          height: 100%;
          margin-right: 10px;
          border-radius: 6px;
          overflow: hidden;
        }
        .echarts-radar {
          width: 35%;
          height: 100%;
          border-radius: 6px;
          overflow: hidden;
        }
      }
      .dialog-table-data {
        width: 100%;
        height: auto;
        margin-top: 10px;
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        .table-async-map {
          width: 400px;
          height: 400px;
        }
        .table-container {
          width: calc(100% - 400px);
          height: 600px;
          margin-right: 20px;
          color: white;
          font-size: 13px;
          font-weight: bold;
          .dialog-table-head {
            width: 100%;
            height: 43px;
            line-height: 43px;
            text-align: center;
            background: #1f486e;
            border: 1px #06c solid;
            display: flex;
            justify-content: flex-start;
          }
          .dialog-table-body {
            width: 100%;
            height: auto;
            .dialog-table-body-row {
              width: 100%;
              height: 43px;
              line-height: 43px;
              text-align: center;
              border-bottom: 1px #013968 solid;
              border-left: 1px #013968 solid;
              border-right: 1px #013968 solid;
              display: flex;
              justify-content: flex-start;
            }
            .dialog-table-body-row:hover {
              cursor: pointer;
              box-shadow: 3px 3px 5px #1f486e;
              background: linear-gradient(to right, #011a33 30%, #bcdfff77 50%) right;
              background-size: 200%;
              transition: .5s ease-in-out;
            }
            .active {
              color: #319ee4;
              font-size: 15px;
            }
          }
          .dialog-table-column {
            width: calc(100% / 5);
            height: 100%;
          }
        }
      }
    }
    .dialog-echarts-query {
      width: 100%;
      height: auto;
      color: #fff;
      padding-top: 5px;
      padding-bottom: 5px;
      box-sizing: border-box;
      ::v-deep .el-radio {
        color: #fff;
      }
    }
  }
  
  ::v-deep .el-dialog {
    background-color: #011a33;
  }
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
  }
  ::v-deep .el-dialog__title {
    font-weight: bold;
    color: #319ee4;
  }
  ::v-deep .el-dialog__headerbtn .el-dialog__close {
    color: #ffffff;
  }
  ::v-deep .el-dialog__headerbtn .el-dialog__close:hover {
    transform: rotate(20deg);
  }
  /* 修改滚动条滑块颜色 */
  ::v-deep .dialog-container::-webkit-scrollbar-thumb {
    background-color: #319ee4;
  }
  /* 修改滚动条轨道背景色 */
  ::v-deep .dialog-container::-webkit-scrollbar-track {
    background-color: #4b6a89;
    border-radius: 3px;
  }
  ::v-deep .el-statistic .title {
    font-size: 21px;
    font-weight: bold;
    color: #e6e6e6;
  }

  ::v-deep .el-statistic .number {
    font-size: 22px;
    color: #ffc800;
  }

  ::v-deep .el-dialog {
    width: 80%;
  }
  // 设置背景地图左右下角标文字颜色
  ::v-deep #location, ::v-deep .amap-copyright {
    color: #ffffff;
  }
  // 暂无数据
  ::v-deep .el-empty {
    height: 100%;
  }
  ::v-deep .el-empty__image svg {
    opacity: 0.3;
  }
  // 筛选
  .search-item {
    width: auto;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-right: 20px;
    margin-bottom: 10px;
    .search-title {
      width: auto;
      min-width: 70px;
      font-size: 13px;
      text-align: right;
      color: #000;
      box-sizing: border-box;
      padding-right: 5px;
    }
    .input {
      width: 100%;
    }
  }
}
.my-dialog {
  width: 50px;
  height: 50px;
  border-radius:  50%;
  background: #013968;
  position: fixed;
  right: 0px;
  top: 190px;
  z-index: 9999999;
  box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.09);
  text-align: center;
  opacity: 0.6;
  .my-dialog__bg {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(to right, #d864f8, transparent);
    animation: spin 2s linear infinite;
  }
  .my-dialog__info {
    width: 95%;
    height: 95%;
    border-radius: 50%;
    color: #fff;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #013968;
    animation: spin2 2s linear infinite;
  }
}
.my-dialog:hover {
  opacity: 0.8;
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes spin2 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}
</style>