<template>
  <div class="detail">
    <!-- 搜索条件 -->
    <div class="search-info">
      <div class="search-row">
        <!-- 车牌VIN码 -->
        <div class="search-item">
          <div class="search-title">车辆VIN码:</div>
          <el-input
            class="w_200"
            v-model="tableQuery.carVIN"
            size="small"
            placeholder="请输入"
          />
        </div>
        <!-- 日期 -->
        <div class="search-item">
          <div class="search-title">日期:</div>
          <el-date-picker
              class="w_200"
              v-model="tableQuery.date"
              size="small"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
        </div>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleGetMarker">查询</el-button>
        <el-button type="primary" icon="el-icon-refresh-left" size="small" @click="handleGetMarker">重置</el-button>
      </div>
    </div>
    <div class="separator">详情信息</div>
    <!-- 高排区域地图 -->
    <el-tabs v-model="activeName" @tab-click="handleClick" type="card" style="height: 100%;">
      <el-tab-pane label="地图标记点" name="marker" style="height: 100%;">
        <div
          class="map-container"
          v-loading="loading"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <my-map
            v-if="isShow"
            :isMaker="isMaker"
            :markerData="markerData"
            @handleGetMarker="handleGetMarker"
          ></my-map>
        </div>
      </el-tab-pane>
      <el-tab-pane label="油站列表" name="list" style="height: 100%;">
        <div class="table-container">
          <my-table
            v-if="activeName !== 'marker'"
            :columns="columns"
            :tableData="tableData"
            :operate="operate"
            @handleLocation="handleLocation"
          ></my-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>


<script>
// 公共方法
import { getTime } from "@/utils/index"
import { createGps } from "@/utils/transform"
// Api
import { getMarkerMapData } from "@/api/maps/map";
// 导入组件
import myMap from '@/components/amap/myMap'
import myTable from '@/components/table/index'


const defaultDate = [new Date(getTime('start')), getTime()]
const defaultQuery = {
  carVIN: '',
  date: defaultDate
}
export default {
  name: "Detail",
  components: {
    myMap,
    myTable
  },
  data() {
    return {
      loading: false,
      tableQuery: Object.assign({}, defaultQuery),
      columns: [
        { prop: 'id', label: '编号' },
        { prop: 'area', label: '省、市' },
        { prop: 'loaction', label: '地址' },
        { prop: 'lng', label: '经度' },
        { prop: 'lat', label: '纬度' },
      ],
      tableData: [],
      operate: ['定位'],
      // Tabs 标签页
      activeName: 'marker',
      // 地图 marker 标记
      isShow: false,
      isMaker: true,
      markerData: [],
    };
  },
  watch: {
    'activeName': function(val) {
      if (val !== 'marker') {
        this.isShow = false
      } else {
        this.isShow = true
      }
    }
  },
  created() {
    this.handleGetMarker()
  },
  mounted() {
  },
  methods: {
    // 查询热力图层数据
    handleGetMarker() {
      this.loading = true
      getMarkerMapData({
        type: '1',
        params2: 2,
        params3: 3
      }).then(res => {
        this.markerData = res?.data
        // table
        res?.data.forEach(el => {
          this.tableData.push(
            {
              'id': el?.title || '',
              'area': '',
              'loaction': '*********',
              'lng': createGps({ longitude: el?.lon, latitude: el?.lat })[0] || '',
              'lat': createGps({ longitude: el?.lon, latitude: el?.lat })[1] || '',
            }
          )
        });
        this.loading = false
        this.isShow = true
      }).catch(err => {
        this.loading = false
      })
    },
    handleLocation(row) {
      const obj = {
        lon: row?.lng,
        lat: row?.lat,
        title: row?.id
      }
      this.markerData = [obj]
      this.activeName = 'marker'
      this.isShow = true
    },
    handleClick(e) {

    },
  }
};
</script>

<style scoped lang="scss">
.detail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 20px;
  display: flex;
  flex-direction: column;
  .search-info {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    margin-bottom: 10px;
    .search-row {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      margin-bottom: 10px;
    }
    .search-item {
      width: auto;
      display: flex;
      align-items: center;
      margin-right: 10px;
      .search-title {
        width: auto;
        min-width: 100px;
        font-size: 13px;
        text-align: right;
        color: #000;
        box-sizing: border-box;
        padding-right: 5px;
      }
    }
  }
  .separator {
    width: 100%;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .map-container, .table-container {
    width: 100%;
    height: calc(100vh - 290px);
    box-sizing: border-box;
  }
  .w_200 {
    width: 200px;
  }
}
</style>
<style>
  .el-tabs__content {
    width: 100%;
    height: calc(100% - 60px);
  }
</style>
