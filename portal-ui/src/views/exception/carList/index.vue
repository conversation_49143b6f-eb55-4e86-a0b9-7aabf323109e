<template>
  <div class="detail">
    <!-- 搜索条件 -->
    <div class="search-info">
      <div class="search-row">
        <!-- 企业名称 -->
        <div class="search-item">
          <div class="search-title">企业名称:</div>
          <el-input
            class="w_200"
            v-model="tableQuery.company"
            size="small"
            clearable
            placeholder="请输入"
          />
        </div>
        <!-- 省、市 -->
        <div class="search-item">
          <div class="search-title">省、市:</div>
          <el-cascader
            v-model="tableQuery.area"
            :options="areaOption"
            :props="{
              multiple: true,
              checkStrictly: true,
              value: 'code',
              label: 'name',
              children: 'children',
            }"
            clearable
          ></el-cascader>
        </div>
        <el-button type="primary" icon="el-icon-search" size="small">查询</el-button>
        <el-button type="primary" icon="el-icon-refresh-left" size="small">重置</el-button>
      </div>
    </div>
    <div class="separator">详情信息</div>
    <!-- 表格 -->
    <div class="table-container">
      <my-table
        :columns="columns || []"
        :tableData="tableData || []"
        :operate="operate"
      ></my-table>
    </div>
  </div>
</template>


<script>
// 公共方法
import { getTime } from "@/utils/index"
// 导入组件
import myTable from '@/components/table/index'
// mock
import country from '../../mock/country.json'
import carList from './carList.json'

const defaultDate = [new Date(getTime('start')), getTime()]
const defaultQuery = {
  company: '',
  area: ''
}
export default {
  name: "carList",
  components: {
    myTable
  },
  data() {
    return {
      tableQuery: Object.assign({}, defaultQuery),
      areaOption: country,
      columns: [
        {
          prop: "vin",
          label: "VIN码",
        },
        {
          prop: "city",
          label: "省、市",
        },{
          prop: "pt_date",
          label: "加油时间",
        },{
          prop: "nox_mass_mean",
          label: "Nox浓度",
        },{
          prop: "nox_gkwh1",
          label: "比排放",
        },{
          prop: "km",
          label: "加油后行驶里程",
        }
      ],
      tableData: [],
      operate: ['轨迹'],
    };
  },
  created() {
    this.handleSearch()
  },
  mounted() {
  },
  methods: {
    handleSearch() {
      this.handleCarList()
    },
    handleCarList() {
      this.tableData = carList
    }
  }
};
</script>

<style scoped lang="scss">
.detail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 20px;
  .search-info {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    margin-bottom: 10px;
    .search-row {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      margin-bottom: 10px;
    }
    .search-item {
      width: auto;
      display: flex;
      align-items: center;
      margin-right: 10px;
      .search-title {
        width: auto;
        min-width: 100px;
        font-size: 13px;
        text-align: right;
        color: #000;
        box-sizing: border-box;
        padding-right: 5px;
      }
    }
  }
  .separator {
    width: 100%;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .table-container {
    width: 100%;
    height: auto;
    box-sizing: border-box;
  }
  .w_230 {
    width: 230px;
  }
}
</style>
