<template>
  <div class="detail">
    <!-- 搜索条件 -->
    <div class="search-info">
      <div class="search-row">
        <!-- 车牌号码 -->
        <div class="search-item">
          <div class="search-title">车牌号码:</div>
          <el-input
            class="w_200"
            v-model="tableQuery.carNo"
            size="small"
            placeholder="请输入"
          />
        </div>
        <!-- 车辆类型 -->
        <div class="search-item">
          <div class="search-title">车辆类型:</div>
          <el-select
            class="w_200"
            v-model="tableQuery.carType"
            placeholder="请选择"
            size="small"
          >
            <el-option
              v-for="item in carOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <!-- 生产厂商 -->
        <div class="search-item">
          <div class="search-title">生产厂商:</div>
          <el-select
            class="w_200"
            v-model="tableQuery.carBrand"
            placeholder="请选择"
            size="small"
          >
            <el-option
              v-for="item in carBrandOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="search-row">
        <!-- 日期 -->
        <div class="search-item">
          <div class="search-title">日期:</div>
          <el-date-picker
              class="w_200"
              v-model="tableQuery.date"
              size="small"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
        </div>
      </div>
    </div>
    <div class="separator">详情信息</div>
    <!-- 表格 -->
    <div class="table-container">
      <my-table></my-table>
    </div>
  </div>
</template>


<script>
// 公共方法
import { getTime } from "@/utils/index"
// 导入组件
import myTable from '@/components/table/index'


const defaultDate = [new Date(getTime('start')), getTime()]
const defaultQuery = {
  carNo: '',
  carType: '',
  carBrandO: '',
  date: defaultDate
}
export default {
  name: "Detail",
  components: {
    myTable
  },
  data() {
    return {
      tableQuery: Object.assign({}, defaultQuery),
      carOptions: [
        {
          value: '1',
          label: '自卸'
        },
        {
          value: '2',
          label: '牵引'
        },
        {
          value: '3',
          label: '拖挂'
        }
      ],
      carBrandOptions: [
        {
          value: '1',
          label: '1'
        },
        {
          value: '2',
          label: '2'
        },
        {
          value: '3',
          label: '3'
        }
      ],
    };
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
};
</script>

<style scoped lang="scss">
.detail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 20px;
  .search-info {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    margin-bottom: 10px;
    .search-row {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      margin-bottom: 10px;
    }
    .search-item {
      width: auto;
      display: flex;
      align-items: center;
      margin-right: 10px;
      .search-title {
        width: auto;
        min-width: 100px;
        font-size: 13px;
        text-align: right;
        color: #000;
        box-sizing: border-box;
        padding-right: 5px;
      }
    }
  }
  .separator {
    width: 100%;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .table-container {
    width: 100%;
    height: auto;
    box-sizing: border-box;
  }
  .w_200 {
    width: 200px;
  }
}
</style>
