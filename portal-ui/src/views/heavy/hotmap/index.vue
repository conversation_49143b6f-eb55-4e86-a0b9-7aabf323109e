<template>
  <div class="container">
    <div class="search-left">
      <div class="separator">筛选条件</div>
      <!-- 日期 -->
      <div class="search-item">
        <div class="search-title">日期:</div>
        <el-date-picker
          class="input"
          v-model="tableQuery.date"
          size="small"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>
      <!-- 排放类型 -->
      <div class="search-item">
        <div class="search-title">排放类型:</div>
        <el-select
          class="input"
          v-model="tableQuery.type"
          placeholder="请选择"
          size="small"
        >
          <el-option
            v-for="item in typeOption"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
    </div>
    <div
      class="map-right"
      v-loading="laoding"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <my-map
        v-if="isShow"
        :isHeatmap="isHeatmap"
        :hotMapData="hotMapData"
        :radius="radius"
        :defaultCenter="defaultCenter"
        :defaultZoom="defaultZoom"
        :isLocation="true"
        @handleGetHotMap="handleGetHotMap"
        @handleRadiusScale="handleRadiusScale"
      ></my-map>
    </div>
  </div>
</template>

<script>
// 公共方法
import { getTime } from "@/utils/index"
// 公共组件
import { createGps } from "@/utils/transform"
// Api
import { getHotMapData } from "@/api/maps/map";
// 导入组件
import myMap from '@/components/amap/myMap'
// mock
import co2 from './co2.json'

const defaultQuery = {
  type: 'co2',
  date: '', // [new Date(getTime('start')), getTime()]
}
export default {
  name: "Index",
  components: {
    myMap
  },
  data() {
    return {
      laoding: false,
      isHeatmap: true, // 是否显示热力图
      hotMapData: [], // 热力图数据
      isShow: false,
      tableQuery: Object.assign({}, defaultQuery),
      typeOption: [
        { label: '氮氧化物排放', value: 'nox' },
        { label: '碳排放', value: 'co2' }
      ],
      radius: 7,
      defaultCenter: [116.40,39.91],
      defaultZoom: 11,
    };
  },
  watch: {
    'tableQuery.type': function(val) {
      if (val == 'co2') {
        this.handleGetHotMap()
      } else {
        this.hotMapData = []
      }
    }
  },
  created() {
    this.handleGetHotMap();
  },
  mounted() {
  },
  methods: {
    handleRadiusScale(level, center) {
      this.radius = level * 2
      this.defaultCenter = [center.lng, center.lat]
      this.defaultZoom = level
    },
    // 查询热力图层数据
    handleGetHotMap(data) {
      this.laoding = true
      getHotMapData({
        params1: 1,
        params2: 2,
        params3: 3
      }).then(res => {
        this.laoding = false
        // this.hotMapData = res?.data || []
        this.hotMapData = co2
        // const arr = []
        // for (let i=0; i<res?.data.length; i++) {
        //   let obj = {
        //     lng: createGps({ longitude: res?.data[i].lng, latitude: res?.data[i].lat })[0],
        //     lat: createGps({ longitude: res?.data[i].lng, latitude: res?.data[i].lat })[1],
        //     count: 1
        //   }
        //   arr.push(obj)
        // }
        // this.hotMapData = arr
        this.isShow = true
      }).catch(err => {
        this.laoding = false
      })
    },
  }
};
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: calc(100vh - 85px);
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px 20px;
  display: flex;
  justify-content: space-between;
  .search-left {
    width: 350px;
    height: 100%;
    margin-right: 20px;
    .separator {
      width: 100%;
      font-size: 13px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .search-item {
      width: auto;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      .search-title {
        width: auto;
        min-width: 70px;
        font-size: 13px;
        text-align: right;
        color: #000;
        box-sizing: border-box;
        padding-right: 5px;
      }
      .input {
        width: 100%;
      }
    }
  }
  .map-right {
    width: 100%;
    height: 100%;
  }
}
</style>