<template>
  <div class="detail">
    <!-- 头，数据类型概览 -->
    <div class="header-container">
      <div class="header-container-left">
        <el-card class="box-card">
          <div slot="header" class="header-title clearfix">
            <span>当前数据类型</span>
          </div>
          <div class="header-info">{{ currentType }}</div>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="header-title clearfix">
            <span>总筛查车辆数（辆）</span>
          </div>
          <div class="header-info">{{ currentNumber }}</div>
        </el-card>
      </div>
      <div class="header-container-right" style="display: flex; justify-content: space-between;">
        <el-card class="box-card" v-if="currentType == '全国'">
          <div slot="header" class="header-title clearfix">
            <span>筛查企业车辆分布</span>
          </div>
          <div class="header-info">
            <echart-bar barType="valueX" :xAxisData="xAxisData" :seriesData="seriesData" titleText=""
              titleColor="#515a6e" :isClick="false"></echart-bar>
          </div>
        </el-card>
        <el-card class="box-card" v-if="currentType !== '全国'">
          <div slot="header" class="header-title clearfix">
            <span>筛查企业品牌分布</span>
          </div>
          <div class="header-info">
            <echart-pie
              :pieData="pieData"
            ></echart-pie>
          </div>
        </el-card>
      </div>
    </div>
    <!-- 丢包率、无效率、异常位移 -->
    <div class="body-container">
      <div class="body-container-row">
        <el-card class="box-card">
          <div slot="header" class="row-title clearfix">
            <span>不同字段的无效率分布</span>
          </div>
          <div class="row-echart h700">
            <echart-boxplot
              :id="1"
              :source="source1"
              :yAxisData="yAxisData1"
            ></echart-boxplot>
          </div>
        </el-card>
      </div>
      <div class="body-container-row">
        <el-card class="box-card">
          <div slot="header" class="row-title clearfix">
            <span>不同字段的合理性指标分布</span>
          </div>
          <div class="row-echart">
            <echart-boxplot
              :id="2"
              :source="source2"
              :yAxisData="yAxisData2"
            ></echart-boxplot>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
// 公共方法
import { qian } from '@/utils/index'
// 组件
import echartBar from '@/components/echart/bar/index'
import echartPie from '@/components/echart/pie/index'
import echartBoxplot from '@/components/echart/boxplot/index'

// mock
import paiming from '../../../mock/company/paiming.json'
import wuxiao from '../../../mock/assessment/wuxiao.json'
import yichangDiubao from '../../../mock/assessment/yichang_diubao.json'
import { data } from 'jquery'

export default {
  name: "assessment-detail",
  components: {
    echartBar,
    echartPie,
    echartBoxplot,
  },
  data() {
    return {
      routerQuery: this.$route.query || '', // 路由传参query
      routerParams: this.$route.params || '', // 路由传参params
      currentType: '全国',
      currentNumber: '--',
      // 筛查企业车辆分布
      xAxisData: [],
      seriesData: [],
      // 盒须图
      source1: [],
      yAxisData1: [],
      source2: [],
      yAxisData2: [],
      // 饼图
      pieData: []
    };
  },
  created() {
    this.handleGetData();
    this.handleBoxplotData();
  },
  mounted() {
  },
  methods: {
    handleGetData() {
      // 当前数据
      this.currentType = this.routerQuery.company || '--'
      this.currentNumber = qian(this.routerQuery.car) || '--'
      paiming.forEach((el, i) => {
        if (this.currentType !== '全国') {
          if (this.currentType == el?.name) {
            this.xAxisData.push(el?.name)
            this.seriesData.push(el?.cnt)
          }
          // 饼图数据展示
          console.log(el?.name, this.currentType)
          if (el?.name == this.currentType) {
            el?.children.forEach(item => {
              this.pieData.push({ value: item?.cnt, name: item?.name })
            })
          }
        } else {
          if (i < 10) {
            this.xAxisData.push(el?.name)
            this.seriesData.push(el?.cnt)
          }
        }
      })
      this.seriesData.quickSort()
    },
    handleBoxplotData() {
      this.yAxisData1 = [
        'SCR出口NOx浓度字段',
        'SCR入口NOx浓度字段',
        ' DPF压字段',
        '发动机扭矩百分比字段',
        '发动机转速字段',
        '进气量字段',
        'SCR出口温度字段',
        'SCR入口温度字段',
        '尿素余量字段',
        '经度字段',
        '维度字段',
        '发动机冷却液温度字段',
        '摩擦扭矩百分比字段',
        '里程表读数字段',
        '大气压力字段',
        '燃料余量字段',
        '燃料流量字段',
        '速度字段',
      ]
      this.yAxisData2 = [
        '丢包率',
        '异常位移',
        '尿素异常',
        'NOx排放异常',
        '速度异常',
        '温度异常',
      ]
      for (let key in wuxiao) {
        // this.yAxisData1.push(key)
        this.source1.push(wuxiao[key].split(','))
      }
      for (let key in yichangDiubao) {
        this.source2.push(yichangDiubao[key].split(','))
      }
    },
  }
};
</script>

<style scoped lang="scss">
.detail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 20px;

  .header-container {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .header-container-left {
      width: 400px;
      height: auto;
      .box-card {
        height: calc((100% - 20px) / 2);
      }
      .box-card:nth-child(1) {
        margin-bottom: 20px;
      }

      .header-title {
        display: flex;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
      }

      .clearfix:before,
      .clearfix:after {
        display: table;
        content: "";
      }

      .header-info {
        text-align: center;
        color: #409eff;
        font-size: 24px;
      }
    }

    .header-container-right {
      width: 100%;
      height: auto;
      margin-left: 20px;

      .box-card {
        // width: calc(100% / 2);
        width: 100%;
        margin-right: 10px;
        .clearfix:before,
        .clearfix:after {
          display: table;
          content: "";
        }
        .header-title {
          display: flex;
          justify-content: left;
          font-weight: bold;
          font-size: 14px;
        }
        .header-info {
          width: 100%;
          height: 300px;
        }
      }
    }
  }
  .body-container {
    width: 100%;
    height: auto;
    .body-container-row {
      width: 100%;
      height: auto;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      .box-card {
        width: 100%;
        height: auto;
        .row-echart {
          width: 100%;
          height: 300px;
        }
        .h700 {
          height: 700px;
        }
      }
      .row-title {
        display: flex;
        justify-content: left;
        font-weight: bold;
        font-size: 14px;
      }
    }
  }
}
</style>