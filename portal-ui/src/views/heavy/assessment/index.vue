<template>
  <div class="assessment-detail">
    <!-- 中间 -->
    <div class="data-view-center">
      <!-- 标题 -->
      <div class="data-view-title">重型车企业排放在线监控数据质量评分系统</div>
      <!-- 地图 -->
      <div class="data-view-map">
        <my-map
          :v-if="isShow"
          :isDeep="true"
          :isLocation="false"
          :radius="7"
          :defaultZoom="defaultZoom"
          :isMarkerClusterer="true"
          :markerClustererData="markerClustererData"
          @handleCompanyInfo="handleCompanyInfo"
          @handleVisibleInfo="handleVisibleInfo"
        ></my-map>
        <!-- 角标 -->
        <div class="data-subtitle">
          <div>企业数量:<span style="color: #d438ff; font-size: 20px;"> &nbsp;{{ count }}</span></div>
          <div>规模企业:<span style="color: #d438ff; font-size: 20px;"> &nbsp;{{ scale }}</span></div>
        </div>
        <!-- 各企业明细数据 -->
        <div class="company-info" v-show="companyInfoVisible">
          <div class="comp-info-item">
            <span>企业名称:</span><span class="comp-info-val">{{ companyInfo.company || '--' }}</span>
          </div>
          <div class="comp-info-item">
            <span>联网车辆数:</span><span class="comp-info-val">{{ companyInfo.online || '--' }}</span>
          </div>
          <div class="comp-info-item">
            <span>九月得分:</span><span class="comp-info-val">{{ '--' }}</span>
          </div>
          <div class="comp-info-item fx_right">
            <el-button
              class="btn-detail"
              type="primary"
              size="mini"
              round
              @click="handleDetail"
            >详情</el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧 -->
    <div class="data-view-right">
      <div class="view-right-date"></div>
      <div class="echart-container">
        <div class="echart-box">
          <echart-radar
            :id="6"
            titleText="雷达图"
            titleColor="#fff"
            backgroundColor="transparent"
            :indicator="indicator"
          ></echart-radar>
        </div>
        <div class="echart-box data-warn">
          <div class="switch-type-radio">
            <el-radio-group v-model="radio2">
              <el-radio label="1" style="margin-right: 10px;">联网车辆数</el-radio>
              <el-radio label="2">在线率</el-radio>
            </el-radio-group>
          </div>
          <echart-bar
            :id="7"
            barType="valueX"
            titleText="数据问题项"
            titleColor="#fff"
            titleFontSize="12px"
            axisLabelColor="#fff"
            :yAxisName="yAxisName"
            :xAxisFormatter="true"
            :xAxisData="xAxisData5"
            :seriesData="seriesData5"
          ></echart-bar>
        </div>
        <div class="echart-box" style="overflow-x: hidden;">
          <div class="ecahrt-box-title">各企业月度得分</div>
          <!-- 滚动列表 -->
          <div class="echart-box-list" @mouseover="handleStop" @mouseleave="handleRun">
            <div :class="{ anim: animate === true }">
              <div v-for="(item, index) in list" :key="index" class="list-item">
                <div class="list-item-label">
                  <el-tooltip class="item" effect="dark" :content="item.name" placement="left">
                    <span>{{ item.name }}</span>
                  </el-tooltip>
                </div>
                <div class="list-item-value">{{ item.score }}分</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      title="详情"
      class="dialog-detail-info"
      :visible.sync="dialogVisible"
      width="80%"
      :before-close="handleClose"
    >
      <!-- 左侧  数据采集、数据传输 -->
      <div class="dialog-data-view-left">
        <div class="echart-container">
          <el-tabs v-model="activeTabsName" type="card" style="height: 100%;">
            <el-tab-pane label="数据采集" name="first" style="height: 100%;">
              <!-- echarts -->
              <div class="echart-box echart-box-column">
                <echart-Dbyaxis
                  v-if="activeTabsName == 'first'"
                  :id="1"
                  titleText="发动机重点考核指标"
                  titleColor="#fff"
                  axisLabelColor="#fff"
                  yAxisLeftName="分数"
                  yAxisRightName="缺失率/无效率"
                  :seriesData="seriesData1"
                  :xAxisData="xAxisData1"
                  :legendData="legendData"
                ></echart-Dbyaxis>
              </div>
              <div class="echart-box echart-box-column">
                <echart-Dbyaxis
                  v-if="activeTabsName == 'first'"
                  :id="2"
                  titleText="发动机通用考核指标"
                  titleColor="#fff"
                  axisLabelColor="#fff"
                  yAxisLeftName="分数"
                  yAxisRightName="缺失率/无效率"
                  :seriesData="seriesData2"
                  :xAxisData="xAxisData2"
                  :legendData="legendData"
                ></echart-Dbyaxis>
              </div>
              <div class="echart-box echart-box-column">
                <echart-Dbyaxis
                  v-if="activeTabsName == 'first'"
                  :id="3"
                  titleText="OBD数据考核指标"
                  titleColor="#fff"
                  axisLabelColor="#fff"
                  yAxisLeftName="分数"
                  yAxisRightName="缺失率/无效率"
                  :seriesData="seriesData3"
                  :xAxisData="xAxisData3"
                  :legendData="legendData"
                ></echart-Dbyaxis>
              </div>
            </el-tab-pane>
            <el-tab-pane label="数据传输" name="second" style="height: 100%;">
              <!-- echarts -->
              <div class="echart-box echart-box-column">
                <div class="switch-type-radio">
                  <el-radio-group v-model="radio">
                    <el-radio label="1" style="margin-right: 10px;">发动机数据</el-radio>
                    <el-radio label="2">OBD数据</el-radio>
                  </el-radio-group>
                </div>
                <echart-Dbyaxis
                  v-if="activeTabsName == 'second'"
                  :id="4"
                  titleText="发动机数据丢失率"
                  titleColor="#fff"
                  axisLabelColor="#fff"
                  yAxisLeftName="分数"
                  yAxisRightName="丢失率"
                  :seriesData="seriesData4"
                  :xAxisData="xAxisData4"
                ></echart-Dbyaxis>
              </div>
              <div class="echart-box echart-box-column">
                <echart-bar
                  v-if="activeTabsName == 'second'"
                  :id="5"
                  barType="valueX"
                  titleText="数据异常"
                  titleColor="#fff"
                  titleFontSize="12px"
                  axisLabelColor="#fff"
                  yAxisName="%"
                  :xAxisFormatter="false"
                  :xAxisData="['OBD数据缺失', '发动机数据缺失①', '发动机数据缺失②', '登入登出数据缺失①', '登入登出数据缺失②', '异常率']"
                  :seriesData="seriesData6"
                ></echart-bar>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <!-- 右侧 -->
      <div class="dialog-data-view-right">

      </div>
    </el-dialog>
  </div>
</template>


<script>
// 导入组件
import myMap from '@/components/amap/myMap'
import echartBar from '@/components/echart/bar/index'
import echartDbyaxis from '@/components/echart/line/dbYaxis'
import echartRadar from '@/components/echart/radar/index'
// mock
import factory from './factory.json'
import point from './point.json'
import default_ from './default.json'
import obd from './obd.json'
import engineLost from './engineLost.json'
import obdLost from './obdLost.json'
import dataQuality from './dataQuality.json'
import online from './online.json'
import abnormal from './abnormal.json'

export default {
  name: "assessment",
  components: {
    myMap,
    echartBar,
    echartRadar,
    echartDbyaxis,
  },
  data() {
    return {
      radio: '1',
      radio2: '1',
      activeTabsName: 'first',
      // 角标
      count: 0,
      scale: 0,
      companyInfo: {},
      companyInfoVisible: false,
      // 地图
      isShow: false,
      defaultZoom: 4,
      invalidRateOption: [],
      isMarkerClusterer: true,
      markerClustererData: [],
      // echarts
      seriesData1: [],
      seriesData2: [],
      seriesData3: [],
      seriesData4: [],
      seriesData5: [],
      seriesData6: [],
      xAxisData1: [],
      xAxisData2: [],
      xAxisData3: [],
      xAxisData4: [],
      xAxisData5: [],
      legendData: [
        {
          name: '缺失率分数',
          icon: 'circle',
          textStyle: {
            color: '#fff'
          }
        },
        {
          name: '缺失率',
          icon: 'circle',
          textStyle: {
            color: '#fff'
          }
        },
        {
          name: '无效率',
          icon: 'circle',
          textStyle: {
            color: '#fff'
          }
        },
        {
          name: '无效率分数',
          icon: 'circle',
          textStyle: {
            color: '#fff'
          }
        }
      ],
      yAxisName:"辆",
      indicator: [
        { name: '发动机重点考核指标' },
        { name: '发动机通用考核指标' },
        { name: 'OBD数据考核指标' },
        { name: '数据丢失率' },
        { name: '数据异常' }
      ],
      // 滚动列表
      list: [],
      animate: false,
      intervalId: null,
      // 弹窗
      dialogVisible: false,
    };
  },
  watch: {
    'radio': function(val) {
      this.dataLostRate();
    },
    'radio2': function(val) {
      this.dataIssues();
    }
  },
  created() {
    // 地图-企业位置
    this.getMarkerClustererData()
    // echarts 图表数据
    this.engineExamine();
    this.engineDefault();
    this.OBDdata();
    this.dataLostRate();
    this.monthScore();
    this.dataIssues();
    // 滚动列表
    this.intervalId = setInterval(this.scroll, 1500);
  },
  mounted() {
  },
  methods: {
    monthScore() {
      this.list = dataQuality
    },
    getMarkerClustererData() {
      for(let key in factory) {
        this.count += 1
        this.markerClustererData.push(
          {
            "lnglat": factory[key][0],
            "name": key
          }
        )
      }
      this.isShow = true
    },
    formatLineChartData(data, seriesKey, xAxisKey) {
      const deletionRate = [] // 缺失率
      const deletionRateScore = [] // 缺失率分数
      const invalidRate = [] // 无效率
      const invalidRateScore = [] // 无效率分数
      for (let i=0; i<data.length; i++) {
        deletionRate.push((data[i].deletion * 100).toFixed(2))
        invalidRate.push((data[i].invalid * 100).toFixed(2))
        deletionRateScore.push(data[i].deletion_score)
        invalidRateScore.push(data[i].invalid_score)
        this[xAxisKey].push(data[i].company)
        if (i == 20) break;
      }
      // 填充数据
      this[seriesKey] = [
        {
          name: '缺失率分数',
          type: 'line',
          lineStyle: {
            color: '#669966',
          },
          itemStyle: {
            color: '#669966', // 数据点颜色
          },
          data: deletionRateScore
        },
        {
          name: '缺失率',
          type: 'line',
          lineStyle: {
            color: '#CC6699',
          },
          itemStyle: {
            color: '#CC6699', // 数据点颜色
          },
          yAxisIndex: 1, // 指定这个系列使用Y轴数组中的第二个（从0开始计数）
          data: deletionRate
        },
        {
          name: '无效率',
          type: 'line',
          lineStyle: {
            color: '#CCFF66',
          },
          itemStyle: {
            color: '#CCFF66', // 数据点颜色
          },
          yAxisIndex: 1, // 指定这个系列使用Y轴数组中的第二个（从0开始计数）
          data: invalidRate
        },
        {
          name: '无效率分数',
          type: 'line',
          lineStyle: {
            color: '#669966',
          },
          itemStyle: {
            color: '#669966', // 数据点颜色
          },
          data: invalidRateScore
        }
      ]
    },
    // 发动机重点考核指标
    engineExamine() {
      this.formatLineChartData(point, 'seriesData1', 'xAxisData1');
    },
    engineDefault() {
      this.formatLineChartData(default_, 'seriesData2', 'xAxisData2');
    },
    OBDdata() {
      this.formatLineChartData(obd, 'seriesData3', 'xAxisData3');
    },
    dataLostRate() {
      
      const engine_lost = [] // 丢失率
      const lostRateScore = [] // 丢失率分数
      this.xAxisData4 = []
      if (this.radio == '1') {
        for (let i=0; i<engineLost.length; i++) {
          engine_lost.push((engineLost[i].lost_rate * 100).toFixed(2))
          lostRateScore.push(engineLost[i].lost_score)
          this.xAxisData4.push(engineLost[i].company)
          if (i == 20) break;
        }
      } else {
        for (let i=0; i<obdLost.length; i++) {
          engine_lost.push((obdLost[i].lost_rate * 100).toFixed(2))
          lostRateScore.push(obdLost[i].lost_score)
          this.xAxisData4.push(obdLost[i].company)
          if (i == 20) break;
        }
      }
      // 填充数据
      this.seriesData4 = [
        {
          name: '丢失率分数',
          type: 'line',
          lineStyle: {
            color: '#669966',
          },
          itemStyle: {
            color: '#669966', // 数据点颜色
          },
          data: lostRateScore
        },
        {
          name: '丢失率',
          type: 'line',
          lineStyle: {
            color: '#CC6699',
          },
          itemStyle: {
            color: '#CC6699', // 数据点颜色
          },
          yAxisIndex: 1, // 指定这个系列使用Y轴数组中的第二个（从0开始计数）
          data: engine_lost
        }
      ]
    },
    // 数据异常、数据问题
    dataIssues() {
      // 数据异常
      this.seriesData6 = []
      let obdSum = 0
      let engineSum = 0
      abnormal.forEach(el => {
        obdSum += Number(el.obd)
        engineSum += Number(el.engine)
      })
      // OBD数据缺失率
      this.seriesData6.push((obdSum / abnormal.length * 100).toFixed(2))
      // 发动机数据缺失率①
      this.seriesData6.push((engineSum / abnormal.length * 100).toFixed(2))
      // 数据问题
      const activeData = JSON.parse(JSON.stringify(online))
      const onlineData = JSON.parse(JSON.stringify(online))
      const activeArr = activeData.sort((a, b) => { return Number(b.active) - Number(a.active) })  // 按照在线率降序
      const onlineArr = onlineData.sort((a, b) => { return Number(b.online) - Number(a.online) })  // 按照联网车辆数降序
      this.seriesData5 = []
      this.xAxisData5 = []
      if (this.radio2 == 1) {
        this.yAxisName = '辆'
        for (let i=0; i<onlineArr.length; i++) {
          this.seriesData5.push(onlineArr[i].online)
          this.xAxisData5.push(onlineArr[i].company)
          if (i == 20) break;
        }
      } else {
        this.yAxisName = '%'
        for (let i=0; i<activeArr.length; i++) {
          this.seriesData5.push(
            ((activeArr[i].active / onlineArr[i].online) * 100).toFixed(2)
          )
          this.xAxisData5.push(activeArr[i].company)
          if (i == 20) break;
        }
      }
    },
    // echart点击事件
    handleClick() {

    },
    // 滚动列表
    scroll() {
      this.animate = true
      setTimeout(() => {
        this.list.push(this.list[0])
        this.list.shift()
        this.animate = false
      }, 500)
    },
    handleRun() {
      this.intervalId = setInterval(this.scroll, 1500)
    },
    handleStop() {
      this.animate = false
      clearInterval(this.intervalId)
    },
    // 各企业信息
    handleCompanyInfo(data) {
      this.companyInfoVisible = false
      const x = data.pixel.x;
      const y = data.pixel.y;
      const dom = document.getElementsByClassName('company-info')[0];
      dom.style.top = y + 'px'
      dom.style.left = x + 'px'
      // 获取对应企业信息
      const company = data.target.w.name;
      online.forEach(el => {
        if (el.company == company) {
          this.companyInfo = el
        }
      })
      this.companyInfoVisible = true
    },
    // 隐藏企业信息
    handleVisibleInfo() {
      this.companyInfoVisible = false
    },
    // 查看详情
    handleDetail() {
      this.dialogVisible = true
    },
    // 关闭详情弹窗
    handleClose() {
      this.dialogVisible = false
    },
  },
  beforeDestroy() {
    clearInterval(this.intervalId)
  }
};
</script>

<style scoped lang="scss">
.assessment-detail {
  width: 100%;
  height: calc(100vh - 84px);
  background-color: #011a33;
  box-sizing: border-box;
  padding: 10px 10px;
  display: flex;
  justify-content: center;
  // 禁止选中文字
  -webkit-user-select: none; /* Chrome, Safari, Opera */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
  .data-view-center {
    width: calc(100% - 400px);
    height: 100%;
    margin-right: 10px;
    margin-left: 10px;
    .data-view-title {
      width: 100%;
      height: 50px;
      line-height: 50px;
      margin-bottom: 10px;
      font-weight: bold;
      font-size: 1.6em;
      color: #fff;
      text-align: center;
    }
    .data-view-map {
      width: 100%;
      // height: calc(100% / 3 * 2);
      height: calc(100% - 60px);
      margin-bottom: 10px;
      box-sizing: border-box;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
      .data-subtitle {
        width: 150px;
        padding: 5px 10px;
        box-sizing: border-box;
        border-radius: 6px;
        background-color: rgba(0, 0, 0, 0.8);
        color: #FFF;
        font-size: 14px;
        font-weight: bold;
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 999;
      }
      .company-info {
        padding: 10px 10px;
        box-sizing: border-box;
        border: 1px solid rgba(0, 0, 0, 0.3);
        background-color: rgba(255, 255, 255, 0.8);
        box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
        border-radius: 6px;
        position: absolute;
        top: 200px;
        left: 200px;
        z-index: 999;
        .comp-info-item {
          font-size: 14px;
          span {
            font-weight: bold;
          }
          .comp-info-val {
            color: blueviolet;
            font-size: 14px;
            padding-left: 10px;
            box-sizing: border-box;
          }
        }
        .fx_right {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
    .data-view-bottom {
      display: none;
      width: 100%;
      height: calc((100% - 10px) / 3);
      display: flex;
      justify-content: center;
      .echart-box {
        width: 100% !important;
        height: 100% !important;
        padding: 5px 5px;
        box-sizing: border-box;
        border-radius: 6px;
        background-color: rgba(189, 189, 189, 0.1);
        margin-bottom: 10px;
        box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.3);
        background-color: rgba(256, 256, 256, 0.1);
      }
    }
  }
  .data-view-right {
    width: 400px;
    height: 100%;
    .view-right-date {
      width: 100%;
      height: 60px;
    }
    .echart-container {
      width: 100%;
      height: calc(100% - 60px);
    }
    .data-warn {
      position: relative;
    }
    .switch-type-radio {
      width: 100%;
      display: flex;
      justify-content: right;
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 999;
    }
    // 滚动列表
    .ecahrt-box-title {
      width: 100%;
      height: 35px;
      line-height: 35px;
      color: #FFF;
      font-weight: bold;
      font-size: 17px;
      padding-left: 5px;
      box-sizing: border-box;
      margin-bottom: 5px;
    }

    // 滚动列表
    .echart-box-list {
      width: calc(100% + 15px);
      height: calc(100% - 40px);
      overflow-y: auto;

      .list-item {
        margin-bottom: 4px;
        width: 97%;
        height: 35px;
        line-height: 35px;
        font-size: 12px;
        color: #fff;
        padding-left: 20px;
        padding-right: 40px;
        box-sizing: border-box;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;

        .list-item-label {
          width: calc(100% - 50px);
          overflow: hidden; /* 确保溢出的内容会被隐藏 */
          white-space: nowrap; /* 确保文本在一行内显示，不换行 */
          text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
        }
        .ist-item-value {
          width: 50px;
          overflow: hidden; /* 确保溢出的内容会被隐藏 */
          white-space: nowrap; /* 确保文本在一行内显示，不换行 */
          text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
        }
        .list-item-value {
          color: rgba(216, 100, 248, 0.7);
          font-weight: bold;
          font-size: 13px;
        }
      }
      .list-item:hover {
        cursor: pointer;
        box-shadow: 3px 3px 5px rgba(230, 159, 249, 0.3);
        background: linear-gradient(to right, rgba(216, 100, 248, 0.1) 30%, rgba(216, 100, 248, 0.7) 50%) right;
        background-size: 200%;
        transition: .5s ease-in-out;
        font-weight: bold;
        font-size: 13px;
        .list-item-value {
          color: #fff;
        }
      }

      .anim {
        transition: all 0.5s;
        margin-top: -41px;
      }
    }
  }
  // echarts 图表
  .echart-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    .echart-box {
      width: 100%;
      height: calc((100% - 20px) / 3);
      padding: 5px 5px;
      box-sizing: border-box;
      border-radius: 6px;
      background-color: rgba(189, 189, 189, 0.1);
      margin-bottom: 10px;
      box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.3);
      background-color: rgba(256, 256, 256, 0.1);
    }
    .echart-box-column {
      height: calc(100% / 2);
    }
  }
  // dialog 弹窗
  .dialog-detail-info {
    .dialog-data-view-left {
      width: 400px;
      height: 100%;
      display: flex;
      align-items: center;
      margin-right: 10px;
      .switch-type-radio {
        width: 100%;
        display: flex;
        justify-content: right;
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 999;
      }
    }
    .dialog-data-view-right {
      width: calc(100% - 400px);
      height: 100%;
      border: 1px solid #319ee4;
    }
  }
  .w_200 {
    width: 200px;
  }
  .w_230 {
    width: 230px;
  }
}
// 弹窗样式
::v-deep .el-dialog {
  height: 80%;
  overflow: hidden;
  background-color: #011a33;
  
}
::v-deep .el-dialog__body {
  display: flex;
  padding: 10px 20px;
  position: absolute;
  top: 54px;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 1;
  overflow: hidden;
  overflow-y: auto;
}
::v-deep .el-dialog__title {
  font-weight: bold;
  color: #319ee4;
}
::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: #ffffff;
  transform: rotate(-180deg);
  transition: transform 1s;
}
::v-deep .el-dialog__headerbtn .el-dialog__close:hover {
  transform: rotate(180deg);
  transition: transform 1s;
}
/* 修改滚动条滑块颜色 */
::v-deep .dialog-container::-webkit-scrollbar-thumb,
::v-deep .el-tabs__content::-webkit-scrollbar-thumb {
  background-color: #319ee4;
}
/* 修改滚动条轨道背景色 */
::v-deep .dialog-container::-webkit-scrollbar-track,
::v-deep .el-tabs__content::-webkit-scrollbar-track {
  background-color: #4b6a89;
  border-radius: 3px;
}

::v-deep .el-radio {
  color: #fff;
}
::v-deep .el-radio__inner {
  width: 10px;
  height: 10px;
}
::v-deep .el-radio__label {
  font-size: 12px;
}
::v-deep .el-tabs__header {
  margin: 0 0 10px;
}
::v-deep .el-tabs__content {
  width: 100%;
  height: calc(100% - 60px);
  box-sizing: border-box;
  padding-right: 5px;
  overflow-y: auto;
}
::v-deep .el-tabs__item {
  color: #fff;
}
::v-deep .el-tabs__header, .el-tabs--card, 
::v-deep .el-tabs__header .el-tabs__item {
  border: 1px solid #011a33;
}
::v-deep .el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: 1px solid #011a33;
  border-bottom-color: rgb(64, 158, 255);
}
::v-deep .el-tabs__header {
  border-bottom-color: rgb(64, 158, 255);
}
::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  border-bottom-color: rgb(64, 158, 255);
  background-color: rgb(64, 158, 255);
  border-radius: 4px 4px 0px 0px;
}
</style>
