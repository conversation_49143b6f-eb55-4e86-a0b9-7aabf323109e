<template>
    <div class="enterprise-container"
      v-loading="laoding"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <div class="row">
        <!-- 打分项 -->
        <div class="left">
          <!-- <div class="notification">
            <div class="notiglow"></div>
            <div class="notiborderglow"></div>
            <div class="notititle">Welcome To Uiverse</div>
            <div class="notibody">Contribute to Open Source UI Elements</div>
          </div> -->
        </div>
        <!-- 总分 -->
        <div class="right">
          <div style="">
            <div class="e-card playing">
              <div class="e-card-title">企业总分数</div>
              <div class="wave"></div>
              <div class="wave"></div>
              <div class="wave"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="test-div">测试数据</div>
      </div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts';
  import { mapState } from 'vuex'
  // Api
  import { getTime } from "@/utils/index"
  
  const defaultDate = [new Date(getTime('start')), getTime()]
  const defaultQuery = {

  }
  export default {
    name: "trajectory",
    components: {

    },
    computed: {
      ...mapState({
        sidebar: state => state.app.sidebar,
      }),
    },
    data() {
      return {
        laoding: false, // 页面整体加载动画
      };
    },
    watch: {
      'sidebar.opened': function (val) {
        setTimeout(() => {
          // 监听左侧菜单栏开合，重绘页面图表
          this.resizeChart();
        }, 300)
      },
    },
    created() {
      // 窗口大小变化，重绘Echart图表
      window.addEventListener('resize', () => {
        this.resizeChart();
      });
    },
    mounted() {

    },
    methods: {
      resizeChart() {
        this.myChart.resize();
      },
    }
  };
  </script>
  
  <style scoped lang="scss">
    .enterprise-container {
      width: 100%;
      min-height: calc(100vh - 84px);
      background-color: #011a33;
      box-sizing: border-box;
      padding: 20px 20px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      overflow-y: auto;

      .row {
        width: 100%;
        height: auto;
        display: flex;
        justify-content: center;
        flex: 1;
        margin-bottom: 20px;

        .left {
          width: 70%;
          margin-right: 10px;
          border: 1px solid #fff;
        }

        .right {
          width: calc(100% - 70%);

          /* 
          
          *  波浪背景卡片 
          *  .wave .playing 样式为辅助动画效果
          */ 
          .e-card {
            background: transparent;
            box-shadow: 0px 8px 28px -9px rgba(182, 180, 180, 0.45);
            position: relative;
            width: 240px;
            height: 330px;
            border-radius: 16px;
            overflow: hidden;
            margin-right: 20px;

            .e-card-title {
              font-size: 20px;
              font-weight: 600;
              color: #fff;
              text-align: center;
              padding: 20px 0;
              position: relative;
              z-index: 1;
            }

            .e-card-content {
              width: 100%;
            }
          }
          .wave {
            position: absolute;
            width: 540px;
            height: 700px;
            opacity: 0.6;
            left: 0;
            top: 0;
            margin-left: -50%;
            margin-top: -70%;
            background: linear-gradient(744deg,#af40ff,#5b42f3 60%,#00ddeb);
          }
          .wave:nth-child(2),
          .wave:nth-child(3) {
            top: 310px; // 调整 2、3 级波浪高度
          }

          .playing .wave {
            border-radius: 40%;
            animation: wave 3000ms infinite linear;
          }
          .playing .wave:hover {
            border-radius: 40%;
            animation: wave 3000ms infinite linear;
          }
          .wave:hover {
            border-radius: 40%;
            animation: wave 55s infinite linear;
          }
          .playing .wave:nth-child(2) {
            animation-duration: 4000ms;
          }
          .wave:nth-child(2) {
            animation-duration: 50s;
          }
          .playing .wave:nth-child(3) {
            animation-duration: 5000ms;
          }
          .wave:nth-child(3) {
            animation-duration: 45s;
          }
          @keyframes wave {
            0% {
              transform: rotate(0deg);
            }

            100% {
              transform: rotate(360deg);
            }
          }
        }
      }
    }
  </style>