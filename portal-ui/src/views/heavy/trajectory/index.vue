<template>
  <div class="trajectory-container"
    v-loading="laoding"
    element-loading-text="加载中..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div class="left">
      <!-- 筛选条件 -->
      <div class="echarts-query">
        <div>
          <span>按时间查询：</span>
          <el-radio-group v-model="targetQuery.date">
            <el-radio label="24h">最近24小时</el-radio>
            <el-radio label="7d">最近7天</el-radio>
          </el-radio-group>
        </div>
        <!-- 设置默认展示省市 -->
        <div>
          <span><i class="el-icon-aim"></i>位置：</span>
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              {{ commandName }}<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in dropdownArr" :key="item.name" :command="item.name">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <!-- Echarts图表 -->
      <div id="info" class="info"></div>
      <!-- 地图展示 -->
      <div
        class="map"
      >
        <my-map
          v-if="isShow"
          :isLocation="true"
          :radius="7"
          :defaultZoom="defaultZoom"
          :isPathSimplifier="isPathSimplifier"
          :pathSimplifierData="pathSimplifierData"
          @handleGetHotRoad="handleGetHotRoad"
        ></my-map>
      </div>
    </div>
    <div class="right">
      <el-tabs v-model="activeName" @tab-click="handleClick" style="height: 100%;">
        <!-- Tabs1 -->
        <el-tab-pane label="全部道路" name="first" style="height: 100%;">
          <span slot="label"><i class="el-icon-truck"></i>全部道路</span>
          <div class="car-list">
            <div class="table-container">
              <!-- 表头 -->
              <div class="table-head">
                <div class="table-column">总长度</div>
                <div class="table-column">道路名</div>
                <div class="table-column">车辆数</div>
                <div class="table-column">Nox排放量</div>
                <div class="table-column">碳排放量</div>
                <div class="table-column">操作</div>
              </div>
              <!-- 数据 -->
              <div class="table-body">
                <div
                  v-for="(item, index) in tableData"
                  :class="selectedId == (index+1) ? 'table-body-row active' : 'table-body-row'"
                  :key="index"
                  @click.stop="handleCurrentChange(index+1, item)"
                >
                  <div class="table-column">{{ item.roadLength + 'km' || '--' }}</div>
                  <div class="table-column txt_left">
                    <el-tooltip class="item" effect="dark" :content="item.road" placement="left">
                      <span><i class="el-icon-location bouncy"></i>{{ item.road || '--' }}</span>
                    </el-tooltip>
                  </div>
                  <div class="table-column">{{ item.cnt || '--' }}</div>
                  <div class="table-column">{{ item.nox || '--' }}%</div>
                  <div class="table-column">{{ item.co2 || '--' }}%</div>
                  <div class="table-column">
                    <span @click.stop="handleDetail(item)">详情</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <!-- Tabs2 -->
        <el-tab-pane label="区域道路" name="second" style="height: 100%;">
          <span slot="label"><i class="el-icon-place"></i>区域道路</span>
          <div class="car-list">
            <div class="table-container">
              <!-- 表头 -->
              <div class="table-head">
                <div class="table-column">区域名</div>
                <div class="table-column">道路名</div>
                <div class="table-column">车辆数</div>
                <div class="table-column">Nox排放量</div>
                <div class="table-column">碳排放量</div>
                <div class="table-column">操作</div>
              </div>
              <!-- 数据 -->
              <div class="table-body">
                <div
                  v-for="(item, index) in tableData"
                  :class="selectedId == (index+1) ? 'table-body-row active' : 'table-body-row'"
                  :key="index"
                  @click.stop="handleCurrentChange(index+1, item)"
                >
                  <div class="table-column">{{ item.id || '--' }}</div>
                  <div class="table-column txt_left">
                    <el-tooltip class="item" effect="dark" :content="item.road" placement="left">
                      <span><i class="el-icon-location"></i>{{ item.road || '--' }}</span>
                    </el-tooltip>
                  </div>
                  <div class="table-column">{{ item.cnt || '--' }}</div>
                  <div class="table-column">{{ item.nox || '--' }}%</div>
                  <div class="table-column">{{ item.co2 || '--' }}%</div>
                  <div class="table-column">
                    <span @click.stop="handleDetail(item)">详情</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="80%"
      :before-close="handleClose"
    >
    <el-collapse v-model="collapseActive">
      <el-collapse-item title="车辆列表" name="1">
        <div class="car-info">
          <el-tag v-for="(item, index) in carList" :key="index" @click="handleCarInfo(item)">
            {{ item.name }}
          </el-tag>
        </div>
      </el-collapse-item>
      <el-collapse-item title="车辆信息" name="2">
        <div class="dialog-detail">
          <el-descriptions title="">
            <el-descriptions-item label="燃料类型">{{ currentCar.epa_vehicle_type || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆分类">{{ currentCar.veh_catalogue || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆类别">{{ currentCar.veh_catalogue_detail || '--' }}</el-descriptions-item>
            <el-descriptions-item label="排放阶段">{{ currentCar.engine_emission_leveltype || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车牌号">{{ currentCar.vehicle_license || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车籍地">{{ currentCar.vehicle_domicile || '--' }}</el-descriptions-item>
            <el-descriptions-item label="备案协议类型">{{ currentCar.register_agreement || '--' }}</el-descriptions-item>
            <el-descriptions-item label="实际协议类型">{{ currentCar.actual_agreement || '--' }}</el-descriptions-item>
            <el-descriptions-item label="激活模式">{{ currentCar.veh_register_mode || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆备案企业名称">{{ currentCar.company_name || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆备案企业类型">{{ currentCar.company_type || '--' }}</el-descriptions-item>
            <el-descriptions-item label="企业平台名称">{{ currentCar.platform_name || '--' }}</el-descriptions-item>
            <el-descriptions-item label="平台建设方式">{{ currentCar.platform_mode || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆生产企业名称">{{ currentCar.manufacturer_build_name || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆底盘生产厂家">{{ currentCar.chassis_company_name || '--' }}</el-descriptions-item>
            <el-descriptions-item label="静态备案时间">{{ currentCar.register_time || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆同步状态">{{ currentCar.sync_status || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆同步时间">{{ currentCar.sync_time || '--' }}</el-descriptions-item>
            <el-descriptions-item label="终端激活状态">{{ currentCar.activation_status || '--' }}</el-descriptions-item>
            <el-descriptions-item label="终端激活时间">{{ currentCar.activation_time || '--' }}</el-descriptions-item>
            <el-descriptions-item label="首次上线时间">{{ currentCar.first_online_time || '--' }}</el-descriptions-item>
            <el-descriptions-item label="首次上线状态">{{ currentCar.first_online_status || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆联网状态">
              <el-tag size="small" type="success" v-if="currentCar.connect_status == '已联网'">{{ currentCar.connect_status || '--' }}</el-tag>
              <el-tag size="small" type="danger" v-else>{{ currentCar.connect_status || '--' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="车辆联网时间">{{ currentCar.connect_time || '--' }}</el-descriptions-item>
            <el-descriptions-item label="车辆型号">{{ currentCar.vehicle_model  || '--'}}</el-descriptions-item>
            <el-descriptions-item label="车辆类型">{{ currentCar.vehicle_type || '--' }}</el-descriptions-item>
            <el-descriptions-item label="总质量">{{ currentCar.max_loads || '--' }}</el-descriptions-item>
            <el-descriptions-item label="发动机型号">{{ currentCar.engine_model || '--' }}</el-descriptions-item>
            <el-descriptions-item label="马力">{{ currentCar.engine_power || '--' }}</el-descriptions-item>
            <el-descriptions-item label="扭矩">{{ currentCar.engine_torque  || '--'}}</el-descriptions-item>
            <el-descriptions-item label="发动机生产地址">{{ currentCar.engine_production_address || '--' }}</el-descriptions-item>
            <el-descriptions-item label="发动机生产厂家">{{ currentCar.engine_manufacturer_name || '--' }}</el-descriptions-item>
            <el-descriptions-item label="终端型号">{{ currentCar.tbox_model || '--' }}</el-descriptions-item>
            <el-descriptions-item label="终端生产厂家">{{ currentCar.tbox_manufacturer_name || '--' }}</el-descriptions-item>
            <el-descriptions-item label="三位标识符">{{ currentCar.chip_prefix || '--' }}</el-descriptions-item>
            <el-descriptions-item label="芯片型号">{{ currentCar.chip_model || '--' }}</el-descriptions-item>
            <el-descriptions-item label="芯片制造企业">{{ currentCar.chip_manufacturer_name || '--' }}</el-descriptions-item>
            <el-descriptions-item label="生产日期(国产)/进口日期(国产)">{{ currentCar.manufacture_date || '--' }}</el-descriptions-item>
            <el-descriptions-item label="国别">{{ currentCar.country || '--' }}</el-descriptions-item>
            <el-descriptions-item label="最大净功率">{{ currentCar.maximum_power || '--' }}</el-descriptions-item>
            <el-descriptions-item label="最大净功率转速">{{ currentCar.maximum_engine_rotationl_speed || '--' }}</el-descriptions-item>
            <el-descriptions-item label="额定功率">{{ currentCar.rated_capacity || '--' }}</el-descriptions-item>
            <el-descriptions-item label="额定功率转速">{{ currentCar.rated_capacity_rotationl_speed || '--' }}</el-descriptions-item>
            <el-descriptions-item label="最大净扭矩">{{ currentCar.maximum_torque || '--' }}</el-descriptions-item>
            <el-descriptions-item label="最大净扭矩转速">{{ currentCar.maximum_torque_rotationl_speed || '--' }}</el-descriptions-item>
            <el-descriptions-item label="燃料供给系统形式">{{ currentCar.fuel_feed_system || '--' }}</el-descriptions-item>
            <el-descriptions-item label="排气后处理系统形式">{{ currentCar.exhaust_aftertreatment || '--' }}</el-descriptions-item>
            <el-descriptions-item label="环保公开信息接收时间">{{ currentCar.public_time || '--' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-collapse-item>
    </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>    
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { mapState } from 'vuex'
// Api
import { getBoundariesMapData } from "@/api/maps/map";
// 公共方法
import { getTime } from "@/utils/index"
import {
  getBearing,
  calculateNewLatLng
} from "@/utils/transform"
// 导入组件
import * as turf from '@turf/turf'
import myMap from '@/components/amap/myMap'
import moveTrajectory from '@/components/amap/move'
// mock数据
import roadPath2 from "../../../components/amap/test.json"

const defaultDate = [new Date(getTime('start')), getTime()]
const defaultQuery = {
  date: '24h'
}

export default {
  name: "trajectory",
  components: {
    myMap,
    moveTrajectory
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
    }),
  },
  data() {
    return {
      dialogTitle: '详情',
      dialogVisible: false,
      collapseActive: '1',
      currentCar: {},
      laoding: false,
      defaultZoom: 8,
      dropdownArr: [
        { name: "北京" },
        { name: "河北省" },
        { name: "山西省" },
        { name: "辽宁省" },
        { name: "吉林省" },
        { name: "黑龙江省" },
        { name: "江苏省" },
        { name: "浙江省" },
        { name: "安徽省" },
        { name: "福建省" },
        { name: "江西省" },
        { name: "山东省" },
        { name: "河南省" },
        { name: "湖北省" },
        { name: "湖南省" },
        { name: "广东省" },
        { name: "海南省" },
        { name: "重庆" },
        { name: "四川省" },
        { name: "贵州省" },
        { name: "云南省" },
        { name: "陕西省" },
        { name: "甘肃省" },
        { name: "青海省" },
        { name: "台湾省" }
      ],
      commandName: '北京',
      // 是否展示菜单
      isShow: false,
      // 海量轨迹
      isPathSimplifier: true,
      pathSimplifierData: [],
      // 筛选条件-日期
      date: defaultDate,
      // 重点通道列表
      tableData: [],
      // 当前道路通过车辆
      carList: [],
      // Echarts图表
      myChart: null,
      echartsData: [
        ["00:00:00", 10],
        ["02:00:00", 13],
        ["04:00:00", 21],
        ["06:00:00", 16],
        ["08:00:00", 19],
        ["10:00:00", 23],
        ["12:00:00", 16],
        ["14:00:00", 32],
        ["16:00:00", 92],
        ["18:00:00", 100],
        ["20:00:00", 76],
        ["22:00:00", 88],
        ["24:00:00", 115]
      ],
      // tabs 标签页
      activeName: 'first',
      targetQuery: Object.assign({}, defaultQuery),
      selectedId: '', // 当前选中行
    };
  },
  watch: {
    'sidebar.opened': function (val) {
      setTimeout(() => {
        // 监听左侧菜单栏开合，重绘页面图表
        this.resizeChart();
      }, 300)
    },
    'targetQuery.date': function(data) {
      if (data == '24h') {
        this.echartsData = [
          ["00:00:00", 10],
          ["02:00:00", 13],
          ["04:00:00", 21],
          ["06:00:00", 16],
          ["08:00:00", 19],
          ["10:00:00", 23],
          ["12:00:00", 16],
          ["14:00:00", 32],
          ["16:00:00", 92],
          ["18:00:00", 100],
          ["20:00:00", 76],
          ["22:00:00", 88],
          ["24:00:00", 115]
        ]
      } else {
        let date = new Date();
        this.echartsData = [
          [this.AddDays(date, 1), 10],
          [this.AddDays(date, 2), 13],
          [this.AddDays(date, 3), 21],
          [this.AddDays(date, 4), 76],
          [this.AddDays(date, 5), 19],
          [this.AddDays(date, 6), 88],
          [this.AddDays(date, 7), 115]
        ]
      }
      this.createEcharts(this.echartsData)
    },
  },
  created() {
    // 窗口大小变化，重绘Echart图表
    window.addEventListener('resize', () => {
      this.resizeChart();
    });
  },
  mounted() {
    this.handleGetCarList();
    this.handleGetHotRoad();
    this.createEcharts(this.echartsData);
  },
  methods: {
    splitPathByDistance(data) {
      // 遍历data更新数组中的值，经度在前
      for (let i = 0; i < data.length; i++) {
        let temp = data[i];
        data[i] = [temp[1], temp[0]];
      }
      let total = 0;
      // 示例数据
      const line = turf.lineString(data);

      const options = { units: 'kilometers' };
      const chunks = turf.lineChunk(line, 10, options);
      const { features } = chunks
      const obj = {"type":"MultiPolygon","coordinates": []}
      for (let i = 0; i < features.length; i++) {
        let coordinates = features[i].geometry.coordinates;
        obj.coordinates.push(coordinates)
        // 计算每一组分段的总长度
        var line2 = turf.lineString(coordinates);
        var length = turf.length(line2, { units: "kilometers" });
      }
      this.exportToJson(obj, 'sanya_10km_segmentation')
    },
    // 导出 json
    exportToJson(data, name) {
        const jsonData = data || [];
        const blob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = name + '.json';
        document.body.appendChild(a);
        a.click();
    },
    AddDays(date,days){
      let nd = new Date(date);
      nd = nd.valueOf();
      nd = nd + days * 24 * 60 * 60 * 1000;
      nd = new Date(nd);
      let y = nd.getFullYear();
      let m = nd.getMonth()+1;
      let d = nd.getDate();
      if(m <= 9) m = "0"+m;
      if(d <= 9) d = "0"+d; 
      let cdate = y+"-"+m+"-"+d;
      return cdate;
    },
    resizeChart() {
      this.myChart.resize();
    },
    // Echarts渐变曲线图
    createEcharts(data) {
      const dateList = data.map(function (item) {
        return item[0];
      });
      const valueList = data.map(function (item) {
        return item[1];
      });
      // 设置图表
      const option = {
        visualMap: [
          {
            show: false,
            type: 'continuous',
            seriesIndex: 0,
            min: 0,
            max: 400
          },
          {
            show: false,
            type: 'continuous',
            seriesIndex: 1,
            dimension: 0,
            min: 0,
            max: dateList.length - 1
          }
        ],
        title: [
          {
            left: 'center',
            text: '24小时重点通道流量',
            textStyle: {
              color: "#FFF",
              fontWeight: "bolder"
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          show: true,
          axisPointer: {
            type: 'line', // 或者 'cross' 为十字准线，还可以是 'shadow'
            lineStyle: {
              color: '#95ff5d' // 这里设置为红色
            }
          }
        },
        xAxis: [
          {
            data: dateList,
            axisLabel: {
              textStyle: {
                color: '#fff'
              }
            }
          }
        ],
        yAxis: [
          {
            name: '(辆)',
            nameTextStyle: {
              color: "#FFF"
            },
            splitNumber: 3,
            axisLabel: {
              textStyle: {
                color: '#fff'
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgb(70, 88, 105)'
              }
            }
          }
        ],
        grid: {
          left: '10%',
          right: '10%',
          top: '25%',
          bottom: '12%'
        },
        series: [
          {
            type: 'line',
            showSymbol: false,
            smooth: true,
            data: valueList,
            lineStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'red' // 渐变起始颜色
                }, {
                    offset: 1,
                    color: '#7cb5ec' // 渐变结束颜色
                }])
            }
          }
        ]
      };
      const chartDom = document.getElementById('info');
      this.myChart = echarts.init(chartDom);
      option && this.myChart.setOption(option);
    },
    // 查询图层数据
    handleGetHotRoad() {
      this.laoding = true
      this.getRoadWeb();
    },
    setTrafficStatus(data) {
      let status = '#67C23A'
      if (Number(data) > 0 && Number(data) <= 50000) {
        status = '#67C23A'
      }
      if (Number(data) > 50000 && Number(data) <= 100000) {
        status = '#E6A23C'
      }
      if (Number(data) > 100000 && Number(data) <= 149999) {
        status = '#F56C6C'
      }
      if (Number(data) > 150000 && Number(data) <= 299999) {
        status = 'rgb(245, 30, 30)'
      }
      if (Number(data) > 300000) {
        status = 'rgb(110, 1, 1)'
      }
      return status
    },
    getRoadWeb() {
      const that = this
      let arr = []
      let roadName = [], roadLength = 0
      let traffic = this.setTrafficStatus(this.getRandomInt(0, 400000))
      this.tableData = []
      // this.splitPathByDistance方法，按照 1km 进行路径分段
      const path_data = roadPath2[0].line_string;
      let coords = []
      runner(path_data)
      // this.splitPathByDistance(coords, 1)

      function runner(data) {
        for (let i=0; i<data.length; i++) {
          for (let j=0; j<data[i].length; j++) {
            coords.push([data[i][j][1], data[i][j][0]])
          }
        }
      }
      // 海量路网
      roadPath2.forEach(el => {
        el.line_string.forEach((el2, index) => {
          if (index % 1000 == 0) {
            traffic = this.setTrafficStatus(this.getRandomInt(0, 400000))
          }
          // 计算出当前道路长度
          const km = this.getHaversine(
            el2[0][0], el2[0][1],
            el2[el2.length - 1][0], el2[el2.length - 1][1]
          )
          const obj = {
            vin: el.name,
            name: el.name,
            traffic,
            path: el2,
            km: km
          }
          arr.push(obj)
          // 获取列表
          roadName.push(el.name)
        })
      })
      // 设置列表数据
      roadName = [...new Set(roadName)]
      roadName.forEach((el, index) => {
        const data = this.getRandomInt(1000, 100000)
        roadLength = 0
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].name == el) {
            roadLength += Number(arr[i].km)
          }
        }
        const obj = {
          road: el,
          cnt: data,
          roadLength: Math.round(roadLength * 100) / 100
        }
        this.tableData.push(obj)
        this.tableData.sort((a, b) => a.roadLength - b.roadLength).reverse()
      })

      this.pathSimplifierData = arr
      this.isShow = true
      this.laoding = false
    },
    getRoadAnArea() {
      this.laoding = true
      this.tableData = []
      this.pathSimplifierData = []
      getBoundariesMapData({ city: '海南省' }).then(res => {
        res.data.features.forEach(item => {
          this.tableData.push(
            {
              road: item.properties.name,
            }
          )
        })
        this.laoding = false
      }).catch(e => {
        this.laoding = false
        console.log(e)
      })
    },
    getHaversine(lat1, lon1, lat2, lon2) {
      const R = 6371; // 地球半径，单位为公里
      const dLat = (lat2 - lat1) * (Math.PI / 180);
      const dLon = (lon2 - lon1) * (Math.PI / 180);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) *
          Math.cos((lat2 * Math.PI) / 180) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;
      return distance;
    },
    handleCurrentChange(index, row) {
      // 当前选中行
      if (this.selectedId == index) {
        index = ''
      }
      this.selectedId = index
      if (this.selectedId == '') {
        this.handleGetHotRoad()
        return
      }
      // 更新地图道路展示
      const road = []
      const roadName = row?.road
      roadPath2.forEach(el => {
        el.line_string.forEach(el2 => {
          // 将道路信息添加到数组中
          if (roadName == el.name) {
            // 计算出当前道路长度
            const km = this.getHaversine(
              el2[0][0], el2[0][1],
              el2[el2.length - 1][0], el2[el2.length - 1][1]
            )
            const obj = {
              vin: el.name,
              name: el.name,
              traffic: '',
              path: el2,
              km: km
            }
            road.push(obj)
          }
        })
      })
      this.pathSimplifierData = road
    },
    handleGetCarList() {
      this.carList = [
      { name: '上汽依维柯商用', id: '1' },
      { name: '福田戴姆勒', id: '2' },
      ];
    },
    handleDetail(e) {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false  
    },
    handleCarInfo(data) {
      console.log('==>', data.name, data.id)
    },
    handleClick(e) {
      this.activeName = e.name
      if (e.name == 'first') {
        this.handleGetHotRoad();
      } else {
        // 按区域查询
        this.getRoadAnArea();
      }
    },
    handleCommand(e) {
      this.commandName = e
    },
    getRandomInt(min, max) {
      min = Math.ceil(min);
      max = Math.floor(max);
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    handleRate(data) {
      data = Number(data)
      let rate = ((data * 10000) * 100) / 10000
      rate = rate.toFixed(2)
      return rate + '%'
    },
  }
};
</script>

<style scoped lang="scss">
.trajectory-container {
  width: 100%;
  height: calc(100vh - 85px);
  background-color: #011a33;
  box-sizing: border-box;
  padding: 10px 10px;
  display: flex;
  justify-content: center;
  .left {
    width: calc(100% - 600px);
    height: 100%;
    padding-right: 10px;
    box-sizing: border-box;
    .echarts-query {
      width: 100%;
      height: 30px;
      line-height: 30px;
      color: #fff;
      padding-top: 5px;
      padding-bottom: 5px;
      box-sizing: border-box;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        font-weight: bold;
      }
      ::v-deep .el-radio {
        color: #fff;
      }
    }
    .info {
      width: 100%;
      height: 200px;
      border-radius: 6px;
      box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
      background-color: rgba(256, 256, 256, 0.1);
    }
    .map {
      width: 100%;
      height: calc(100% - 240px);
      margin-top: 10px;
      box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.3);
      border-radius: 6px;
      overflow: hidden;
    }
  }
  .right {
    width: 600px;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
    padding-top: 17px;
    .car-list {
      width: 100%;
      height: 100%;
      padding: 10px 10px;
      box-sizing: border-box;
      .table-container {
        width: 100%;
        height: 100%;
        margin-right: 20px;
        color: white;
        font-size: 13px;
        font-weight: bold;
        .table-head {
          width: 100%;
          height: 43px;
          line-height: 43px;
          text-align: center;
          background: #1f486e;
          border: 1px #06c solid;
          display: flex;
          justify-content: flex-start;
        }
        .table-body {
          width: 100%;
          height: calc(100% - 35px);
          overflow-y: auto;
          .table-body-row {
            width: 100%;
            height: 43px;
            line-height: 43px;
            text-align: center;
            border-bottom: 1px #013968 solid;
            border-left: 1px #013968 solid;
            border-right: 1px #013968 solid;
            display: flex;
            justify-content: flex-start;
          }
          .table-body-row:hover {
            cursor: pointer;
            box-shadow: 3px 3px 5px rgba(230, 159, 249, 0.3);
            background: linear-gradient(to right, rgba(216, 100, 248, 0.1) 30%, rgba(216, 100, 248, 0.7) 50%) right;
            background-size: 120%;
            transition: .5s ease-in-out;
            /* 应用动画到元素 */
            .bouncy {
              animation: bounce 0.7s infinite;
            }
          }
          .active {
            color: rgba(216, 100, 248, 0.7);
            font-size: 15px;
          }
          .txt_left {
            text-align: left;
          }
          /* 定义弹跳动画 */
          @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
          }
        }
        .table-column {
          width: calc(100% / 5);
          height: 100%;
          overflow: hidden; /* 确保溢出的内容会被隐藏 */
          white-space: nowrap; /* 确保文本在一行内显示，不换行 */
          text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
        }
      }
    }
  }

  .car-info {
    width: 100%;
    display: flex;
    justify-content: left;
  }
  ::v-deep .el-icon-location {
    color: rgba(216, 100, 248, 1);
    margin-right: 5px;
  }
  ::v-deep .dialog-detail {
    width: 100%;
    height: calc(100vh - 300px);
    overflow-y: auto;
  }
  ::v-deep .el-tag {
    width: calc(100% / 8);
    margin-bottom: 10px;
    margin-right: 10px;
  }
  ::v-deep .el-tag:hover {
    box-shadow: 3px 3px 3px rgba(64, 158, 255, 0.6);
    cursor: pointer;
  }
  ::v-deep .el-tabs__item {
    color: #fff;
  }
  ::v-deep .el-tabs__item.is-active {
    color: #1890ff;
  }
  ::v-deep .el-tabs__active-bar {
    height: 4px;
    border-radius: 4px;
  }
  ::v-deep .el-tabs__nav-wrap::after {
    border-radius: 2px;
  }
  ::v-deep .el-tabs__content {
    width: 100%;
    height: calc(100% - 60px);
  }
  ::v-deep .el-dropdown {
    color: #fff;
    font-weight: bold;
    text-decoration: underline;
  }
  ::v-deep .el-dialog__body {
    height: 600px;
    overflow-y: auto;
  }
  // 设置背景地图左右下角标文字颜色
  ::v-deep #location, ::v-deep .amap-copyright {
    // color: #ffffff;
  }
}
</style>
<style>
  .el-card__body {
    padding: 10px 10px !important;
    box-sizing: border-box;
  }
</style>