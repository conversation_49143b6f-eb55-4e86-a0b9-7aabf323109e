<template>
  <div class="detail">
    <!-- 搜索条件 -->
    <div class="search-info">
      <div class="search-row">
        <!-- 企业名称 -->
        <div class="search-item">
          <div class="search-title">企业名称:</div>
          <el-input
            class="w_200"
            v-model="tableQuery.company"
            size="small"
            clearable
            placeholder="请输入"
          />
        </div>
        <!-- 省、市 -->
        <div class="search-item">
          <div class="search-title">省、市:</div>
          <el-cascader
            v-model="tableQuery.area"
            :options="areaOption"
            :props="{
              multiple: true,
              checkStrictly: true,
              value: 'code',
              label: 'name',
              children: 'children',
            }"
            clearable
          ></el-cascader>
        </div>
      </div>
      <div class="search-row">
        <!-- 日期 -->
        <div class="search-item">
          <div class="search-title">日期:</div>
          <el-date-picker
              class="w_200"
              v-model="tableQuery.date"
              size="small"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
        </div>
        <el-button type="primary" icon="el-icon-search" size="small">查询</el-button>
        <el-button type="primary" icon="el-icon-refresh-left" size="small">重置</el-button>
      </div>
    </div>
    <div class="separator">详情信息</div>
    <!-- 表格 -->
    <div class="table-container">
      <my-table
        :columns="columns"
        :tableData="tableData"
        :operate="operate"
      ></my-table>
    </div>
  </div>
</template>


<script>
// 公共方法
import { getTime } from "@/utils/index"
// 导入组件
import myTable from '@/components/table/index'
// mock
import country from '../../mock/country.json'
import ribao from '../../mock/ribao.json'


const defaultDate = [new Date(getTime('start')), getTime()]
const defaultQuery = {
  area: '',
  date: defaultDate
}
export default {
  name: "Tamper",
  components: {
    myTable
  },
  data() {
    return {
      tableQuery: Object.assign({}, defaultQuery),
      areaOption: country,
      columns: [
        { prop: 'vin', label: 'VIN', width: 200 },
        { prop: 'cum_resturea_pct', label: '尿素消耗量(%)', width: 130 },
        { prop: 'cum_resturea', label: '尿素消耗量(L)', width: 130 },
        { prop: 'cum_fuelflow', label: '燃料消耗量', width: 130 },
        { prop: 'cum_odometer', label: '行驶里程' },
        { prop: 'avg_nox_o_0_3000', label: 'SCR下游NOx浓度日均值', width: 180 },
        { prop: 'nox_o_00', label: '0以下帧数' },
        { prop: 'nox_o_0', label: '0帧数' },
        { prop: 'nox_o_0_50', label: '(0-50]帧数', width: 130 },
        { prop: 'nox_o_50_100', label: '(50-100]帧数', width: 130 },
        { prop: 'nox_o_100_120', label: '(100-120]帧数', width: 130 },
        { prop: 'nox_o_120_150', label: '(120-150]帧数', width: 130 },
        { prop: 'nox_o_150_200', label: '(150-200]帧数', width: 130 },
        { prop: 'nox_o_200_250', label: '(200-250]帧数', width: 130 },
        { prop: 'nox_o_250_300', label: '(250-300]帧数', width: 130 },
        { prop: 'nox_o_300_350', label: '(300-350]帧数', width: 130 },
        { prop: 'nox_o_350_400', label: '(350-400]帧数', width: 130 },
        { prop: 'nox_o_400_450', label: '(400-450]帧数', width: 130 },
        { prop: 'nox_o_450_500', label: '(450-500]帧数', width: 130 },
        { prop: 'nox_o_500_3000', label: '(500-3000]帧数', width: 130 },
        { prop: 'nox_o_3000_65535', label: '3000以上帧数', width: 130 },
        { prop: 'nox_o_65535', label: '65535帧数', width: 130 },
        { prop: 'rest_fuel_rate', label: '尿燃比' },
        { prop: 'avg_nox_i', label: '入口温度大于230>度时SCR上游NOx浓度均值', width: 160 },
        { prop: 'avg_nox_o', label: '入口温度大于230度时SCR下游NOx浓度均值', width: 160 },
        { prop: 'scr_transform_rate', label: 'SCR转换率', width: 100 },
        { prop: 'cailid', label: 'calid' },
        { prop: 'cvn', label: 'cvn' },	
        { prop: 'y', label: '年' },	
        { prop: 'm', label: '月' },	
        { prop: 'd', label: '日' },											
      ],
      tableData: [],
      operate: []
    };
  },
  created() {
    this.handleSearch()
  },
  mounted() {
  },
  methods: {
    handleSearch() {
      this.tableData = ribao || []
    },
  }
};
</script>

<style scoped lang="scss">
.detail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 20px;
  .search-info {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    margin-bottom: 10px;
    .search-row {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      margin-bottom: 10px;
    }
    .search-item {
      width: auto;
      display: flex;
      align-items: center;
      margin-right: 10px;
      .search-title {
        width: auto;
        min-width: 80px;
        font-size: 13px;
        text-align: right;
        color: #000;
        box-sizing: border-box;
        padding-right: 5px;
      }
    }
  }
  .separator {
    width: 100%;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .table-container {
    width: 100%;
    height: auto;
    box-sizing: border-box;
  }
  .w_200 {
    width: 200px;
  }
}
</style>
