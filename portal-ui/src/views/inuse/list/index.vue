<template>
  <div class="detail">
    <!-- 搜索条件 -->
    <div class="search-info">
      <div class="search-row">
        <!-- 企业名称 -->
        <div class="search-item">
          <div class="search-title">企业名称:</div>
          <el-input
            class="w_200"
            v-model="tableQuery.company"
            size="small"
            clearable
            placeholder="请输入"
          />
        </div>
        <!-- 车辆类型 -->
        <div class="search-item">
          <div class="search-title">车辆类型:</div>
          <el-input
            class="w_200"
            v-model="tableQuery.carType"
            size="small"
            clearable
            placeholder="请输入"
          />
        </div>
        <!-- 省、市 -->
        <div class="search-item">
          <div class="search-title">省、市:</div>
          <el-cascader
            v-model="tableQuery.area"
            :options="areaOption"
            :props="{
              multiple: true,
              checkStrictly: true,
              value: 'code',
              label: 'name',
              children: 'children',
            }"
            clearable
          ></el-cascader>
        </div>
      </div>
      <div class="search-row">
        <!-- 日期 -->
        <div class="search-item">
          <div class="search-title">日期:</div>
          <el-date-picker
            class="w_230"
            v-model="tableQuery.date"
            size="small"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </div>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleSearch">查询</el-button>
        <el-button type="primary" icon="el-icon-refresh-left" size="small" @click="handleRset">重置</el-button>
      </div>
    </div>
    <!-- 汇总信息 -->
    <div class="data-sum">
      <el-row :gutter="20" style="width: 100%;">
        <el-col :span="8" class="data-sum-col">
          <div class="data-sum-title">高排车辆总数(辆)</div>
          <div class="data-sum-value">{{ allCarCount }}</div>
        </el-col>
        <el-col :span="8" class="data-sum-col">
          <div class="data-sum-title">最大高排车辆类型</div>
          <div class="data-sum-value">{{  vehicleType }}</div>
        </el-col>
        <el-col :span="8" class="data-sum-col">
          <div class="data-sum-title">最大排放比(%)</div>
          <div class="data-sum-value">{{ maxNoxMassMean }}</div>
        </el-col>
      </el-row>
    </div>
    <div class="separator">详情信息</div>
    <div class="echart-container">
      <div class="echart-box">
        <echart-bar barType="valueX" :xAxisData="xAxisData" xAxisName="企业" yAxisName="数量" :seriesData="seriesData" titleText=""
          titleColor="#515a6e" :isClick="false"></echart-bar>
      </div>
      <div class="echart-box">
        <echart-pie
          :pieData="pieData"
        ></echart-pie>
      </div>
    </div>
    <!-- 表格 -->
    <div class="table-container">
      <my-table
        :columns="columns || []"
        :tableData="tableData || []"
        :operate="operate"
        :isAutoHeight="false"
      ></my-table>
    </div>
  </div>
</template>


<script>
// 公共方法
import { getTime, qian } from "@/utils/index"
// 导入组件
import myTable from '@/components/table/index'
import echartBar from '@/components/echart/bar/index'
import echartPie from '@/components/echart/pie/index'
// mock
import country from '../../mock/country.json'
import gaopai from './gaopai.json'


const defaultDate = [new Date(getTime('start')), getTime()]
const defaultQuery = {
  company: '',
  carType: '',
  area: '',
  date: defaultDate
}
export default {
  name: "HighCarList",
  components: {
    myTable,
    echartBar,
    echartPie
  },
  data() {
    return {
      // 汇总数
      allCarCount: 0,
      vehicleType: '牵引汽车',
      maxNoxMassMean: 0,
      // 表格
      tableQuery: Object.assign({}, defaultQuery),
      columns: [
        {
          prop: "vin",
          label: "VIN码",
          width: 220,
        },{
          prop: "nox_mass_mean",
          label: "Nox浓度",
        },{
          prop: "nox_gkwh1",
          label: "比排放",
        },{
          prop: "vehicle_type",
          label: "车辆类型",
        },{
          prop: "manufacturer_build_name",
          label: "企业",
        },{
          prop: "city",
          label: "省、市",
        },{
          prop: "pt_date",
          label: "日期",
        }
      ],
      tableData: [],
      operate: ['轨迹'],
      areaOption: country,
      // echarts
      xAxisData: ['东风汽车', '中国重汽', '其他', '上汽依维柯商用', '东风商用', '中联重科', '大运汽车'],
      seriesData: [13, 34, 19, 53, 15, 21, 15],
      pieData: [
        {
            "name": "自卸汽车",
            "value": 45
        },
        {
            "name": "专用汽车",
            "value": 30
        },
        {
            "name": "载货汽车",
            "value": 96
        },
        {
            "name": "牵引汽车",
            "value": 6
        },
      ],
    };
  },
  created() {
    this.handleSearch()
  },
  mounted() {
    this.numberScroll('allCarCount', 300, 320, 200)
    this.numberScroll('maxNoxMassMean', 55, 62, 200)
  },
  methods: {
    handleSearch() {
      this.tableData = gaopai
    },
    handleRset() {
      this.tableData = gaopai
    },
    // 数字滚动
    numberScroll(key, startNumber, endNumber, duration) {
      let currentNumber = startNumber;
      const increment = (endNumber - startNumber) / duration;
      const interval = setInterval(() => {
        currentNumber += increment;
        this[key] = qian(Math.round(currentNumber))
        if (currentNumber >= endNumber) {
          clearInterval(interval);
        }
      }, 1);
    },
  }
};
</script>

<style scoped lang="scss">
.detail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 20px;
  .search-info {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    margin-bottom: 10px;
    .search-row {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      margin-bottom: 10px;
    }
    .search-item {
      width: auto;
      display: flex;
      align-items: center;
      margin-right: 10px;
      .search-title {
        width: auto;
        min-width: 100px;
        font-size: 13px;
        text-align: right;
        color: #000;
        box-sizing: border-box;
        padding-right: 5px;
      }
    }
  }
  
  .data-sum {
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: left;

    .el-row {
      width: 100%;
      display: flex;
      align-items: center;
      .data-sum-col {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .data-sum-title {
          font-size: 1.1rem;
          font-weight: bold;
          background-image: -webkit-linear-gradient(left, #1890ff, #2eb9f5 25%, #1890ff 50%, #2eb9f5 75%, #1890ff);
          -webkit-text-fill-color: transparent;
          -webkit-background-clip: text;
          -webkit-background-size: 200% 100%;
          text-shadow: 3px 3px 6px rgba(20, 123, 150, 0.5);
        }
        .data-sum-value {
          font-size: 1rem;
          color: #2a2a2a;
          margin-top: 5px;
        }
      }
    }
  }
  .separator {
    width: 100%;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .echart-container {
    width: 100%;
    height: 300px;
    margin-bottom: 10px;
    box-sizing: border-box;
    padding: 10px 10px;
    display: flex;
    justify-content: space-around;
    .echart-box {
      width: calc((100% - 20px) / 3);
      height: 100%;
      border-radius: 6px;
      box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
    }
  }
  .table-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }
  .w_230 {
    width: 230px;
  }
}
</style>
