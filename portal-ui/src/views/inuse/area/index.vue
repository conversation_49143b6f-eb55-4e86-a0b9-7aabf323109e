<template>
  <div class="detail">
    <!-- 搜索条件 -->
    <div class="search-info">
      <div class="search-row">
        <!-- 车牌号码 -->
        <div class="search-item">
          <div class="search-title">车牌号码:</div>
          <el-input
            class="w_200"
            v-model="tableQuery.carNo"
            size="small"
            placeholder="请输入"
          />
        </div>
        <!-- 车辆类型 -->
        <div class="search-item">
          <div class="search-title">车辆类型:</div>
          <el-select
            class="w_200"
            v-model="tableQuery.carType"
            placeholder="请选择"
            size="small"
          >
            <el-option
              v-for="item in carOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <!-- 生产厂商 -->
        <div class="search-item">
          <div class="search-title">生产厂商:</div>
          <el-select
            class="w_200"
            v-model="tableQuery.carBrand"
            placeholder="请选择"
            size="small"
          >
            <el-option
              v-for="item in carBrandOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="search-row">
        <!-- 排量 -->
        <div class="search-item">
          <div class="search-title">排量:</div>
          <el-input
            class="w_200"
            v-model="tableQuery.output"
            size="small"
            placeholder="请输入"
          />
        </div>
        <!-- 日期 -->
        <div class="search-item">
          <div class="search-title">日期:</div>
          <el-date-picker
              class="w_200"
              v-model="tableQuery.date"
              size="small"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
        </div>
      </div>
    </div>
    <div class="separator">详情信息</div>
    <!-- 高排区域地图 -->
    <div
      class="map-container"
      v-loading="loading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <my-map
        v-if="isShow"
        :isHeatmap="isHeatmap"
        :hotMapData="hotMapData"
        @handleGetHotMap="handleGetHotMap"
      ></my-map>
    </div>
  </div>
</template>


<script>
// 公共方法
import { getTime } from "@/utils/index"
// Api
import { getHotMapData } from "@/api/maps/map";
// 导入组件
import myMap from '@/components/amap/myMap'


const defaultDate = [new Date(getTime('start')), getTime()]
const defaultQuery = {
  carNo: '',
  carType: '',
  carBrandO: '',
  output: '',
  date: defaultDate
}
export default {
  name: "Detail",
  components: {
    myMap
  },
  data() {
    return {
      loading: false,
      tableQuery: Object.assign({}, defaultQuery),
      carOptions: [
        {
          value: '1',
          label: '自卸'
        },
        {
          value: '2',
          label: '牵引'
        },
        {
          value: '3',
          label: '拖挂'
        }
      ],
      carBrandOptions: [
        {
          value: '1',
          label: '1'
        },
        {
          value: '2',
          label: '2'
        },
        {
          value: '3',
          label: '3'
        }
      ],
      // 热力图
      isShow: false,
      isHeatmap: true,
      hotMapData: [],
    };
  },
  created() {
    this.handleGetHotMap()
  },
  mounted() {
  },
  methods: {
    // 查询热力图层数据
    handleGetHotMap(data) {
      this.loading = true
      getHotMapData({
        params1: 1,
        params2: 2,
        params3: 3
      }).then(res => {
        this.hotMapData = res?.data
        this.loading = false
        this.isShow = true
      }).catch(err => {
        this.loading = false
      })
    }
  }
};
</script>

<style scoped lang="scss">
.detail {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  padding: 20px 20px;
  display: flex;
  flex-direction: column;
  .search-info {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    margin-bottom: 10px;
    .search-row {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      margin-bottom: 10px;
    }
    .search-item {
      width: auto;
      display: flex;
      align-items: center;
      margin-right: 10px;
      .search-title {
        width: auto;
        min-width: 100px;
        font-size: 13px;
        text-align: right;
        color: #000;
        box-sizing: border-box;
        padding-right: 5px;
      }
    }
  }
  .separator {
    width: 100%;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .map-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }
  .w_200 {
    width: 200px;
  }
}
</style>
