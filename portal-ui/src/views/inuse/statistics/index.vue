<template>
    <div class="detail">
      <!-- 搜索条件 -->
      <div class="search-info">
        <div class="search-row">
          <!-- 车辆VIN码 -->
          <div class="search-item">
            <div class="search-title">车辆VIN码:</div>
            <el-input
              class="w_200"
              v-model="tableQuery.carVIN"
              size="small"
              placeholder="请输入"
            />
          </div>
          <!-- 日期 -->
          <div class="search-item">
            <div class="search-title">日期:</div>
            <el-date-picker
                class="w_230"
                v-model="tableQuery.date"
                size="small"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
          </div>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleSearch">查询</el-button>
          <el-button type="primary" size="small" @click="handleExport">
            导出<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </div>
      </div>
      <div class="separator">详情信息</div>
      <!-- 表格 -->
      <div class="table-container">
        <my-table
          :operate="operate"
        ></my-table>
      </div>
    </div>
  </template>
  
  
  <script>
  // 公共方法
  import { getTime } from "@/utils/index"
  // 导入组件
  import myTable from '@/components/table/index'
  
  
  const defaultDate = [new Date(getTime('start')), getTime()]
  const defaultQuery = {
    carVIN: '',
    carType: '',
    carBrandO: '',
    output: '',
    date: defaultDate
  }
  export default {
    name: "statistics",
    components: {
      myTable
    },
    data() {
      return {
        tableQuery: Object.assign({}, defaultQuery),
        operate: ['信息'],
      };
    },
    created() {
    },
    mounted() {
    },
    methods: {
      handleSearch() {
  
      },
      handleExport() {

      },
    }
  };
  </script>
  
  <style scoped lang="scss">
  .detail {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 20px 20px;
    .search-info {
      width: 100%;
      height: auto;
      box-sizing: border-box;
      margin-bottom: 10px;
      .search-row {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        margin-bottom: 10px;
      }
      .search-item {
        width: auto;
        display: flex;
        align-items: center;
        margin-right: 10px;
        .search-title {
          width: auto;
          min-width: 100px;
          font-size: 13px;
          text-align: right;
          color: #000;
          box-sizing: border-box;
          padding-right: 5px;
        }
      }
    }
    .separator {
      width: 100%;
      font-size: 13px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .table-container {
      width: 100%;
      height: auto;
      box-sizing: border-box;
    }
    .w_200 {
      width: 200px;
    }
    .w_230 {
      width: 230px;
    }
  }
  </style>
  