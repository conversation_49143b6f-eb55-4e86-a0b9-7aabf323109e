package com.portal.common.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Geometry 数据压缩工具类
 * 
 * <AUTHOR>
 */
public class GeometryCompressionUtil {
    
    private static final Logger log = LoggerFactory.getLogger(GeometryCompressionUtil.class);
    
    /**
     * 压缩阈值：超过此大小的字符串才进行压缩（单位：字符）
     */
    private static final int COMPRESSION_THRESHOLD = 10000;
    
    /**
     * 压缩标识前缀
     */
    private static final String COMPRESSED_PREFIX = "GZIP_BASE64:";

    /**
     * 压缩结果缓存，避免重复压缩相同数据
     */
    private static final ConcurrentHashMap<String, String> compressionCache = new ConcurrentHashMap<>();

    /**
     * 缓存最大大小
     */
    private static final int MAX_CACHE_SIZE = 1000;
    
    /**
     * 压缩字符串数据
     * 
     * @param data 原始字符串数据
     * @return 压缩后的字符串（如果数据较小则不压缩）
     */
    public static String compress(String data) {
        if (data == null || data.length() < COMPRESSION_THRESHOLD) {
            return data;
        }
        
        try {
            // 将字符串转换为字节数组
            byte[] inputBytes = data.getBytes("UTF-8");
            
            // 使用 GZIP 压缩
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
                gzipOut.write(inputBytes);
                gzipOut.finish();
            }
            
            // 将压缩后的字节数组转换为 Base64 字符串
            byte[] compressedBytes = baos.toByteArray();
            String compressedData = Base64.getEncoder().encodeToString(compressedBytes);
            
            // 添加压缩标识前缀
            String result = COMPRESSED_PREFIX + compressedData;
            
            // 计算压缩率
            double compressionRatio = (double) result.length() / data.length();
            log.debug("Geometry compression: {} -> {} chars, ratio: {:.2f}", 
                     data.length(), result.length(), compressionRatio);
            
            // 如果压缩后反而更大，则返回原数据
            if (result.length() >= data.length()) {
                log.debug("Compression not beneficial, returning original data");
                return data;
            }
            
            return result;
            
        } catch (IOException e) {
            log.error("Failed to compress geometry data", e);
            return data; // 压缩失败时返回原数据
        }
    }
    
    /**
     * 解压缩字符串数据
     * 
     * @param compressedData 压缩后的字符串数据
     * @return 解压缩后的原始字符串
     */
    public static String decompress(String compressedData) {
        if (compressedData == null || !compressedData.startsWith(COMPRESSED_PREFIX)) {
            return compressedData; // 未压缩的数据直接返回
        }
        
        try {
            // 移除压缩标识前缀
            String base64Data = compressedData.substring(COMPRESSED_PREFIX.length());
            
            // 将 Base64 字符串转换为字节数组
            byte[] compressedBytes = Base64.getDecoder().decode(base64Data);
            
            // 使用 GZIP 解压缩
            ByteArrayInputStream bais = new ByteArrayInputStream(compressedBytes);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            try (GZIPInputStream gzipIn = new GZIPInputStream(bais)) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = gzipIn.read(buffer)) != -1) {
                    baos.write(buffer, 0, len);
                }
            }
            
            // 将解压缩后的字节数组转换为字符串
            return baos.toString("UTF-8");
            
        } catch (IOException e) {
            log.error("Failed to decompress geometry data", e);
            return compressedData; // 解压缩失败时返回原数据
        }
    }
    
    /**
     * 检查字符串是否已压缩
     * 
     * @param data 字符串数据
     * @return 是否已压缩
     */
    public static boolean isCompressed(String data) {
        return data != null && data.startsWith(COMPRESSED_PREFIX);
    }
    
    /**
     * 获取压缩阈值
     *
     * @return 压缩阈值
     */
    public static int getCompressionThreshold() {
        return COMPRESSION_THRESHOLD;
    }

    /**
     * 专门用于几何数据的压缩方法
     * 对几何数据强制压缩，使用缓存避免重复压缩相同数据
     *
     * @param geometryData 几何数据字符串
     * @return 压缩后的字符串
     */
    public static String compressGeometry(String geometryData) {
        if (geometryData == null || geometryData.isEmpty()) {
            return geometryData;
        }

        // 检查缓存
        String cacheKey = String.valueOf(geometryData.hashCode());
        String cached = compressionCache.get(cacheKey);
        if (cached != null) {
            log.debug("Using cached compression for geometry data, size: {}", geometryData.length());
            return cached;
        }

        // 控制缓存大小
        if (compressionCache.size() >= MAX_CACHE_SIZE) {
            compressionCache.clear();
            log.debug("Compression cache cleared due to size limit");
        }

        // 强制压缩几何数据
        String result = compress(geometryData);

        // 缓存压缩结果
        compressionCache.put(cacheKey, result);
        log.debug("Cached compression result for geometry data, cache size: {}", compressionCache.size());

        return result;
    }
}
