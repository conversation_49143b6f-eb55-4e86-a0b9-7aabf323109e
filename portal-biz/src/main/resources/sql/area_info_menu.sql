
-- 通用区域查询接口权限
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2100, '通用区域查询', 2000, 1, '', '', '', 1, 0, 'F', '0', '0', 'area_city_query:area:list', '#', 'admin', SYSDATE(), '', NULL, '通用区域查询接口，支持省市区三级查询');

-- 省份列表查询接口权限
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2101, '省份列表查询', 2000, 2, '', '', '', 1, 0, 'F', '0', '0', 'area_city_query:area:province_list', '#', 'admin', SYSDATE(), '', NULL, '获取省份列表');

-- 城市列表查询接口权限
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2102, '城市列表查询', 2000, 3, '', '', '', 1, 0, 'F', '0', '0', 'area_city_query:area:city_list', '#', 'admin', SYSDATE(), '', NULL, '获取城市列表');

-- 区县列表查询接口权限
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2103, '区县列表查询', 2000, 4, '', '', '', 1, 0, 'F', '0', '0', 'area_city_query:area:district_list', '#', 'admin', SYSDATE(), '', NULL, '获取区县列表');
