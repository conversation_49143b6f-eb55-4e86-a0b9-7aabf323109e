# Portal-Biz 模块接口地址汇总

## 模块概览

| 模块名称 | 控制器类 | 基础路径 | 功能描述 |
|----------|----------|----------|----------|
| shiyunhui_daily_report | VehicleOnlineController | /shiyunhui/vehicle | 车辆上线统计分析 |
| obd_vehicle_dailyStatic | VehicleDailyStaticController | /daily_static | 车辆每日统计分析 |
| obd_dataquality_score | VehicleDataQualityController | /dataquality_score | 车辆数据质量分析 |
| area_city_query | AreaInfoController | /system/area | 区域信息查询管理 |

## 详细接口列表

### 1. shiyunhui_daily_report 模块

**基础路径**: `/shiyunhui/vehicle`

| 序号 | 接口名称 | HTTP方法 | 完整URL | 权限标识 | 功能描述 |
|------|----------|----------|---------|----------|----------|
| 1 | 获取车辆上线统计数据 | GET | `/shiyunhui/vehicle/online_stats` | `shiyunhui:vehicle:online_stats` | 获取车辆上线统计数据 |
| 2 | 获取单车每日行驶里程 | GET | `/shiyunhui/vehicle/drive_odometer` | `shiyunhui:vehicle:drive_odometer` | 获取单车每日行驶里程（分页） |
| 3 | 获取热点时段车辆数 | GET | `/shiyunhui/vehicle/hot_hour` | `shiyunhui:vehicle:hot_hour` | 获取热点时段车辆数统计 |
| 4 | 获取热点区域车辆数 | GET | `/shiyunhui/vehicle/hot_area` | `shiyunhui:vehicle:hot_area` | 获取热点区域车辆数统计 |
| 5 | 获取总行驶里程统计 | GET | `/shiyunhui/vehicle/total_odometer` | `shiyunhui:vehicle:total_odometer` | 获取总行驶里程和超排车辆数统计 |
| 6 | 获取热点城市统计 | GET | `/shiyunhui/vehicle/hot_area_stats` | `shiyunhui:vehicle:hot_area_stats` | 获取上线车辆数最多的城市 |

### 2. obd_vehicle_dailyStatic 模块

**基础路径**: `/daily_static`

| 序号 | 接口名称 | HTTP方法 | 完整URL | 权限标识 | 功能描述 |
|------|----------|----------|---------|----------|----------|
| 1 | 获取省份列表 | GET | `/daily_static/province_list` | `daily_static:province_list` | 获取所有有数据的省份 |
| 2 | 获取区域列表 | GET | `/daily_static/area_list/{parentAdcode}` | `daily_static:area_list` | 根据父级编码获取区域列表 |
| 3 | 燃料类型统计查询 | GET | `/daily_static/fuel_type_stats` | `daily_static:fuel_type_stats` | 按燃料类型统计车辆数据（分页） |
| 4 | 燃料类型统计导出 | POST | `/daily_static/fuel_type_stats/export` | `daily_static:fuel_type_stats:export` | 导出燃料类型统计数据 |
| 5 | 协议类型统计查询 | GET | `/daily_static/agreement_stats` | `daily_static:agreement_stats` | 按车辆协议统计车辆数据（分页） |
| 6 | 协议类型统计导出 | POST | `/daily_static/agreement_stats/export` | `daily_static:agreement_stats:export` | 导出协议类型统计数据 |

### 3. obd_dataquality_score 模块

**基础路径**: `/dataquality_score`

| 序号 | 接口名称 | HTTP方法 | 完整URL | 权限标识 | 功能描述 |
|------|----------|----------|---------|----------|----------|
| 1 | 数据质量摘要 | GET | `/dataquality_score/online_info` | `dataquality_score:online_info` | 获取车辆数据质量摘要信息 |
| 2 | 车型数据质量摘要 | GET | `/dataquality_score/online_info_byVtype` | `dataquality_score:online_info_byVtype` | 按车辆类型分组获取数据质量摘要 |
| 3 | 数据越界率指标 | GET | `/dataquality_score/outboundary_rate` | `dataquality_score:outboundary_rate` | 获取数据越界率指标数据 |
| 4 | 数据缺失率指标 | GET | `/dataquality_score/lossrate_rate` | `dataquality_score:lossrate_rate` | 获取数据缺失率指标数据 |
| 5 | 传输数据缺失率 | GET | `/dataquality_score/trans_outboundary_rate` | `dataquality_score:trans_outboundary_rate` | 获取传输数据缺失率指标 |
| 6 | 柴油车VIN缺失率 | GET | `/dataquality_score/diesel_vin_lossrate` | `dataquality_score:diesel_vin_lossrate` | 获取柴油车VIN码数据缺失率（分页） |
| 7 | 柴油车VIN越界率 | GET | `/dataquality_score/diesel_vin_outboundary` | `dataquality_score:diesel_vin_outboundary` | 获取柴油车VIN码数据越界率（分页） |
| 8 | 燃气车VIN缺失率 | GET | `/dataquality_score/gas_vin_lossrate` | `dataquality_score:gas_vin_lossrate` | 获取燃气车VIN码数据缺失率（分页） |
| 9 | 燃气车VIN越界率 | GET | `/dataquality_score/gas_vin_outboundary` | `dataquality_score:gas_vin_outboundary` | 获取燃气车VIN码数据越界率（分页） |
| 10 | 发动机厂家列表 | GET | `/dataquality_score/engine_manufacturer_list` | `dataquality_score:engine_manufacturer_list` | 获取发动机生产厂家列表 |
| 11 | 发动机型号列表 | GET | `/dataquality_score/engine_model_list` | `dataquality_score:engine_model_list` | 获取发动机型号列表 |

### 4. area_city_query 模块

**基础路径**: `/system/area`

| 序号 | 接口名称 | HTTP方法 | 完整URL | 权限标识 | 功能描述 |
|------|----------|----------|---------|----------|----------|
| 1 | 区域信息查询 | GET | `/system/area/list` | `system:area:list` | 获取区域列表（支持省市区三级查询） |
| 2 | 省份列表查询 | GET | `/system/area/province_list` | `system:area:province_list` | 获取所有省份列表 |
| 3 | 城市列表查询 | GET | `/system/area/city_list/{provinceCode}` | `system:area:city_list` | 根据省份编码获取城市列表 |
| 4 | 区县列表查询 | GET | `/system/area/district_list/{cityCode}` | `system:area:district_list` | 根据城市编码获取区县列表 |

## 菜单配置说明

### 菜单层级结构

```
数据分析 (2000)
├── 车辆统计 (2050)
│   ├── 上线车辆统计 (2051)
│   ├── 单车里程统计 (2052)
│   ├── 热点时段分析 (2053)
│   ├── 热点区域分析 (2054)
│   ├── 总里程统计 (2055)
│   ├── 热点城市排名 (2056)
│   ├── 燃料类型统计 (2057)
│   └── 协议类型统计 (2058)
├── 数据质量 (2200)
│   ├── 数据质量摘要 (2201)
│   ├── 车型数据质量 (2202)
│   ├── 数据越界率分析 (2203)
│   ├── 数据缺失率分析 (2204)
│   ├── 传输数据缺失率 (2205)
│   ├── 柴油车VIN缺失率 (2206)
│   ├── 柴油车VIN越界率 (2207)
│   ├── 燃气车VIN缺失率 (2208)
│   └── 燃气车VIN越界率 (2209)
└── 区域管理 (2300)
    ├── 区域信息查询 (2301)
    ├── 省份信息管理 (2302)
    ├── 城市信息管理 (2303)
    └── 区县信息管理 (2304)
```

### 权限标识规范

- **格式**: `模块名:功能:操作`
- **示例**: `shiyunhui:vehicle:online_stats`
- **操作类型**: 
  - 无后缀：基础查询权限
  - `:export`：导出权限
  - `:query`：详细查询权限

### 菜单ID分配规则

- **主菜单目录**: 2000-2099
- **车辆统计菜单**: 2050-2099  
- **数据质量菜单**: 2200-2299
- **区域管理菜单**: 2300-2399
- **功能权限按钮**: 2400-2499

## 使用说明

1. **菜单配置**: 执行 `vehicle_daily_static_menu.sql` 创建完整菜单结构
2. **权限分配**: 根据角色需要分配相应的菜单权限到 `sys_role_menu` 表
3. **前端路由**: 确保前端路由与 `component` 字段保持一致
4. **接口权限**: 后端接口使用 `@PreAuthorize` 注解验证 `perms` 字段中的权限标识
5. **参数说明**: 所有查询接口都支持多维度筛选参数，具体参数详见各模块的DTO类

## 注意事项

- 所有接口都需要登录认证
- 分页接口支持排序和分页参数
- 导出接口支持当前页导出和全量导出
- 区域查询接口支持层级查询（省-市-区县）
- 数据质量接口支持按企业、车型、协议等多维度筛选
