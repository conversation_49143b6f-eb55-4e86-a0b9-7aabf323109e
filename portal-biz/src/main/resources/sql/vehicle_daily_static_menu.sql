-- ========================================
-- Portal-Biz 模块完整菜单配置
-- ========================================

-- ========================================
-- 1. 主菜单目录
-- ========================================

-- 数据分析主菜单
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2000, '数据分析', 0, 5, 'analysis', NULL, '', 1, 0, 'M', '0', '0', '', 'chart', 'admin', now(), '', NULL, '数据分析主菜单');

-- 车辆统计菜单
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2050, '车辆统计', 2000, 1, 'vehicle', NULL, '', 1, 0, 'M', '0', '0', '', 'car', 'admin', now(), '', NULL, '车辆统计菜单');

-- 数据质量菜单
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2200, '数据质量', 2000, 2, 'dataquality', NULL, '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', now(), '', NULL, '数据质量分析菜单');

-- 区域管理菜单
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2300, '区域管理', 2000, 3, 'area', NULL, '', 1, 0, 'M', '0', '0', '', 'location', 'admin', now(), '', NULL, '区域信息管理菜单');

-- ========================================
-- 2. 车辆统计模块 (shiyunhui_daily_report)
-- ========================================

-- 上线车辆统计
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2051, '上线车辆统计', 2050, 1, 'online_stats', 'shiyunhui/vehicle/online_stats', '', 1, 0, 'C', '0', '0', 'shiyunhui:vehicle:online_stats', '#', 'admin', now(), '', NULL, '车辆上线统计数据');

-- 单车里程统计
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2052, '单车里程统计', 2050, 2, 'drive_odometer', 'shiyunhui/vehicle/drive_odometer', '', 1, 0, 'C', '0', '0', 'shiyunhui:vehicle:drive_odometer', '#', 'admin', now(), '', NULL, '单车每日行驶里程统计');

-- 热点时段分析
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2053, '热点时段分析', 2050, 3, 'hot_hour', 'shiyunhui/vehicle/hot_hour', '', 1, 0, 'C', '0', '0', 'shiyunhui:vehicle:hot_hour', '#', 'admin', now(), '', NULL, '24小时热点时段统计');

-- 热点区域分析
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2054, '热点区域分析', 2050, 4, 'hot_area', 'shiyunhui/vehicle/hot_area', '', 1, 0, 'C', '0', '0', 'shiyunhui:vehicle:hot_area', '#', 'admin', now(), '', NULL, '热点区域活动车辆统计');

-- 总里程统计
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2055, '总里程统计', 2050, 5, 'total_odometer', 'shiyunhui/vehicle/total_odometer', '', 1, 0, 'C', '0', '0', 'shiyunhui:vehicle:total_odometer', '#', 'admin', now(), '', NULL, '总行驶里程和超排车辆数统计');

-- 热点城市排名
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2056, '热点城市排名', 2050, 6, 'hot_area_stats', 'shiyunhui/vehicle/hot_area_stats', '', 1, 0, 'C', '0', '0', 'shiyunhui:vehicle:hot_area_stats', '#', 'admin', now(), '', NULL, '上线车辆数最多的城市统计');

-- ========================================
-- 3. 车辆每日统计模块 (obd_vehicle_dailyStatic)
-- ========================================

-- 燃料类型统计
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2057, '燃料类型统计', 2050, 7, 'fuel_type_stats', 'daily_static/fuel_type_stats', '', 1, 0, 'C', '0', '0', 'daily_static:fuel_type_stats', '#', 'admin', now(), '', NULL, '按燃料类型统计车辆数据');

-- 协议类型统计
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2058, '协议类型统计', 2050, 8, 'agreement_stats', 'daily_static/agreement_stats', '', 1, 0, 'C', '0', '0', 'daily_static:agreement_stats', '#', 'admin', now(), '', NULL, '按车辆协议统计车辆数据');

-- ========================================
-- 4. 数据质量模块 (obd_dataquality_score)
-- ========================================

-- 数据质量摘要
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2201, '数据质量摘要', 2200, 1, 'online_info', 'dataquality_score/online_info', '', 1, 0, 'C', '0', '0', 'dataquality_score:online_info', '#', 'admin', now(), '', NULL, '车辆数据质量摘要信息');

-- 车型数据质量
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2202, '车型数据质量', 2200, 2, 'online_info_byVtype', 'dataquality_score/online_info_byVtype', '', 1, 0, 'C', '0', '0', 'dataquality_score:online_info_byVtype', '#', 'admin', now(), '', NULL, '按车辆类型分组的数据质量摘要');

-- 数据越界率分析
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2203, '数据越界率分析', 2200, 3, 'outboundary_rate', 'dataquality_score/outboundary_rate', '', 1, 0, 'C', '0', '0', 'dataquality_score:outboundary_rate', '#', 'admin', now(), '', NULL, '数据越界率指标分析');

-- 数据缺失率分析
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2204, '数据缺失率分析', 2200, 4, 'lossrate_rate', 'dataquality_score/lossrate_rate', '', 1, 0, 'C', '0', '0', 'dataquality_score:lossrate_rate', '#', 'admin', now(), '', NULL, '数据缺失率指标分析');

-- 传输数据缺失率
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2205, '传输数据缺失率', 2200, 5, 'trans_outboundary_rate', 'dataquality_score/trans_outboundary_rate', '', 1, 0, 'C', '0', '0', 'dataquality_score:trans_outboundary_rate', '#', 'admin', now(), '', NULL, '传输数据缺失率指标分析');

-- 柴油车VIN缺失率
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2206, '柴油车VIN缺失率', 2200, 6, 'diesel_vin_lossrate', 'dataquality_score/diesel_vin_lossrate', '', 1, 0, 'C', '0', '0', 'dataquality_score:diesel_vin_lossrate', '#', 'admin', now(), '', NULL, '柴油车VIN码数据缺失率分析');

-- 柴油车VIN越界率
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2207, '柴油车VIN越界率', 2200, 7, 'diesel_vin_outboundary', 'dataquality_score/diesel_vin_outboundary', '', 1, 0, 'C', '0', '0', 'dataquality_score:diesel_vin_outboundary', '#', 'admin', now(), '', NULL, '柴油车VIN码数据越界率分析');

-- 燃气车VIN缺失率
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2208, '燃气车VIN缺失率', 2200, 8, 'gas_vin_lossrate', 'dataquality_score/gas_vin_lossrate', '', 1, 0, 'C', '0', '0', 'dataquality_score:gas_vin_lossrate', '#', 'admin', now(), '', NULL, '燃气车VIN码数据缺失率分析');

-- 燃气车VIN越界率
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2209, '燃气车VIN越界率', 2200, 9, 'gas_vin_outboundary', 'dataquality_score/gas_vin_outboundary', '', 1, 0, 'C', '0', '0', 'dataquality_score:gas_vin_outboundary', '#', 'admin', now(), '', NULL, '燃气车VIN码数据越界率分析');

-- ========================================
-- 5. 区域管理模块 (area_city_query)
-- ========================================

-- 区域信息查询
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2301, '区域信息查询', 2300, 1, 'area_list', 'system/area/area_list', '', 1, 0, 'C', '0', '0', 'system:area:list', '#', 'admin', now(), '', NULL, '区域信息查询管理');

-- 省份信息管理
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2302, '省份信息管理', 2300, 2, 'province_list', 'system/area/province_list', '', 1, 0, 'C', '0', '0', 'system:area:province_list', '#', 'admin', now(), '', NULL, '省份信息管理');

-- 城市信息管理
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2303, '城市信息管理', 2300, 3, 'city_list', 'system/area/city_list', '', 1, 0, 'C', '0', '0', 'system:area:city_list', '#', 'admin', now(), '', NULL, '城市信息管理');

-- 区县信息管理
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2304, '区县信息管理', 2300, 4, 'district_list', 'system/area/district_list', '', 1, 0, 'C', '0', '0', 'system:area:district_list', '#', 'admin', now(), '', NULL, '区县信息管理');

-- ========================================
-- 6. 功能权限按钮配置
-- ========================================

-- 车辆统计模块权限按钮

-- shiyunhui_daily_report 模块按钮权限
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2401, '上线车辆查询', 2051, 1, '', '', '', 1, 0, 'F', '0', '0', 'shiyunhui:vehicle:online_stats', '#', 'admin', now(), '', NULL, '查询上线车辆统计数据');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2402, '单车里程查询', 2052, 1, '', '', '', 1, 0, 'F', '0', '0', 'shiyunhui:vehicle:drive_odometer', '#', 'admin', now(), '', NULL, '查询单车里程数据');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2403, '热点时段查询', 2053, 1, '', '', '', 1, 0, 'F', '0', '0', 'shiyunhui:vehicle:hot_hour', '#', 'admin', now(), '', NULL, '查询热点时段数据');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2404, '热点区域查询', 2054, 1, '', '', '', 1, 0, 'F', '0', '0', 'shiyunhui:vehicle:hot_area', '#', 'admin', now(), '', NULL, '查询热点区域数据');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2405, '总里程查询', 2055, 1, '', '', '', 1, 0, 'F', '0', '0', 'shiyunhui:vehicle:total_odometer', '#', 'admin', now(), '', NULL, '查询总里程统计数据');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2406, '热点城市查询', 2056, 1, '', '', '', 1, 0, 'F', '0', '0', 'shiyunhui:vehicle:hot_area_stats', '#', 'admin', now(), '', NULL, '查询热点城市数据');

-- obd_vehicle_dailyStatic 模块按钮权限
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2407, '省份列表查询', 2057, 1, '', '', '', 1, 0, 'F', '0', '0', 'daily_static:province_list', '#', 'admin', now(), '', NULL, '查询省份列表');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2408, '区域列表查询', 2057, 2, '', '', '', 1, 0, 'F', '0', '0', 'daily_static:area_list', '#', 'admin', now(), '', NULL, '查询区域列表');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2409, '燃料类型统计查询', 2057, 3, '', '', '', 1, 0, 'F', '0', '0', 'daily_static:fuel_type_stats', '#', 'admin', now(), '', NULL, '查询燃料类型统计数据');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2410, '燃料类型统计导出', 2057, 4, '', '', '', 1, 0, 'F', '0', '0', 'daily_static:fuel_type_stats:export', '#', 'admin', now(), '', NULL, '导出燃料类型统计数据');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2411, '协议类型统计查询', 2058, 1, '', '', '', 1, 0, 'F', '0', '0', 'daily_static:agreement_stats', '#', 'admin', now(), '', NULL, '查询协议类型统计数据');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2412, '协议类型统计导出', 2058, 2, '', '', '', 1, 0, 'F', '0', '0', 'daily_static:agreement_stats:export', '#', 'admin', now(), '', NULL, '导出协议类型统计数据');

-- obd_dataquality_score 模块按钮权限
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2413, '数据质量摘要查询', 2201, 1, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:online_info', '#', 'admin', now(), '', NULL, '查询数据质量摘要信息');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2414, '车型数据质量查询', 2202, 1, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:online_info_byVtype', '#', 'admin', now(), '', NULL, '查询车型数据质量信息');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2415, '数据越界率查询', 2203, 1, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:outboundary_rate', '#', 'admin', now(), '', NULL, '查询数据越界率指标');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2416, '数据缺失率查询', 2204, 1, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:lossrate_rate', '#', 'admin', now(), '', NULL, '查询数据缺失率指标');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2417, '传输数据缺失率查询', 2205, 1, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:trans_outboundary_rate', '#', 'admin', now(), '', NULL, '查询传输数据缺失率');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2418, '柴油车VIN缺失率查询', 2206, 1, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:diesel_vin_lossrate', '#', 'admin', now(), '', NULL, '查询柴油车VIN缺失率');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2419, '柴油车VIN越界率查询', 2207, 1, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:diesel_vin_outboundary', '#', 'admin', now(), '', NULL, '查询柴油车VIN越界率');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2420, '燃气车VIN缺失率查询', 2208, 1, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:gas_vin_lossrate', '#', 'admin', now(), '', NULL, '查询燃气车VIN缺失率');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2421, '燃气车VIN越界率查询', 2209, 1, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:gas_vin_outboundary', '#', 'admin', now(), '', NULL, '查询燃气车VIN越界率');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2422, '发动机厂家列表', 2200, 10, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:engine_manufacturer_list', '#', 'admin', now(), '', NULL, '获取发动机生产厂家列表');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2423, '发动机型号列表', 2200, 11, '', '', '', 1, 0, 'F', '0', '0', 'dataquality_score:engine_model_list', '#', 'admin', now(), '', NULL, '获取发动机型号列表');

-- area_city_query 模块按钮权限
INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2424, '区域信息查询', 2301, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:area:list', '#', 'admin', now(), '', NULL, '查询区域信息');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2425, '省份信息查询', 2302, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:area:province_list', '#', 'admin', now(), '', NULL, '查询省份信息');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2426, '城市信息查询', 2303, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:area:city_list', '#', 'admin', now(), '', NULL, '查询城市信息');

INSERT INTO portal.sys_menu (menu_id, menu_name, parent_id, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES(2427, '区县信息查询', 2304, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:area:district_list', '#', 'admin', now(), '', NULL, '查询区县信息');

-- ========================================
-- 7. 接口地址与权限对应关系说明
-- ========================================

/*
Portal-Biz 模块接口地址汇总：

1. shiyunhui_daily_report 模块 (/shiyunhui/vehicle)
   - GET /shiyunhui/vehicle/online_stats        -> shiyunhui:vehicle:online_stats
   - GET /shiyunhui/vehicle/drive_odometer      -> shiyunhui:vehicle:drive_odometer
   - GET /shiyunhui/vehicle/hot_hour            -> shiyunhui:vehicle:hot_hour
   - GET /shiyunhui/vehicle/hot_area            -> shiyunhui:vehicle:hot_area
   - GET /shiyunhui/vehicle/total_odometer      -> shiyunhui:vehicle:total_odometer
   - GET /shiyunhui/vehicle/hot_area_stats      -> shiyunhui:vehicle:hot_area_stats

2. obd_vehicle_dailyStatic 模块 (/daily_static)
   - GET /daily_static/province_list            -> daily_static:province_list
   - GET /daily_static/area_list/{parentAdcode} -> daily_static:area_list
   - GET /daily_static/fuel_type_stats          -> daily_static:fuel_type_stats
   - POST /daily_static/fuel_type_stats/export  -> daily_static:fuel_type_stats:export
   - GET /daily_static/agreement_stats          -> daily_static:agreement_stats
   - POST /daily_static/agreement_stats/export  -> daily_static:agreement_stats:export

3. obd_dataquality_score 模块 (/dataquality_score)
   - GET /dataquality_score/online_info                -> dataquality_score:online_info
   - GET /dataquality_score/online_info_byVtype        -> dataquality_score:online_info_byVtype
   - GET /dataquality_score/outboundary_rate           -> dataquality_score:outboundary_rate
   - GET /dataquality_score/lossrate_rate              -> dataquality_score:lossrate_rate
   - GET /dataquality_score/trans_outboundary_rate     -> dataquality_score:trans_outboundary_rate
   - GET /dataquality_score/diesel_vin_lossrate        -> dataquality_score:diesel_vin_lossrate
   - GET /dataquality_score/diesel_vin_outboundary     -> dataquality_score:diesel_vin_outboundary
   - GET /dataquality_score/gas_vin_lossrate           -> dataquality_score:gas_vin_lossrate
   - GET /dataquality_score/gas_vin_outboundary        -> dataquality_score:gas_vin_outboundary
   - GET /dataquality_score/engine_manufacturer_list   -> dataquality_score:engine_manufacturer_list
   - GET /dataquality_score/engine_model_list          -> dataquality_score:engine_model_list

4. area_city_query 模块 (/system/area)
   - GET /system/area/list                      -> system:area:list
   - GET /system/area/province_list             -> system:area:province_list
   - GET /system/area/city_list/{provinceCode}  -> system:area:city_list
   - GET /system/area/district_list/{cityCode}  -> system:area:district_list

菜单ID分配规则：
- 主菜单目录：2000-2099
- 车辆统计菜单：2050-2099
- 数据质量菜单：2200-2299
- 区域管理菜单：2300-2399
- 功能权限按钮：2400-2499

使用说明：
1. 执行此SQL脚本创建完整的菜单结构
2. 根据角色需要分配相应的菜单权限
3. 前端路由需要与component字段保持一致
4. 接口权限验证使用perms字段中的权限标识
*/