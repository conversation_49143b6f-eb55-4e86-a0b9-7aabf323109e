<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.portal.obd_static_info.mapper.VehicleBasisMapper">

    <resultMap type="com.portal.obd_static_info.domain.VehicleBasis" id="VehicleBasisResult">
        <result property="vin" column="vin"/>
        <result property="enpInfoPublicNum" column="enp_info_public_num"/>
        <result property="epaVehicleType" column="epa_vehicle_type"/>
        <result property="vehCatalogue" column="veh_catalogue"/>
        <result property="vehCatalogueDetail" column="veh_catalogue_detail"/>
        <result property="engineEmissionLeveltype" column="engine_emission_leveltype"/>
        <result property="vehicleLicense" column="vehicle_license"/>
        <result property="vehicleDomicile" column="vehicle_domicile"/>
        <result property="registerAgreement" column="register_agreement"/>
        <result property="vehRegisterMode" column="veh_register_mode"/>
        <result property="companyName" column="company_name"/>
        <result property="platformName" column="platform_name"/>
        <result property="platformAccountName" column="platform_account_name"/>
        <result property="constructionUnit" column="construction_unit"/>
        <result property="platformMode" column="platform_mode"/>
        <result property="manufacturerBuildName" column="manufacturer_build_name"/>
        <result property="registerTime" column="register_time"/>
        <result property="syncStatus" column="sync_status"/>
        <result property="syncTime" column="sync_time"/>
        <result property="activationStatus" column="activation_status"/>
        <result property="activationTime" column="activation_time"/>
        <result property="firstOnlineTime" column="first_online_time"/>
        <result property="firstOnlineStatus" column="first_online_status"/>
        <result property="connectStatus" column="connect_status"/>
        <result property="connectTime" column="connect_time"/>
        <result property="vehicleModel" column="vehicle_model"/>
        <result property="vehicleType" column="vehicle_type"/>
        <result property="vehicleLoads" column="vehicle_loads"/>
        <result property="maxLoads" column="max_loads"/>
        <result property="engineModel" column="engine_model"/>
        <result property="engineManufacturerName" column="engine_manufacturer_name"/>
        <result property="tboxModel" column="tbox_model"/>
        <result property="tboxManufacturerName" column="tbox_manufacturer_name"/>
        <result property="chipPrefix" column="chip_prefix"/>
        <result property="chipModel" column="chip_model"/>
        <result property="chipManufacturerName" column="chip_manufacturer_name"/>
        <result property="manufactureDate" column="manufacture_date"/>
        <result property="country" column="country"/>
        <result property="maximumPower" column="maximum_power"/>
        <result property="maximumEngineRotationlSpeed" column="maximum_engine_rotationl_speed"/>
        <result property="ratedCapacity" column="rated_capacity"/>
        <result property="ratedCapacityRotationlSpeed" column="rated_capacity_rotationl_speed"/>
        <result property="maximumTorque" column="maximum_torque"/>
        <result property="maximumTorqueRotationlSpeed" column="maximum_torque_rotationl_speed"/>
        <result property="fuelFeedSystem" column="fuel_feed_system"/>
        <result property="exhaustAftertreatment" column="exhaust_aftertreatment"/>
        <result property="companyType" column="company_type"/>
        <result property="chassisCompanyName" column="chassis_company_name"/>
        <result property="enginePower" column="engine_power"/>
        <result property="engineTorque" column="engine_torque"/>
        <result property="engineProductionAddress" column="engine_production_address"/>
        <result property="publicTime" column="public_time"/>
        <result property="actualAgreement" column="actual_agreement"/>
        <result property="specialVehType" column="special_veh_type"/>
        <result property="specialVehTerm" column="special_veh_term"/>
    </resultMap>

    <sql id="selectVehicleBasisVo">
        SELECT vin, enp_info_public_num, epa_vehicle_type, veh_catalogue, veh_catalogue_detail, 
        engine_emission_leveltype, vehicle_license, vehicle_domicile, register_agreement, veh_register_mode,
        company_name, platform_name, platform_account_name, construction_unit, platform_mode, 
        manufacturer_build_name, register_time, sync_status, sync_time, activation_status, 
        activation_time, first_online_time, first_online_status, connect_status, connect_time,
        vehicle_model, vehicle_type, vehicle_loads, max_loads, engine_model, 
        engine_manufacturer_name, tbox_model, tbox_manufacturer_name, chip_prefix, chip_model, 
        chip_manufacturer_name, manufacture_date, country, maximum_power, maximum_engine_rotationl_speed, 
        rated_capacity, rated_capacity_rotationl_speed, maximum_torque, maximum_torque_rotationl_speed, 
        fuel_feed_system, exhaust_aftertreatment, company_type, chassis_company_name, engine_power, 
        engine_torque, engine_production_address, public_time, actual_agreement, special_veh_type, special_veh_term
        FROM dw_ods.sys_vehicle_basis_sync
    </sql>

    <select id="selectVehicleBasisList" parameterType="com.portal.obd_static_info.dto.VehicleBasisQueryParam" resultMap="VehicleBasisResult">
        <include refid="selectVehicleBasisVo"/>
        <where>
            <!-- 动态查询参数支持 - 支持数据库表中的所有字段 -->
            <if test="params != null">
                <!-- 基础字段模糊匹配 - 统一使用驼峰命名 -->
                <if test="params.vin != null and params.vin != ''">
                    AND vin LIKE concat('%', #{params.vin}, '%')
                </if>
                <if test="params.epaVehicleType != null and params.epaVehicleType != ''">
                    AND epa_vehicle_type LIKE concat('%', #{params.epaVehicleType}, '%')
                </if>
                <if test="params.vehicleLicense != null and params.vehicleLicense != ''">
                    AND vehicle_license LIKE concat('%', #{params.vehicleLicense}, '%')
                </if>
                <if test="params.vehicleType != null and params.vehicleType != ''">
                    AND vehicle_type LIKE concat('%', #{params.vehicleType}, '%')
                </if>
                <if test="params.engineModel != null and params.engineModel != ''">
                    AND engine_model LIKE concat('%', #{params.engineModel}, '%')
                </if>
                <if test="params.engineManufacturerName != null and params.engineManufacturerName != ''">
                    AND engine_manufacturer_name LIKE concat('%', #{params.engineManufacturerName}, '%')
                </if>
                <if test="params.manufacturerBuildName != null and params.manufacturerBuildName != ''">
                    AND manufacturer_build_name LIKE concat('%', #{params.manufacturerBuildName}, '%')
                </if>
                <if test="params.vehicleLoads != null and params.vehicleLoads != ''">
                    AND vehicle_loads LIKE concat('%', #{params.vehicleLoads}, '%')
                </if>
                <if test="params.engineEmissionLeveltype != null and params.engineEmissionLeveltype != ''">
                    AND engine_emission_leveltype LIKE concat('%', #{params.engineEmissionLeveltype}, '%')
                </if>
                <if test="params.connectStatus != null and params.connectStatus != ''">
                    AND connect_status = #{params.connectStatus}
                </if>
                <if test="params.vehicleDomicile != null and params.vehicleDomicile != ''">
                    AND vehicle_domicile LIKE concat('%', #{params.vehicleDomicile}, '%')
                </if>
                <if test="params.firstOnlineStatus != null and params.firstOnlineStatus != ''">
                    AND first_online_status = #{params.firstOnlineStatus}
                </if>
                <if test="params.syncStatus != null and params.syncStatus != ''">
                    AND sync_status = #{params.syncStatus}
                </if>
                <if test="params.beginTime != null and params.beginTime != ''">
                    AND register_time &gt;= #{params.beginTime}
                </if>
                <if test="params.endTime != null and params.endTime != ''">
                    AND register_time &lt;= #{params.endTime}
                </if>
                <if test="params.enpInfoPublicNum != null and params.enpInfoPublicNum != ''">
                    AND enp_info_public_num LIKE concat('%', #{params.enpInfoPublicNum}, '%')
                </if>
                <!-- 扩展字段模糊匹配 -->
                <if test="params.vehCatalogue != null and params.vehCatalogue != ''">
                    AND veh_catalogue LIKE concat('%', #{params.vehCatalogue}, '%')
                </if>
                <if test="params.vehCatalogueDetail != null and params.vehCatalogueDetail != ''">
                    AND veh_catalogue_detail LIKE concat('%', #{params.vehCatalogueDetail}, '%')
                </if>
                <if test="params.registerAgreement != null and params.registerAgreement != ''">
                    AND register_agreement LIKE concat('%', #{params.registerAgreement}, '%')
                </if>
                <if test="params.vehRegisterMode != null and params.vehRegisterMode != ''">
                    AND veh_register_mode LIKE concat('%', #{params.vehRegisterMode}, '%')
                </if>
                <if test="params.companyName != null and params.companyName != ''">
                    AND company_name LIKE concat('%', #{params.companyName}, '%')
                </if>
                <if test="params.platformName != null and params.platformName != ''">
                    AND platform_name LIKE concat('%', #{params.platformName}, '%')
                </if>
                <if test="params.platformAccountName != null and params.platformAccountName != ''">
                    AND platform_account_name LIKE concat('%', #{params.platformAccountName}, '%')
                </if>
                <if test="params.constructionUnit != null and params.constructionUnit != ''">
                    AND construction_unit LIKE concat('%', #{params.constructionUnit}, '%')
                </if>
                <if test="params.platformMode != null and params.platformMode != ''">
                    AND platform_mode LIKE concat('%', #{params.platformMode}, '%')
                </if>
                <if test="params.activationStatus != null and params.activationStatus != ''">
                    AND activation_status = #{params.activationStatus}
                </if>
                <if test="params.vehicleModel != null and params.vehicleModel != ''">
                    AND vehicle_model LIKE concat('%', #{params.vehicleModel}, '%')
                </if>
                <if test="params.maxLoads != null and params.maxLoads != ''">
                    AND max_loads = #{params.maxLoads}
                </if>
                <if test="params.tboxModel != null and params.tboxModel != ''">
                    AND tbox_model LIKE concat('%', #{params.tboxModel}, '%')
                </if>
                <if test="params.tboxManufacturerName != null and params.tboxManufacturerName != ''">
                    AND tbox_manufacturer_name LIKE concat('%', #{params.tboxManufacturerName}, '%')
                </if>
                <if test="params.chipPrefix != null and params.chipPrefix != ''">
                    AND chip_prefix LIKE concat('%', #{params.chipPrefix}, '%')
                </if>
                <if test="params.chipModel != null and params.chipModel != ''">
                    AND chip_model LIKE concat('%', #{params.chipModel}, '%')
                </if>
                <if test="params.chipManufacturerName != null and params.chipManufacturerName != ''">
                    AND chip_manufacturer_name LIKE concat('%', #{params.chipManufacturerName}, '%')
                </if>
                <if test="params.country != null and params.country != ''">
                    AND country LIKE concat('%', #{params.country}, '%')
                </if>
                <if test="params.maximumPower != null and params.maximumPower != ''">
                    AND maximum_power = #{params.maximumPower}
                </if>
                <if test="params.maximumEngineRotationlSpeed != null and params.maximumEngineRotationlSpeed != ''">
                    AND maximum_engine_rotationl_speed = #{params.maximumEngineRotationlSpeed}
                </if>
                <if test="params.ratedCapacity != null and params.ratedCapacity != ''">
                    AND rated_capacity = #{params.ratedCapacity}
                </if>
                <if test="params.ratedCapacityRotationlSpeed != null and params.ratedCapacityRotationlSpeed != ''">
                    AND rated_capacity_rotationl_speed = #{params.ratedCapacityRotationlSpeed}
                </if>
                <if test="params.maximumTorque != null and params.maximumTorque != ''">
                    AND maximum_torque = #{params.maximumTorque}
                </if>
                <if test="params.maximumTorqueRotationlSpeed != null and params.maximumTorqueRotationlSpeed != ''">
                    AND maximum_torque_rotationl_speed = #{params.maximumTorqueRotationlSpeed}
                </if>
                <if test="params.fuelFeedSystem != null and params.fuelFeedSystem != ''">
                    AND fuel_feed_system LIKE concat('%', #{params.fuelFeedSystem}, '%')
                </if>
                <if test="params.exhaustAftertreatment != null and params.exhaustAftertreatment != ''">
                    AND exhaust_aftertreatment LIKE concat('%', #{params.exhaustAftertreatment}, '%')
                </if>
                <if test="params.companyType != null and params.companyType != ''">
                    AND company_type LIKE concat('%', #{params.companyType}, '%')
                </if>
                <if test="params.chassisCompanyName != null and params.chassisCompanyName != ''">
                    AND chassis_company_name LIKE concat('%', #{params.chassisCompanyName}, '%')
                </if>
                <if test="params.enginePower != null and params.enginePower != ''">
                    AND engine_power = #{params.enginePower}
                </if>
                <if test="params.engineTorque != null and params.engineTorque != ''">
                    AND engine_torque = #{params.engineTorque}
                </if>
                <if test="params.engineProductionAddress != null and params.engineProductionAddress != ''">
                    AND engine_production_address LIKE concat('%', #{params.engineProductionAddress}, '%')
                </if>
                <if test="params.actualAgreement != null and params.actualAgreement != ''">
                    AND actual_agreement LIKE concat('%', #{params.actualAgreement}, '%')
                </if>
                <if test="params.specialVehType != null and params.specialVehType != ''">
                    AND special_veh_type LIKE concat('%', #{params.specialVehType}, '%')
                </if>
                <if test="params.specialVehTerm != null and params.specialVehTerm != ''">
                    AND special_veh_term LIKE concat('%', #{params.specialVehTerm}, '%')
                </if>

                <!-- 时间范围查询 -->
                <if test="params.registerTimeBegin != null and params.registerTimeBegin != ''">
                    AND register_time &gt;= #{params.registerTimeBegin}
                </if>
                <if test="params.registerTimeEnd != null and params.registerTimeEnd != ''">
                    AND register_time &lt;= #{params.registerTimeEnd}
                </if>
                <if test="params.syncTimeBegin != null and params.syncTimeBegin != ''">
                    AND sync_time &gt;= #{params.syncTimeBegin}
                </if>
                <if test="params.syncTimeEnd != null and params.syncTimeEnd != ''">
                    AND sync_time &lt;= #{params.syncTimeEnd}
                </if>
                <if test="params.activationTimeBegin != null and params.activationTimeBegin != ''">
                    AND activation_time &gt;= #{params.activationTimeBegin}
                </if>
                <if test="params.activationTimeEnd != null and params.activationTimeEnd != ''">
                    AND activation_time &lt;= #{params.activationTimeEnd}
                </if>
                <if test="params.firstOnlineTimeBegin != null and params.firstOnlineTimeBegin != ''">
                    AND first_online_time &gt;= #{params.firstOnlineTimeBegin}
                </if>
                <if test="params.firstOnlineTimeEnd != null and params.firstOnlineTimeEnd != ''">
                    AND first_online_time &lt;= #{params.firstOnlineTimeEnd}
                </if>
                <if test="params.connectTimeBegin != null and params.connectTimeBegin != ''">
                    AND connect_time &gt;= #{params.connectTimeBegin}
                </if>
                <if test="params.connectTimeEnd != null and params.connectTimeEnd != ''">
                    AND connect_time &lt;= #{params.connectTimeEnd}
                </if>
                <if test="params.publicTimeBegin != null and params.publicTimeBegin != ''">
                    AND public_time &gt;= #{params.publicTimeBegin}
                </if>
                <if test="params.publicTimeEnd != null and params.publicTimeEnd != ''">
                    AND public_time &lt;= #{params.publicTimeEnd}
                </if>
            </if>
        </where>
    </select>

    <select id="selectVehicleBasisByVin" parameterType="String" resultMap="VehicleBasisResult">
        <include refid="selectVehicleBasisVo"/>
        WHERE vin = #{vin}
    </select>
    
    <select id="selectFieldValues" resultType="String">
        SELECT DISTINCT ${fieldName}
        FROM dw_ods.sys_vehicle_basis_sync
        WHERE ${fieldName} IS NOT NULL AND ${fieldName} != ''
        ORDER BY ${fieldName}
    </select>

</mapper> 