<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.portal.obd_static_info.mapper.VehicleBasisMapper">

    <resultMap type="com.portal.obd_static_info.domain.VehicleBasis" id="VehicleBasisResult">
        <result property="vin" column="vin"/>
        <result property="enpInfoPublicNum" column="enp_info_public_num"/>
        <result property="epaVehicleType" column="epa_vehicle_type"/>
        <result property="vehCatalogue" column="veh_catalogue"/>
        <result property="vehCatalogueDetail" column="veh_catalogue_detail"/>
        <result property="engineEmissionLeveltype" column="engine_emission_leveltype"/>
        <result property="vehicleLicense" column="vehicle_license"/>
        <result property="vehicleDomicile" column="vehicle_domicile"/>
        <result property="registerAgreement" column="register_agreement"/>
        <result property="vehRegisterMode" column="veh_register_mode"/>
        <result property="companyName" column="company_name"/>
        <result property="platformName" column="platform_name"/>
        <result property="platformAccountName" column="platform_account_name"/>
        <result property="constructionUnit" column="construction_unit"/>
        <result property="platformMode" column="platform_mode"/>
        <result property="manufacturerBuildName" column="manufacturer_build_name"/>
        <result property="registerTime" column="register_time"/>
        <result property="syncStatus" column="sync_status"/>
        <result property="syncTime" column="sync_time"/>
        <result property="activationStatus" column="activation_status"/>
        <result property="activationTime" column="activation_time"/>
        <result property="firstOnlineTime" column="first_online_time"/>
        <result property="firstOnlineStatus" column="first_online_status"/>
        <result property="connectStatus" column="connect_status"/>
        <result property="connectTime" column="connect_time"/>
        <result property="vehicleModel" column="vehicle_model"/>
        <result property="vehicleType" column="vehicle_type"/>
        <result property="vehicleLoads" column="vehicle_loads"/>
        <result property="maxLoads" column="max_loads"/>
        <result property="engineModel" column="engine_model"/>
        <result property="engineManufacturerName" column="engine_manufacturer_name"/>
        <result property="tboxModel" column="tbox_model"/>
        <result property="tboxManufacturerName" column="tbox_manufacturer_name"/>
        <result property="chipPrefix" column="chip_prefix"/>
        <result property="chipModel" column="chip_model"/>
        <result property="chipManufacturerName" column="chip_manufacturer_name"/>
        <result property="manufactureDate" column="manufacture_date"/>
        <result property="country" column="country"/>
        <result property="maximumPower" column="maximum_power"/>
        <result property="maximumEngineRotationlSpeed" column="maximum_engine_rotationl_speed"/>
        <result property="ratedCapacity" column="rated_capacity"/>
        <result property="ratedCapacityRotationlSpeed" column="rated_capacity_rotationl_speed"/>
        <result property="maximumTorque" column="maximum_torque"/>
        <result property="maximumTorqueRotationlSpeed" column="maximum_torque_rotationl_speed"/>
        <result property="fuelFeedSystem" column="fuel_feed_system"/>
        <result property="exhaustAftertreatment" column="exhaust_aftertreatment"/>
        <result property="companyType" column="company_type"/>
        <result property="chassisCompanyName" column="chassis_company_name"/>
        <result property="enginePower" column="engine_power"/>
        <result property="engineTorque" column="engine_torque"/>
        <result property="engineProductionAddress" column="engine_production_address"/>
        <result property="publicTime" column="public_time"/>
        <result property="actualAgreement" column="actual_agreement"/>
        <result property="specialVehType" column="special_veh_type"/>
        <result property="specialVehTerm" column="special_veh_term"/>
    </resultMap>

    <sql id="selectVehicleBasisVo">
        SELECT vin, enp_info_public_num, epa_vehicle_type, veh_catalogue, veh_catalogue_detail, 
        engine_emission_leveltype, vehicle_license, vehicle_domicile, register_agreement, veh_register_mode,
        company_name, platform_name, platform_account_name, construction_unit, platform_mode, 
        manufacturer_build_name, register_time, sync_status, sync_time, activation_status, 
        activation_time, first_online_time, first_online_status, connect_status, connect_time,
        vehicle_model, vehicle_type, vehicle_loads, max_loads, engine_model, 
        engine_manufacturer_name, tbox_model, tbox_manufacturer_name, chip_prefix, chip_model, 
        chip_manufacturer_name, manufacture_date, country, maximum_power, maximum_engine_rotationl_speed, 
        rated_capacity, rated_capacity_rotationl_speed, maximum_torque, maximum_torque_rotationl_speed, 
        fuel_feed_system, exhaust_aftertreatment, company_type, chassis_company_name, engine_power, 
        engine_torque, engine_production_address, public_time, actual_agreement, special_veh_type, special_veh_term
        FROM dw_ods.sys_vehicle_basis_sync
    </sql>

    <select id="selectVehicleBasisList" parameterType="com.portal.obd_static_info.dto.VehicleBasisQueryParam" resultMap="VehicleBasisResult">
        <include refid="selectVehicleBasisVo"/>
        <where>
            <!-- 动态查询参数支持 - 支持数据库表中的所有字段 -->
            <if test="params != null">
                <!-- 字符串字段模糊匹配 -->
                <if test="params.vin != null and params.vin != ''">
                    AND vin LIKE concat('%', #{params.vin}, '%')
                </if>
                <if test="params.epa_vehicle_type != null and params.epa_vehicle_type != ''">
                    AND epa_vehicle_type LIKE concat('%', #{params.epa_vehicle_type}, '%')
                </if>
                <if test="params.epaVehicleType != null and params.epaVehicleType != ''">
                    AND epa_vehicle_type LIKE concat('%', #{params.epaVehicleType}, '%')
                </if>
                <if test="params.vehicle_license != null and params.vehicle_license != ''">
                    AND vehicle_license LIKE concat('%', #{params.vehicle_license}, '%')
                </if>
                <if test="params.vehicleLicense != null and params.vehicleLicense != ''">
                    AND vehicle_license LIKE concat('%', #{params.vehicleLicense}, '%')
                </if>
                <if test="params.vehicle_type != null and params.vehicle_type != ''">
                    AND vehicle_type LIKE concat('%', #{params.vehicle_type}, '%')
                </if>
                <if test="params.vehicleType != null and params.vehicleType != ''">
                    AND vehicle_type LIKE concat('%', #{params.vehicleType}, '%')
                </if>
                <if test="params.engine_model != null and params.engine_model != ''">
                    AND engine_model LIKE concat('%', #{params.engine_model}, '%')
                </if>
                <if test="params.engineModel != null and params.engineModel != ''">
                    AND engine_model LIKE concat('%', #{params.engineModel}, '%')
                </if>
                <if test="params.engine_manufacturer_name != null and params.engine_manufacturer_name != ''">
                    AND engine_manufacturer_name LIKE concat('%', #{params.engine_manufacturer_name}, '%')
                </if>
                <if test="params.engineManufacturerName != null and params.engineManufacturerName != ''">
                    AND engine_manufacturer_name LIKE concat('%', #{params.engineManufacturerName}, '%')
                </if>
                <if test="params.manufacturer_build_name != null and params.manufacturer_build_name != ''">
                    AND manufacturer_build_name LIKE concat('%', #{params.manufacturer_build_name}, '%')
                </if>
                <if test="params.manufacturerBuildName != null and params.manufacturerBuildName != ''">
                    AND manufacturer_build_name LIKE concat('%', #{params.manufacturerBuildName}, '%')
                </if>
                <if test="params.vehicle_loads != null and params.vehicle_loads != ''">
                    AND vehicle_loads LIKE concat('%', #{params.vehicle_loads}, '%')
                </if>
                <if test="params.vehicleLoads != null and params.vehicleLoads != ''">
                    AND vehicle_loads LIKE concat('%', #{params.vehicleLoads}, '%')
                </if>
                <if test="params.engine_emission_leveltype != null and params.engine_emission_leveltype != ''">
                    AND engine_emission_leveltype LIKE concat('%', #{params.engine_emission_leveltype}, '%')
                </if>
                <if test="params.engineEmissionLeveltype != null and params.engineEmissionLeveltype != ''">
                    AND engine_emission_leveltype LIKE concat('%', #{params.engineEmissionLeveltype}, '%')
                </if>
                <if test="params.connect_status != null and params.connect_status != ''">
                    AND connect_status = #{params.connect_status}
                </if>
                <if test="params.connectStatus != null and params.connectStatus != ''">
                    AND connect_status = #{params.connectStatus}
                </if>
                <if test="params.vehicle_domicile != null and params.vehicle_domicile != ''">
                    AND vehicle_domicile LIKE concat('%', #{params.vehicle_domicile}, '%')
                </if>
                <if test="params.vehicleDomicile != null and params.vehicleDomicile != ''">
                    AND vehicle_domicile LIKE concat('%', #{params.vehicleDomicile}, '%')
                </if>
                <if test="params.first_online_status != null and params.first_online_status != ''">
                    AND first_online_status = #{params.first_online_status}
                </if>
                <if test="params.firstOnlineStatus != null and params.firstOnlineStatus != ''">
                    AND first_online_status = #{params.firstOnlineStatus}
                </if>
                <if test="params.sync_status != null and params.sync_status != ''">
                    AND sync_status = #{params.sync_status}
                </if>
                <if test="params.syncStatus != null and params.syncStatus != ''">
                    AND sync_status = #{params.syncStatus}
                </if>
                <if test="params.begin_time != null and params.begin_time != ''">
                    AND register_time &gt;= #{params.begin_time}
                </if>
                <if test="params.beginTime != null and params.beginTime != ''">
                    AND register_time &gt;= #{params.beginTime}
                </if>
                <if test="params.end_time != null and params.end_time != ''">
                    AND register_time &lt;= #{params.end_time}
                </if>
                <if test="params.endTime != null and params.endTime != ''">
                    AND register_time &lt;= #{params.endTime}
                </if>
                <if test="params.enp_info_public_num != null and params.enp_info_public_num != ''">
                    AND enp_info_public_num LIKE concat('%', #{params.enp_info_public_num}, '%')
                </if>
                <if test="params.veh_catalogue != null and params.veh_catalogue != ''">
                    AND veh_catalogue LIKE concat('%', #{params.veh_catalogue}, '%')
                </if>
                <if test="params.veh_catalogue_detail != null and params.veh_catalogue_detail != ''">
                    AND veh_catalogue_detail LIKE concat('%', #{params.veh_catalogue_detail}, '%')
                </if>
                <if test="params.register_agreement != null and params.register_agreement != ''">
                    AND register_agreement LIKE concat('%', #{params.register_agreement}, '%')
                </if>
                <if test="params.veh_register_mode != null and params.veh_register_mode != ''">
                    AND veh_register_mode LIKE concat('%', #{params.veh_register_mode}, '%')
                </if>
                <if test="params.company_name != null and params.company_name != ''">
                    AND company_name LIKE concat('%', #{params.company_name}, '%')
                </if>
                <if test="params.platform_name != null and params.platform_name != ''">
                    AND platform_name LIKE concat('%', #{params.platform_name}, '%')
                </if>
                <if test="params.platform_account_name != null and params.platform_account_name != ''">
                    AND platform_account_name LIKE concat('%', #{params.platform_account_name}, '%')
                </if>
                <if test="params.construction_unit != null and params.construction_unit != ''">
                    AND construction_unit LIKE concat('%', #{params.construction_unit}, '%')
                </if>
                <if test="params.platform_mode != null and params.platform_mode != ''">
                    AND platform_mode LIKE concat('%', #{params.platform_mode}, '%')
                </if>
                <if test="params.activation_status != null and params.activation_status != ''">
                    AND activation_status = #{params.activation_status}
                </if>
                <if test="params.vehicle_model != null and params.vehicle_model != ''">
                    AND vehicle_model LIKE concat('%', #{params.vehicle_model}, '%')
                </if>
                <if test="params.max_loads != null and params.max_loads != ''">
                    AND max_loads = #{params.max_loads}
                </if>
                <if test="params.tbox_model != null and params.tbox_model != ''">
                    AND tbox_model LIKE concat('%', #{params.tbox_model}, '%')
                </if>
                <if test="params.tbox_manufacturer_name != null and params.tbox_manufacturer_name != ''">
                    AND tbox_manufacturer_name LIKE concat('%', #{params.tbox_manufacturer_name}, '%')
                </if>
                <if test="params.chip_prefix != null and params.chip_prefix != ''">
                    AND chip_prefix LIKE concat('%', #{params.chip_prefix}, '%')
                </if>
                <if test="params.chip_model != null and params.chip_model != ''">
                    AND chip_model LIKE concat('%', #{params.chip_model}, '%')
                </if>
                <if test="params.chip_manufacturer_name != null and params.chip_manufacturer_name != ''">
                    AND chip_manufacturer_name LIKE concat('%', #{params.chip_manufacturer_name}, '%')
                </if>
                <if test="params.country != null and params.country != ''">
                    AND country LIKE concat('%', #{params.country}, '%')
                </if>
                <if test="params.maximum_power != null and params.maximum_power != ''">
                    AND maximum_power = #{params.maximum_power}
                </if>
                <if test="params.maximum_engine_rotationl_speed != null and params.maximum_engine_rotationl_speed != ''">
                    AND maximum_engine_rotationl_speed = #{params.maximum_engine_rotationl_speed}
                </if>
                <if test="params.rated_capacity != null and params.rated_capacity != ''">
                    AND rated_capacity = #{params.rated_capacity}
                </if>
                <if test="params.rated_capacity_rotationl_speed != null and params.rated_capacity_rotationl_speed != ''">
                    AND rated_capacity_rotationl_speed = #{params.rated_capacity_rotationl_speed}
                </if>
                <if test="params.maximum_torque != null and params.maximum_torque != ''">
                    AND maximum_torque = #{params.maximum_torque}
                </if>
                <if test="params.maximum_torque_rotationl_speed != null and params.maximum_torque_rotationl_speed != ''">
                    AND maximum_torque_rotationl_speed = #{params.maximum_torque_rotationl_speed}
                </if>
                <if test="params.fuel_feed_system != null and params.fuel_feed_system != ''">
                    AND fuel_feed_system LIKE concat('%', #{params.fuel_feed_system}, '%')
                </if>
                <if test="params.exhaust_aftertreatment != null and params.exhaust_aftertreatment != ''">
                    AND exhaust_aftertreatment LIKE concat('%', #{params.exhaust_aftertreatment}, '%')
                </if>
                <if test="params.company_type != null and params.company_type != ''">
                    AND company_type LIKE concat('%', #{params.company_type}, '%')
                </if>
                <if test="params.chassis_company_name != null and params.chassis_company_name != ''">
                    AND chassis_company_name LIKE concat('%', #{params.chassis_company_name}, '%')
                </if>
                <if test="params.engine_power != null and params.engine_power != ''">
                    AND engine_power = #{params.engine_power}
                </if>
                <if test="params.engine_torque != null and params.engine_torque != ''">
                    AND engine_torque = #{params.engine_torque}
                </if>
                <if test="params.engine_production_address != null and params.engine_production_address != ''">
                    AND engine_production_address LIKE concat('%', #{params.engine_production_address}, '%')
                </if>
                <if test="params.actual_agreement != null and params.actual_agreement != ''">
                    AND actual_agreement LIKE concat('%', #{params.actual_agreement}, '%')
                </if>
                <if test="params.special_veh_type != null and params.special_veh_type != ''">
                    AND special_veh_type LIKE concat('%', #{params.special_veh_type}, '%')
                </if>
                <if test="params.special_veh_term != null and params.special_veh_term != ''">
                    AND special_veh_term LIKE concat('%', #{params.special_veh_term}, '%')
                </if>
                
                <!-- 时间范围查询 -->
                <if test="params.register_time_begin != null and params.register_time_begin != ''">
                    AND register_time &gt;= #{params.register_time_begin}
                </if>
                <if test="params.register_time_end != null and params.register_time_end != ''">
                    AND register_time &lt;= #{params.register_time_end}
                </if>
                <if test="params.sync_time_begin != null and params.sync_time_begin != ''">
                    AND sync_time &gt;= #{params.sync_time_begin}
                </if>
                <if test="params.sync_time_end != null and params.sync_time_end != ''">
                    AND sync_time &lt;= #{params.sync_time_end}
                </if>
                <if test="params.activation_time_begin != null and params.activation_time_begin != ''">
                    AND activation_time &gt;= #{params.activation_time_begin}
                </if>
                <if test="params.activation_time_end != null and params.activation_time_end != ''">
                    AND activation_time &lt;= #{params.activation_time_end}
                </if>
                <if test="params.first_online_time_begin != null and params.first_online_time_begin != ''">
                    AND first_online_time &gt;= #{params.first_online_time_begin}
                </if>
                <if test="params.first_online_time_end != null and params.first_online_time_end != ''">
                    AND first_online_time &lt;= #{params.first_online_time_end}
                </if>
                <if test="params.connect_time_begin != null and params.connect_time_begin != ''">
                    AND connect_time &gt;= #{params.connect_time_begin}
                </if>
                <if test="params.connect_time_end != null and params.connect_time_end != ''">
                    AND connect_time &lt;= #{params.connect_time_end}
                </if>
                <if test="params.public_time_begin != null and params.public_time_begin != ''">
                    AND public_time &gt;= #{params.public_time_begin}
                </if>
                <if test="params.public_time_end != null and params.public_time_end != ''">
                    AND public_time &lt;= #{params.public_time_end}
                </if>
            </if>
        </where>
    </select>

    <select id="selectVehicleBasisByVin" parameterType="String" resultMap="VehicleBasisResult">
        <include refid="selectVehicleBasisVo"/>
        WHERE vin = #{vin}
    </select>
    
    <select id="selectFieldValues" resultType="String">
        SELECT DISTINCT ${fieldName}
        FROM dw_ods.sys_vehicle_basis_sync
        WHERE ${fieldName} IS NOT NULL AND ${fieldName} != ''
        ORDER BY ${fieldName}
    </select>

</mapper> 