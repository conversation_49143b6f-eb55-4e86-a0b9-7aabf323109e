<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.portal.obd_dataquality_score.mapper.VehicleDataQualityMapper">

    <!-- 车辆在线离线数 -->
    <select id="getVehicleDataSummary" resultType="com.portal.obd_dataquality_score.domain.VehicleDataSummary">
        with t1 as 
        (select pt_month,
             manufacturer_build_name as manufacturer_build_name,
             sum(online_cnt) as onlineCnt,  
             sum(connected_cnt) as connectedCnt
        from dw_dwm.dwm_online_connected_vehicles_cnt_monthtable 
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="epaVehicleType != null and epaVehicleType != ''">
                and epa_vehicle_type like CONCAT('%', #{epaVehicleType}, '%')
            </if>
            <if test="actualAgreement != null and actualAgreement != ''">
                and actual_agreement = #{actualAgreement}
            </if>
            <if test="engineManufacturerName != null and engineManufacturerName != ''">
                and engine_manufacturer_name = #{engineManufacturerName}
            </if>
            <if test="engineModel != null and engineModel != ''">
                and engine_model = #{engineModel}
            </if>
        group by pt_month,
                 manufacturer_build_name),
         t2 as 
         (select pt_month,
             manufacturer_build_name as manufacturer_build_name,
             sum(offline_cnt) as offlineCnt
        from 
        dw_dwm.dwm_obd_data_longterm_offline_rate_ctype_agreement 
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="epaVehicleType != null and epaVehicleType != ''">
                and ctype like CONCAT('%', #{epaVehicleType}, '%')
            </if>
            <if test="actualAgreement != null and actualAgreement != ''">
                and agreement = #{actualAgreement}
            </if>
            <if test="engineManufacturerName != null and engineManufacturerName != ''">
                and engine_manufacturer_name = #{engineManufacturerName}
            </if>
            <if test="engineModel != null and engineModel != ''">
                and engine_model = #{engineModel}
            </if>
        group by pt_month,
                 manufacturer_build_name)
        select 
          t1.pt_month as ptMonth,
          t1.manufacturer_build_name as manufacturerBuildName,
          t1.onlineCnt,
          t1.connectedCnt,
          t2.offlineCnt
        from t1 inner join t2 
          on t1.pt_month = t2.pt_month 
          and t1.manufacturer_build_name = t2.manufacturer_build_name
    </select>

    <!-- 数据越界率 -->
    <select id="getEngineIndicatorData" resultType="com.portal.obd_dataquality_score.domain.DataOutboundaryRate">
        -- 按条件查询
        SELECT '发动机重点指标' as type, 
             manufacturer_build_name as manufacturerBuildName,
             avg(cast(avg_cross_border_rate as double)) as avgCrossBorderRate,
             avg(cast(score as double)) as score     
        FROM 
        dw_dwm.dwm_motor_and_twc_key_columns_crossborder_rate_score_monthtable
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="epaVehicleType != null and epaVehicleType != ''">
                and epa_vehicle_type like CONCAT('%', #{epaVehicleType}, '%')
            </if>
            <if test="actualAgreement != null and actualAgreement != ''">
                and actual_agreement = #{actualAgreement}
            </if>
            <if test="engineManufacturerName != null and engineManufacturerName != ''">
                and engine_manufacturer_name = #{engineManufacturerName}
            </if>
            <if test="engineModel != null and engineModel != ''">
                and engine_model = #{engineModel}
            </if>
        group by 
          manufacturer_build_name
        -- 02 通用越界打分
        union all
        select '发动机通用指标' as type,
             manufacturer_build_name as manufacturerBuildName,
             avg(cast(avg_cross_border_rate as double)) as avgCrossBorderRate,
             avg(cast(score as double)) as score  
        from dw_dwm.dwm_motor_twc_general_columns_crossborder_rate_score_monthtable
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="epaVehicleType != null and epaVehicleType != ''">
                and epa_vehicle_type like CONCAT('%', #{epaVehicleType}, '%')
            </if>
            <if test="actualAgreement != null and actualAgreement != ''">
                and actual_agreement = #{actualAgreement}
            </if>
            <if test="engineManufacturerName != null and engineManufacturerName != ''">
                and engine_manufacturer_name = #{engineManufacturerName}
            </if>
            <if test="engineModel != null and engineModel != ''">
                and engine_model = #{engineModel}
            </if>
        group by 
          manufacturer_build_name
        -- 03 OBD 指标越界
        union all
        select 'OBD指标' as type,
             manufacturer_build_name as manufacturerBuildName,
             avg(cast(`90th_invalidrate` as double)) as avgCrossBorderRate,
             avg(cast(invalidrate_score as double)) as score
        from dw_dwm.dwm_obd_data_lossrate_outboundrate_monthly_ctype_agreement
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="epaVehicleType != null and epaVehicleType != ''">
                and ctype like CONCAT('%', #{epaVehicleType}, '%')
            </if>
            <if test="actualAgreement != null and actualAgreement != ''">
                and agreement = #{actualAgreement}
            </if>
            <if test="engineManufacturerName != null and engineManufacturerName != ''">
                and engine_manufacturer_name = #{engineManufacturerName}
            </if>
            <if test="engineModel != null and engineModel != ''">
                and engine_model = #{engineModel}
            </if>
        group by 
          manufacturer_build_name
          
        -- 不带筛选条件的总体平均值
        union all
        select '发动机重点指标(总体平均)' as type,
             '总体平均' as manufacturerBuildName,
             avg(cast(avg_cross_border_rate as double)) as avgCrossBorderRate,
             avg(cast(score as double)) as score     
        FROM 
        dw_dwm.dwm_motor_and_twc_key_columns_crossborder_rate_score_monthtable
        where pt_month = #{ptMonth}
        
        union all
        select '发动机通用指标(总体平均)' as type,
             '总体平均' as manufacturerBuildName,
             avg(cast(avg_cross_border_rate as double)) as avgCrossBorderRate,
             avg(cast(score as double)) as score  
        from dw_dwm.dwm_motor_twc_general_columns_crossborder_rate_score_monthtable
        where pt_month = #{ptMonth}
        
        union all
        select 'OBD指标(总体平均)' as type,
             '总体平均' as manufacturerBuildName,
             avg(cast(`90th_invalidrate` as double)) as avgCrossBorderRate,
             avg(cast(invalidrate_score as double)) as score
        from dw_dwm.dwm_obd_data_lossrate_outboundrate_monthly_ctype_agreement
        where pt_month = #{ptMonth}
    </select>
    
    <!-- 缺失率 -->
    <select id="getEngineLossRateData" resultType="com.portal.obd_dataquality_score.domain.DataLossrate">
        -- 按条件查询
        SELECT '发动机重点指标' as type, 
             manufacturer_build_name as manufacturerBuildName,
             avg(cast(avg_lossrate as double)) as avgLossRate,
             avg(cast(score as double)) as score  
        FROM dw_dwm.dwm_motor_and_twc_key_columns_lossrate_score_monthtable
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="epaVehicleType != null and epaVehicleType != ''">
                and epa_vehicle_type like CONCAT('%', #{epaVehicleType}, '%')
            </if>
            <if test="actualAgreement != null and actualAgreement != ''">
                and actual_agreement = #{actualAgreement}
            </if>
            <if test="engineManufacturerName != null and engineManufacturerName != ''">
                and engine_manufacturer_name = #{engineManufacturerName}
            </if>
            <if test="engineModel != null and engineModel != ''">
                and engine_model = #{engineModel}
            </if>
        group by 
          manufacturer_build_name 
        -- 02 通用打分
        union all
        select '发动机通用指标' as type,
             manufacturer_build_name as manufacturerBuildName,
             avg(cast(avg_lossrate as double)) as avgLossRate,
             avg(cast(score as double)) as score  
        from dw_dwm.dwm_motor_twc_supply_general_columns_lossrate_score_monthtable
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="epaVehicleType != null and epaVehicleType != ''">
                and epa_vehicle_type like CONCAT('%', #{epaVehicleType}, '%')
            </if>
            <if test="actualAgreement != null and actualAgreement != ''">
                and actual_agreement = #{actualAgreement}
            </if>
            <if test="engineManufacturerName != null and engineManufacturerName != ''">
                and engine_manufacturer_name = #{engineManufacturerName}
            </if>
            <if test="engineModel != null and engineModel != ''">
                and engine_model = #{engineModel}
            </if>
        group by 
          manufacturer_build_name
        -- 03 OBD 
        union all
        select 'OBD指标' as type,
             manufacturer_build_name as manufacturerBuildName,
             avg(cast(`90th_lossrate` as double)) as avgLossRate,
             avg(cast(lossrate_score as double)) as score
        from dw_dwm.dwm_obd_data_lossrate_outboundrate_monthly_ctype_agreement
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="epaVehicleType != null and epaVehicleType != ''">
                and ctype like CONCAT('%', #{epaVehicleType}, '%')
            </if>
            <if test="actualAgreement != null and actualAgreement != ''">
                and agreement = #{actualAgreement}
            </if>
            <if test="engineManufacturerName != null and engineManufacturerName != ''">
                and engine_manufacturer_name = #{engineManufacturerName}
            </if>
            <if test="engineModel != null and engineModel != ''">
                and engine_model = #{engineModel}
            </if>
        group by 
          manufacturer_build_name
          
        -- 不带筛选条件的总体平均值
        union all
        SELECT '发动机重点指标(总体平均)' as type, 
             '总体平均' as manufacturerBuildName,
             avg(cast(avg_lossrate as double)) as avgLossRate,
             avg(cast(score as double)) as score  
        FROM dw_dwm.dwm_motor_and_twc_key_columns_lossrate_score_monthtable
        where pt_month = #{ptMonth}
        
        union all
        select '发动机通用指标(总体平均)' as type,
             '总体平均' as manufacturerBuildName,
             avg(cast(avg_lossrate as double)) as avgLossRate,
             avg(cast(score as double)) as score  
        from dw_dwm.dwm_motor_twc_supply_general_columns_lossrate_score_monthtable
        where pt_month = #{ptMonth}
        
        union all
        select 'OBD指标(总体平均)' as type,
             '总体平均' as manufacturerBuildName,
             avg(cast(`90th_lossrate` as double)) as avgLossRate,
             avg(cast(lossrate_score as double)) as score
        from dw_dwm.dwm_obd_data_lossrate_outboundrate_monthly_ctype_agreement
        where pt_month = #{ptMonth}
    </select>
    
    <!-- 传输数据缺失率 -->
    <select id="getTransDataLossrate" resultType="com.portal.obd_dataquality_score.domain.TransDataLossrate">
        -- 按条件查询
        SELECT '发动机数据' as type,
               manufacturer_build_name as manufacturerBuildName,
               avg(cast(lossrate_score as double)) as obdLossrateScore,
               avg(cast(`90th_lossrate` as double)) as obdLossrateRate
        FROM dw_dwm.dwm_obd_data_lossrate_score_monthly_ctype_agreement
        WHERE pt_month = #{ptMonth}
              and manufacturer_build_name = #{manufacturerBuildName}
              <if test="epaVehicleType != null and epaVehicleType != ''">
                  and ctype like CONCAT('%', #{epaVehicleType}, '%')
              </if>
              <if test="actualAgreement != null and actualAgreement != ''">
                  and agreement = #{actualAgreement}
              </if>
              <if test="engineManufacturerName != null and engineManufacturerName != ''">
                  and engine_manufacturer_name = #{engineManufacturerName}
              </if>
              <if test="engineModel != null and engineModel != ''">
                  and engine_model = #{engineModel}
              </if>
        GROUP BY 
            manufacturer_build_name 
        UNION ALL
        SELECT 'OBD数据' as type,
               manufacturer_build_name as manufacturerBuildName,
               avg(cast(lossrate_score as double)) as obdLossrateScore,
               avg(cast(`90th_lossrate` as double)) as obdLossrateRate
        FROM dw_dwm.dwm_motor_data_lossrate_score_monthly_ctype_agreement
        WHERE pt_month = #{ptMonth}
              and manufacturer_build_name = #{manufacturerBuildName}
              <if test="epaVehicleType != null and epaVehicleType != ''">
                  and ctype like CONCAT('%', #{epaVehicleType}, '%')
              </if>
              <if test="actualAgreement != null and actualAgreement != ''">
                  and agreement = #{actualAgreement}
              </if>
              <if test="engineManufacturerName != null and engineManufacturerName != ''">
                  and engine_manufacturer_name = #{engineManufacturerName}
              </if>
              <if test="engineModel != null and engineModel != ''">
                  and engine_model = #{engineModel}
              </if>
        GROUP BY 
            manufacturer_build_name
            
        -- 不带筛选条件的总体平均值
        UNION ALL
        SELECT '发动机数据(总体平均)' as type,
               '总体平均' as manufacturerBuildName,
               avg(cast(lossrate_score as double)) as obdLossrateScore,
               avg(cast(`90th_lossrate` as double)) as obdLossrateRate
        FROM dw_dwm.dwm_obd_data_lossrate_score_monthly_ctype_agreement
        WHERE pt_month = #{ptMonth}
        
        UNION ALL
        SELECT 'OBD数据(总体平均)' as type,
               '总体平均' as manufacturerBuildName,
               avg(cast(lossrate_score as double)) as obdLossrateScore,
               avg(cast(`90th_lossrate` as double)) as obdLossrateRate
        FROM dw_dwm.dwm_motor_data_lossrate_score_monthly_ctype_agreement
        WHERE pt_month = #{ptMonth}
    </select>
    
    <!-- 数据异常率 -->
    <select id="getDataExecption" resultType="com.portal.obd_dataquality_score.domain.DataExecption">
        -- 按条件查询
        SELECT manufacturer_build_name as manufacturerBuildName,
               avg(cast(obd_exception_rate as double)) as obdExceptionRate,
               avg(cast(obd_exception_score as double)) as obdExceptionScore,
               avg(cast(motor_exception_rate_1 as double)) as motorExceptionRate1,
               avg(cast(motor_exception_score_1 as double)) as motorExceptionScore1,
               avg(cast(motor_exception_rate_2 as double)) as motorExceptionRate2,
               avg(cast(motor_exception_score_2 as double)) as motorExceptionScore2,
               avg(cast(log_exception_rate_1 as double)) as logExceptionRate1,
               avg(cast(log_exception_score_1 as double)) as logExceptionScore1,
               avg(cast(log_exception_rate_2 as double)) as logExceptionRate2,
               avg(cast(log_exception_score_2 as double)) as logExceptionScore2
        FROM dw_dwm.dwm_obd_data_execption_rate_monthly_ctype_agreement
        WHERE pt_month = #{ptMonth}
              and manufacturer_build_name = #{manufacturerBuildName}
              <if test="epaVehicleType != null and epaVehicleType != ''">
                  and ctype like CONCAT('%', #{epaVehicleType}, '%')
              </if>
              <if test="actualAgreement != null and actualAgreement != ''">
                  and agreement = #{actualAgreement}
              </if>
              <if test="engineManufacturerName != null and engineManufacturerName != ''">
                  and engine_manufacturer_name = #{engineManufacturerName}
              </if>
              <if test="engineModel != null and engineModel != ''">
                  and engine_model = #{engineModel}
              </if>
        GROUP BY 
            manufacturer_build_name 
            
        UNION ALL
        
        -- 不带筛选条件的总体平均值
        SELECT '总体平均' as manufacturerBuildName,
               avg(cast(obd_exception_rate as double)) as obdExceptionRate,
               avg(cast(obd_exception_score as double)) as obdExceptionScore,
               avg(cast(motor_exception_rate_1 as double)) as motorExceptionRate1,
               avg(cast(motor_exception_score_1 as double)) as motorExceptionScore1,
               avg(cast(motor_exception_rate_2 as double)) as motorExceptionRate2,
               avg(cast(motor_exception_score_2 as double)) as motorExceptionScore2,
               avg(cast(log_exception_rate_1 as double)) as logExceptionRate1,
               avg(cast(log_exception_score_1 as double)) as logExceptionScore1,
               avg(cast(log_exception_rate_2 as double)) as logExceptionRate2,
               avg(cast(log_exception_score_2 as double)) as logExceptionScore2
        FROM dw_dwm.dwm_obd_data_execption_rate_monthly_ctype_agreement
        WHERE pt_month = #{ptMonth}
    </select>

    <!-- 柴油车型VIN码的数据越界率 -->
    <select id="getDieselVinOutboundaryList" resultType="com.portal.obd_dataquality_score.domain.DieselVinOutboundaryList">
        SELECT * FROM (
            SELECT 
                vin,
                manufacturerBuildName,
                engineModel,
                engineManufacturerName,
                epaVehicleType,
                actualAgreement,
                scrIRate,
                scrORate,
                noxIRate,
                noxORate,
                restUreaRate,
                restFuelRate,
                dpfRate,
                lonRate,
                latRate,
                diagnosticProtocolRate,
                milStateRate,
                defectCodeCntRate,
                iuprRate,
                scrIRate as scr_i_rate,
                scrORate as scr_o_rate,
                noxIRate as nox_i_rate,
                noxORate as nox_o_rate,
                restUreaRate as rest_urea_rate,
                restFuelRate as rest_fuel_rate,
                dpfRate as dpf_rate,
                lonRate as lon_rate,
                latRate as lat_rate,
                diagnosticProtocolRate as diagnostic_protocol_rate,
                milStateRate as mil_state_rate,
                defectCodeCntRate as defect_code_cnt_rate,
                iuprRate as iupr_rate
            FROM (
                SELECT vin,
                       manufacturer_build_name as manufacturerBuildName,
                       engine_model as engineModel,
                       engine_manufacturer_name as engineManufacturerName,
                       epa_vehicle_type as epaVehicleType,
                       actual_agreement as actualAgreement,
                       validflag_temp_i_cnt/valid_cnt as scrIRate,
                       validflag_temp_o_cnt/valid_cnt as scrORate,
                       validflag_nox_i_cnt/valid_cnt as noxIRate,
                       validflag_nox_o_cnt/valid_cnt as noxORate,
                       validflag_resturea_cnt/valid_cnt as restUreaRate,
                       validflag_restfuel_cnt/valid_cnt as restFuelRate,
                       validflag_dpf_pres_diff_cnt/valid_cnt as dpfRate,
                       validflag_lon_cnt/valid_cnt as lonRate,
                       validflag_lat_cnt/valid_cnt as latRate,
                       re_diagnostic_protocol_lossrate/obd_cnt as diagnosticProtocolRate,
                       re_mil_state_lossrate/obd_cnt as milStateRate,
                       re_defect_code_cnt_lossrate/obd_cnt as defectCodeCntRate,
                       re_iupr_lossrate/obd_cnt as iuprRate
                FROM dw_dwm.dwm_motor_twc_supply_columns_crossborder_cnt_byvin_monthtable
                WHERE pt_month = #{ptMonth}
                      and manufacturer_build_name = #{manufacturerBuildName}
                      and epa_vehicle_type = #{epaVehicleType}
                      <if test="vin != null and vin != ''">
                          and vin like CONCAT('%', #{vin}, '%')
                      </if>
                      <if test="actualAgreement != null and actualAgreement != ''">
                          and actual_agreement = #{actualAgreement}
                      </if>
                      <if test="engineManufacturerName != null and engineManufacturerName != ''">
                          and engine_manufacturer_name = #{engineManufacturerName}
                      </if>
                      <if test="engineModel != null and engineModel != ''">
                          and engine_model = #{engineModel}
                      </if>
            ) t
        ) result
    </select>
    
    <!-- 燃气车型VIN码的数据越界率 -->
    <select id="getGasVinOutboundaryList" resultType="com.portal.obd_dataquality_score.domain.GasVinOutboundaryList">
        SELECT * FROM (
            SELECT 
                vin,
                manufacturerBuildName,
                engineModel,
                engineManufacturerName,
                epaVehicleType,
                actualAgreement,
                threeCatalystO3InRate,
                threeCatalystO3OutRate,
                lonRate,
                latRate,
                diagnosticProtocolRate,
                milStateRate,
                defectCodeCntRate,
                iuprRate,
                threeCatalystO3InRate as three_catalyst_o3_in_rate,
                threeCatalystO3OutRate as three_catalyst_o3_out_rate,
                lonRate as lon_rate,
                latRate as lat_rate,
                diagnosticProtocolRate as diagnostic_protocol_rate,
                milStateRate as mil_state_rate,
                defectCodeCntRate as defect_code_cnt_rate,
                iuprRate as iupr_rate
            FROM (
                SELECT vin,
                       manufacturer_build_name as manufacturerBuildName,
                       engine_model as engineModel,
                       engine_manufacturer_name as engineManufacturerName,
                       epa_vehicle_type as epaVehicleType,
                       actual_agreement as actualAgreement,
                       validflag_three_catalyst_o3_in_cnt/valid_cnt as threeCatalystO3InRate,
                       validflag_three_catalyst_o3_out_cnt/valid_cnt as threeCatalystO3OutRate,
                       validflag_lon_cnt/valid_cnt as lonRate,
                       validflag_lat_cnt/valid_cnt as latRate,
                       re_diagnostic_protocol_lossrate/obd_cnt as diagnosticProtocolRate,
                       re_mil_state_lossrate/obd_cnt as milStateRate,
                       re_defect_code_cnt_lossrate/obd_cnt as defectCodeCntRate,
                       re_iupr_lossrate/obd_cnt as iuprRate
                FROM dw_dwm.dwm_motor_twc_supply_columns_crossborder_cnt_byvin_monthtable
                WHERE pt_month = #{ptMonth}
                      and manufacturer_build_name = #{manufacturerBuildName}
                      and epa_vehicle_type = #{epaVehicleType}
                      <if test="vin != null and vin != ''">
                          and vin like CONCAT('%', #{vin}, '%')
                      </if>
                      <if test="actualAgreement != null and actualAgreement != ''">
                          and actual_agreement = #{actualAgreement}
                      </if>
                      <if test="engineManufacturerName != null and engineManufacturerName != ''">
                          and engine_manufacturer_name = #{engineManufacturerName}
                      </if>
                      <if test="engineModel != null and engineModel != ''">
                          and engine_model = #{engineModel}
                      </if>
                ) t
        ) result
    </select>

    <!-- 柴油车型VIN码的数据缺失率 -->
    <select id="getDieselVinLossrateList" resultType="com.portal.obd_dataquality_score.domain.DieselVinLossrateList">
        SELECT * FROM (
            SELECT 
                vin,
                manufacturerBuildName,
                engineModel,
                engineManufacturerName,
                epaVehicleType,
                actualAgreement,
                scrIRate,
                scrORate,
                noxIRate,
                noxORate,
                restUreaRate,
                restFuelRate,
                dpfRate,
                lonRate,
                latRate,
                diagnosticProtocolRate,
                milStateRate,
                defectCodeCntRate,
                iuprRate,
                scrIRate as scr_i_rate,
                scrORate as scr_o_rate,
                noxIRate as nox_i_rate,
                noxORate as nox_o_rate,
                restUreaRate as rest_urea_rate,
                restFuelRate as rest_fuel_rate,
                dpfRate as dpf_rate,
                lonRate as lon_rate,
                latRate as lat_rate,
                diagnosticProtocolRate as diagnostic_protocol_rate,
                milStateRate as mil_state_rate,
                defectCodeCntRate as defect_code_cnt_rate,
                iuprRate as iupr_rate
            FROM (
                SELECT vin,
                       manufacturer_build_name as manufacturerBuildName,
                       engine_model as engineModel,
                       engine_manufacturer_name as engineManufacturerName,
                       epa_vehicle_type as epaVehicleType,
                       actual_agreement as actualAgreement,
                       valid_temp_i_cnt/valid_cnt as scrIRate,
                       valid_temp_o_cnt/valid_cnt as scrORate,
                       invalid_nox_i_cnt/valid_cnt as noxIRate,
                       invalid_nox_o_cnt/valid_cnt as noxORate,
                       invalid_resturea_cnt/valid_cnt as restUreaRate,
                       invalid_restfuel_cnt/valid_cnt as restFuelRate,
                       invalid_dpf_pres_diff_cnt/valid_cnt as dpfRate,
                       invalid_lon_cnt/valid_cnt as lonRate,
                       invalid_lat_cnt/valid_cnt as latRate,
                       invalid_diagnostic_protocol_cnt/obd_cnt as diagnosticProtocolRate,
                       invalid_mil_state_cnt/obd_cnt as milStateRate,
                       invalid_defect_code_cnt/obd_cnt as defectCodeCntRate,
                       invalid_iupr_cnt/obd_cnt as iuprRate
                FROM dw_dwm.dwm_motor_twc_supply_columns_invalid_cnt_byvin_monthtable
                WHERE pt_month = #{ptMonth}
                      and manufacturer_build_name = #{manufacturerBuildName}
                      and epa_vehicle_type = #{epaVehicleType}
                      <if test="vin != null and vin != ''">
                          and vin like CONCAT('%', #{vin}, '%')
                      </if>
                      <if test="actualAgreement != null and actualAgreement != ''">
                          and actual_agreement = #{actualAgreement}
                      </if>
                      <if test="engineManufacturerName != null and engineManufacturerName != ''">
                          and engine_manufacturer_name = #{engineManufacturerName}
                      </if>
                      <if test="engineModel != null and engineModel != ''">
                          and engine_model = #{engineModel}
                      </if>
                ) t
        ) result
    </select>
    
    <!-- 燃气车型VIN码的数据缺失率 -->
    <select id="getGasVinLossrateList" resultType="com.portal.obd_dataquality_score.domain.GasVinLossrateList">
        SELECT * FROM (
            SELECT 
                vin,
                manufacturerBuildName,
                engineModel,
                engineManufacturerName,
                epaVehicleType,
                actualAgreement,
                threeCatalystO3InRate,
                threeCatalystO3OutRate,
                lonRate,
                latRate,
                diagnosticProtocolRate,
                milStateRate,
                defectCodeCntRate,
                iuprRate,
                threeCatalystO3InRate as three_catalyst_o3_in_rate,
                threeCatalystO3OutRate as three_catalyst_o3_out_rate,
                lonRate as lon_rate,
                latRate as lat_rate,
                diagnosticProtocolRate as diagnostic_protocol_rate,
                milStateRate as mil_state_rate,
                defectCodeCntRate as defect_code_cnt_rate,
                iuprRate as iupr_rate
            FROM (
                SELECT vin,
                       manufacturer_build_name as manufacturerBuildName,
                       engine_model as engineModel,
                       engine_manufacturer_name as engineManufacturerName,
                       epa_vehicle_type as epaVehicleType,
                       actual_agreement as actualAgreement,
                       invalid_three_catalyst_o3_in_cnt/valid_cnt as threeCatalystO3InRate,
                       invalid_three_catalyst_o3_out_cnt/valid_cnt as threeCatalystO3OutRate,
                       invalid_lon_cnt/valid_cnt as lonRate,
                       invalid_lat_cnt/valid_cnt as latRate,
                       invalid_diagnostic_protocol_cnt/obd_cnt as diagnosticProtocolRate,
                       invalid_mil_state_cnt/obd_cnt as milStateRate,
                       invalid_defect_code_cnt/obd_cnt as defectCodeCntRate,
                       invalid_iupr_cnt/obd_cnt as iuprRate
                FROM dw_dwm.dwm_motor_twc_supply_columns_invalid_cnt_byvin_monthtable
                WHERE pt_month = #{ptMonth}
                      and manufacturer_build_name = #{manufacturerBuildName}
                      and epa_vehicle_type = #{epaVehicleType}
                      <if test="vin != null and vin != ''">
                          and vin like CONCAT('%', #{vin}, '%')
                      </if>
                      <if test="actualAgreement != null and actualAgreement != ''">
                          and actual_agreement = #{actualAgreement}
                      </if>
                      <if test="engineManufacturerName != null and engineManufacturerName != ''">
                          and engine_manufacturer_name = #{engineManufacturerName}
                      </if>
                      <if test="engineModel != null and engineModel != ''">
                          and engine_model = #{engineModel}
                      </if>
                ) t
        ) result
    </select>

    <!-- 获取企业名称列表 -->
    <select id="getManufacturerBuildNameList" resultType="java.lang.String">
        SELECT DISTINCT 
               manufacturer_build_name 
        FROM dw_ods.sys_vehicle_basis_sync
        WHERE manufacturer_build_name IS NOT NULL 	
              AND UPPER(manufacturer_build_name) != 'NULL'
              AND TRIM(manufacturer_build_name) != ''
    </select>
    
    <!-- 获取发动机生产厂家列表 -->
    <select id="getEngineManufacturerNameList" resultType="java.lang.String">
        SELECT DISTINCT 
               engine_manufacturer_name 
        FROM dw_ods.sys_vehicle_basis_sync
        WHERE engine_manufacturer_name IS NOT NULL 	
              AND UPPER(engine_manufacturer_name) != 'NULL'
              AND TRIM(engine_manufacturer_name) != ''
    </select>
    
    <!-- 获取发动机型号列表 -->
    <select id="getEngineModelList" resultType="java.lang.String">
        SELECT DISTINCT 
               engine_model 
        FROM dw_ods.sys_vehicle_basis_sync
        WHERE UPPER(engine_model) != 'NULL'
              AND TRIM(engine_model) != ''
              AND engine_manufacturer_name = #{engineManufacturerName}
    </select>

    <select id="getVehicleDataSummaryByVtype" resultType="com.portal.obd_dataquality_score.domain.VehicleDataSummary">
        with t1 as 
        (select pt_month,
             epa_vehicle_type as epa_vehicle_type,
             sum(online_cnt) as onlineCnt,  
             sum(connected_cnt) as connectedCnt
        from dw_dwm.dwm_online_connected_vehicles_cnt_monthtable 
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="actualAgreement != null and actualAgreement != ''">
                and actual_agreement like CONCAT('%', #{actualAgreement}, '%')
            </if>
        group by pt_month,
                 epa_vehicle_type),
         t2 as 
         (select pt_month,
             ctype as epa_vehicle_type,
             sum(offline_cnt) as offlineCnt
        from 
        dw_dwm.dwm_obd_data_longterm_offline_rate_ctype_agreement 
        where pt_month = #{ptMonth}
            and manufacturer_build_name = #{manufacturerBuildName}
            <if test="actualAgreement != null and actualAgreement != ''">
                and agreement like CONCAT('%', #{actualAgreement}, '%')
            </if>
        group by pt_month,
                 ctype)
        select 
          t1.pt_month as ptMonth,
          t1.epa_vehicle_type as epaVehicleType,
          t1.onlineCnt,
          t1.connectedCnt,
          t2.offlineCnt
        from t1 inner join t2 
          on t1.pt_month = t2.pt_month 
          and t1.epa_vehicle_type = t2.epa_vehicle_type
    </select>

</mapper> 