<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.portal.shiyunhui_daily_report.mapper.VehicleOnlineMapper">

    <!-- 获取车辆上线统计 -->
    <select id="getVehicleOnlineStats" resultType="com.portal.shiyunhui_daily_report.domain.VehicleOnlineStat"
            parameterType="com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam">
            <choose>
                <!-- 查询全国所有省份数据 -->
            <when test="(provinceAdcode == null or provinceAdcode == '') and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
            SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.province_center AS provinceCenter,
                    NULL AS provinceGeometry,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS cityCenter,
                    NULL AS cityGeometry,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    NULL AS districtCenter,
                    NULL AS districtGeometry,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                        WHEN SUM(t1.online_cars) > 0 THEN
                            ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                        ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_province t1
                INNER JOIN
                (SELECT DISTINCT province_nm, province_adcode, province_center, province_geometry
                 FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province
                <where>
                <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                </if>
                <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                    </if>
                </where>
                GROUP BY
                    t2.province_nm, t2.province_adcode, t2.province_center
                    <if test="epaType != null and epaType != ''">
                        , t1.epa_type
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        , t1.vehicle_type
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        , t1.engine_model
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        , t1.engine_manufacturer_name
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        , t1.manufacturer_build_name
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        , t1.vehicle_loads
                    </if>
                    , t1.pt_date
            </when>
                <!-- 查询指定省份下所有市的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                    SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.province_center AS provinceCenter,
                    null AS provinceGeometry,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    t2.city_center AS cityCenter,
                    NULL AS cityGeometry,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    NULL AS districtCenter,
                    NULL AS districtGeometry,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                    WHEN SUM(t1.online_cars) > 0 THEN
                    ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                    ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                    FROM dw_ads.ads_daily_report_vehicle_online_city t1
                    INNER JOIN
                    (SELECT DISTINCT province_nm, province_adcode, province_center, province_geometry,
                    city_nm, city_adcode, city_center, city_geometry
                    FROM dw_dim.dim_area_info_extend_whole_country
                    WHERE province_adcode = #{provinceAdcode}) t2
                    ON t2.province_nm = t1.province AND t2.city_nm = t1.city
                    <where>
                        <if test="ptDate != null and ptDate != ''">
                            t1.pt_date = #{ptDate}
                        </if>
                        <if test="epaType != null and epaType != ''">
                            AND t1.epa_type = #{epaType}
                        </if>
                        <if test="vehicleType != null and vehicleType != ''">
                            AND t1.vehicle_type = #{vehicleType}
                        </if>
                        <if test="engineModel != null and engineModel != ''">
                            AND t1.engine_model = #{engineModel}
                        </if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">
                            AND t1.engine_manufacturer_name = #{engineManufacturerName}
                        </if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                            AND t1.manufacturer_build_name = #{manufacturerBuildName}
                        </if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">
                            AND t1.vehicle_loads = #{vehicleLoads}
                        </if>
                    </where>
                    GROUP BY
                    t2.province_nm, t2.province_adcode, t2.province_center,
                    t2.city_nm, t2.city_adcode, t2.city_center
                    <if test="epaType != null and epaType != ''">
                        , t1.epa_type
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        , t1.vehicle_type
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        , t1.engine_model
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        , t1.engine_manufacturer_name
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        , t1.manufacturer_build_name
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        , t1.vehicle_loads
                    </if>
                    , t1.pt_date

                </when>
                <!-- 查询指定市下所有区域数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and cityAdcode != null and cityAdcode != '' and (districtAdcode == null or districtAdcode == '')">
                SELECT 
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.province_center AS provinceCenter,
                    null AS provinceGeometry,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    t2.city_center AS cityCenter,
                    NULL AS cityGeometry,
                    t2.district_nm AS districtNm,
                    t2.district_adcode AS districtAdcode,
                    t2.district_center AS districtCenter,
                    NULL AS districtGeometry,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                SUM(t1.online_cars) AS onlineCars,
                SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                CASE
                WHEN SUM(t1.online_cars) > 0 THEN
                ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                ELSE 0
                END AS growthRate,
                t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_county t1
                INNER JOIN
                (SELECT DISTINCT province_nm, province_adcode, province_center, province_geometry,
                        city_nm, city_adcode, city_center, city_geometry,
                        district_nm, district_adcode, district_center, district_geometry
                 FROM dw_dim.dim_area_info_extend_whole_country
                 WHERE city_adcode = #{cityAdcode}) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city AND t2.district_nm = t1.county
                <where>
                    <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                    </if>
                    <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                </if>
                </where>
                GROUP BY
                    t2.province_nm, t2.province_adcode, t2.province_center,
                    t2.city_nm, t2.city_adcode, t2.city_center,
                    t2.district_nm, t2.district_adcode, t2.district_center
                    <if test="epaType != null and epaType != ''">
                        , t1.epa_type
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        , t1.vehicle_type
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        , t1.engine_model
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        , t1.engine_manufacturer_name
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        , t1.manufacturer_build_name
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        , t1.vehicle_loads
                    </if>
                    , t1.pt_date
            </when>
               <!-- 查询指定区域的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and cityAdcode != null and cityAdcode != '' and districtAdcode != null and districtAdcode != ''">
                SELECT 
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.province_center AS provinceCenter,
                    null AS provinceGeometry,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    t2.city_center AS cityCenter,
                    null AS cityGeometry,
                    t2.district_nm AS districtNm,
                    t2.district_adcode AS districtAdcode,
                    t2.district_center AS districtCenter,
                    NULL AS districtGeometry,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                    WHEN SUM(t1.online_cars) > 0 THEN
                        ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                    ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_county t1
                INNER JOIN
                (SELECT DISTINCT province_nm, province_adcode, province_center, province_geometry,
                        city_nm, city_adcode, city_center, city_geometry,
                        district_nm, district_adcode, district_center, district_geometry
                FROM dw_dim.dim_area_info_extend_whole_country
                WHERE district_adcode = #{districtAdcode}) t2
                ON t2.province_nm = t1.province 
                AND t2.city_nm = t1.city 
                AND t2.district_nm = t1.county
                <where>
                <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                </if>
                <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                </if>
                </where>
                GROUP BY
                    t2.province_nm, t2.province_adcode, t2.province_center,
                    t2.city_nm, t2.city_adcode, t2.city_center,
                    t2.district_nm, t2.district_adcode, t2.district_center
                    <if test="epaType != null and epaType != ''">
                        , t1.epa_type
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        , t1.vehicle_type
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        , t1.engine_model
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        , t1.engine_manufacturer_name
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        , t1.manufacturer_build_name
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        , t1.vehicle_loads
                    </if>
                    , t1.pt_date

                </when>
            </choose>
    </select>

    <!-- 获取单车每日行驶里程 -->
    <select id="getVehicleDriveOdometer" resultType="com.portal.shiyunhui_daily_report.domain.VehicleDriveOdometer"
            parameterType="com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam">
        <choose>
            <!-- 没传省市区参数，默认查询四川省数据 -->
            <when test="(provinceAdcode == null or provinceAdcode == '') and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    t1.epa_type AS epaType,
                    t1.vehicle_type AS vehicleType,
                    t1.engine_model AS engineModel,
                    t1.engine_manufacturer_name AS engineManufacturerName,
                    t1.manufacturer_build_name AS manufacturerBuildName,
                    t1.vehicle_loads AS vehicleLoads,
                    t1.vin,
                    t1.driving_odometer AS drivingOdometer,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province
                <where>
                    t1.pt_date = #{ptDate} AND t2.province_adcode = '510000'
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
            </when>

            <!-- 传入省参数，查询对应省份的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    t1.epa_type AS epaType,
                    t1.vehicle_type AS vehicleType,
                    t1.engine_model AS engineModel,
                    t1.engine_manufacturer_name AS engineManufacturerName,
                    t1.manufacturer_build_name AS manufacturerBuildName,
                    t1.vehicle_loads AS vehicleLoads,
                    t1.vin,
                    t1.driving_odometer AS drivingOdometer,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province
                <where>
                    t1.pt_date = #{ptDate} AND t2.province_adcode = #{provinceAdcode}
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
            </when>

            <!-- 传入省+市参数，查询对应市的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and cityAdcode != null and cityAdcode != '' and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    t1.epa_type AS epaType,
                    t1.vehicle_type AS vehicleType,
                    t1.engine_model AS engineModel,
                    t1.engine_manufacturer_name AS engineManufacturerName,
                    t1.manufacturer_build_name AS manufacturerBuildName,
                    t1.vehicle_loads AS vehicleLoads,
                    t1.vin,
                    t1.driving_odometer AS drivingOdometer,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode, city_nm, city_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city
                <where>
                    t1.pt_date = #{ptDate} AND t2.city_adcode = #{cityAdcode}
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
            </when>

            <!-- 传入省+市+区参数，查询对应区的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and cityAdcode != null and cityAdcode != '' and districtAdcode != null and districtAdcode != ''">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    t2.district_nm AS districtNm,
                    t2.district_adcode AS districtAdcode,
                    t1.epa_type AS epaType,
                    t1.vehicle_type AS vehicleType,
                    t1.engine_model AS engineModel,
                    t1.engine_manufacturer_name AS engineManufacturerName,
                    t1.manufacturer_build_name AS manufacturerBuildName,
                    t1.vehicle_loads AS vehicleLoads,
                    t1.vin,
                    t1.driving_odometer AS drivingOdometer,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode, city_nm, city_adcode, district_nm, district_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city AND t2.district_nm = t1.county
                <where>
                    t1.pt_date = #{ptDate} AND t2.district_adcode = #{districtAdcode}
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
            </when>
        </choose>
    </select>

    <!-- 获取热点时段车辆数 -->
    <select id="getVehicleHotHour" resultType="com.portal.shiyunhui_daily_report.domain.VehicleHotHour"
            parameterType="com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam">
        <choose>
            <!-- 没传省市区参数，默认查询四川省数据 -->
            <when test="(provinceAdcode == null or provinceAdcode == '') and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    t1.hour,
                    SUM(t1.online_cars) AS onlineCars,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_hot_hour t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province
                <where>
                    t1.pt_date = #{ptDate} AND t2.province_adcode = '510000'
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
                GROUP BY t2.province_nm, t2.province_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.hour, t1.pt_date
                    </trim>
                ORDER BY t1.hour
            </when>

            <!-- 传入省参数，查询对应省份的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    t1.hour,
                    SUM(t1.online_cars) AS onlineCars,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_hot_hour t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province
                <where>
                    t1.pt_date = #{ptDate} AND t2.province_adcode = #{provinceAdcode}
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
                GROUP BY t2.province_nm, t2.province_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.hour, t1.pt_date
                    </trim>
                ORDER BY t1.hour
            </when>

            <!-- 传入省+市参数，查询对应市的数据 -->
            <when test="cityAdcode != null and cityAdcode != '' and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    t1.hour,
                    SUM(t1.online_cars) AS onlineCars,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_hot_hour t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode, city_nm, city_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city
                <where>
                    t1.pt_date = #{ptDate} AND t2.city_adcode = #{cityAdcode}
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
                GROUP BY t2.province_nm, t2.province_adcode, t2.city_nm, t2.city_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.hour, t1.pt_date
                    </trim>
                ORDER BY t1.hour
            </when>

            <!-- 传入省+市+区参数，查询对应区的数据 -->
            <otherwise>
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    t2.district_nm AS districtNm,
                    t2.district_adcode AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    t1.hour,
                    SUM(t1.online_cars) AS onlineCars,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_hot_hour t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode, city_nm, city_adcode, district_nm, district_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city AND t2.district_nm = t1.county
                <where>
                    t1.pt_date = #{ptDate} AND t2.district_adcode = #{districtAdcode}
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
                GROUP BY t2.province_nm, t2.province_adcode, t2.city_nm, t2.city_adcode, t2.district_nm, t2.district_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.hour, t1.pt_date
                    </trim>
                ORDER BY t1.hour
            </otherwise>
        </choose>
    </select>

    <!-- 获取总行驶里程和超排车辆数统计 -->
    <select id="getTotalOdometer" resultType="com.portal.shiyunhui_daily_report.domain.VehicleTotalOdometer"
            parameterType="com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam">
        <choose>
            <!-- 1. 全国汇总数据 -->
            <when test="(provinceAdcode == null or provinceAdcode == '') and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    '全国' AS provinceNm,
                    'TOTAL' AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    -- 累计行驶里程
                    ROUND(SUM(valid_odometer), 4) AS drivingOdometer,
                    -- 昨日总行驶里程
                    ROUND(SUM(valid_odometer_yesterday), 4) AS drivingOdometerYesterday,
                    CASE
                        WHEN SUM(valid_odometer_yesterday) > 0 THEN
                        ROUND((SUM(valid_odometer) - SUM(valid_odometer_yesterday)) / SUM(valid_odometer_yesterday), 4)
                        ELSE 0
                    END AS odometerGrowthRate,
                    -- 超排车辆数
                    SUM(emission_current) AS overEmissionCars,
                    SUM(emission_yesterday) AS overEmissionCarsYesterday,
                    CASE
                        WHEN SUM(emission_current) > 0 THEN
                        ROUND((SUM(emission_current) - SUM(emission_yesterday)) / SUM(emission_yesterday), 4)
                        ELSE 0
                    END AS emissionGrowthRate,
                    t1.pt_date AS ptDate
                FROM (
                    SELECT
                        t1.driving_odometer,
                        t1.driving_odometer_yesterday,
                        t1.is_over_emission,
                        t1.is_over_emission_yesterday,
                        t1.pt_date,
                        t1.province,
                        <if test="epaType != null and epaType != ''">t1.epa_type,</if>
                        <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type,</if>
                        <if test="engineModel != null and engineModel != ''">t1.engine_model,</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name,</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name,</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads,</if>
                        CASE WHEN t1.driving_odometer > 1200 THEN 0 ELSE t1.driving_odometer END AS valid_odometer,
                        CASE WHEN t1.driving_odometer_yesterday > 1200 THEN 0 ELSE t1.driving_odometer_yesterday END AS valid_odometer_yesterday,
                        CASE WHEN COALESCE(t1.is_over_emission, 0) != 0 THEN CAST(COALESCE(t1.is_over_emission, 0) AS INT) ELSE 0 END AS emission_current,
                        CASE WHEN COALESCE(t1.is_over_emission, 0) != 0 THEN CAST(COALESCE(t1.is_over_emission_yesterday, 0) AS INT) ELSE 0 END AS emission_yesterday
                    FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                    <where>
                        <if test="ptDate != null and ptDate != ''">
                            t1.pt_date = #{ptDate}
                        </if>
                        <if test="epaType != null and epaType != ''">
                            AND t1.epa_type = #{epaType}
                        </if>
                        <if test="vehicleType != null and vehicleType != ''">
                            AND t1.vehicle_type = #{vehicleType}
                        </if>
                        <if test="engineModel != null and engineModel != ''">
                            AND t1.engine_model = #{engineModel}
                        </if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">
                            AND t1.engine_manufacturer_name = #{engineManufacturerName}
                        </if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                            AND t1.manufacturer_build_name = #{manufacturerBuildName}
                        </if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">
                            AND t1.vehicle_loads = #{vehicleLoads}
                        </if>
                    </where>
                ) t1
                GROUP BY
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.pt_date
                    </trim>
            </when>
            <!-- 2. 省份汇总数据 -->
            <when test="(provinceAdcode != null and provinceAdcode != '') and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    -- 累计行驶里程
                    ROUND(SUM(valid_odometer), 4) AS drivingOdometer,
                    -- 昨日总行驶里程
                    ROUND(SUM(valid_odometer_yesterday), 4) AS drivingOdometerYesterday,
                    CASE
                        WHEN SUM(valid_odometer_yesterday) > 0 THEN
                        ROUND((SUM(valid_odometer) - SUM(valid_odometer_yesterday)) / SUM(valid_odometer_yesterday), 4)
                        ELSE 0
                    END AS odometerGrowthRate,
                    -- 超排车辆数
                    SUM(emission_current) AS overEmissionCars,
                    SUM(emission_yesterday) AS overEmissionCarsYesterday,
                    CASE
                        WHEN SUM(emission_current) > 0 THEN
                        ROUND((SUM(emission_current) - SUM(emission_yesterday)) / SUM(emission_yesterday), 4)
                        ELSE 0
                    END AS emissionGrowthRate,
                    t1.pt_date AS ptDate
                FROM (
                    SELECT
                        t1.driving_odometer,
                        t1.driving_odometer_yesterday,
                        t1.is_over_emission,
                        t1.is_over_emission_yesterday,
                        t1.pt_date,
                        t1.province,
                        t1.city,
                        <if test="districtAdcode != null and districtAdcode != ''">t1.county,</if>
                        <if test="epaType != null and epaType != ''">t1.epa_type,</if>
                        <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type,</if>
                        <if test="engineModel != null and engineModel != ''">t1.engine_model,</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name,</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name,</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads,</if>
                        CASE WHEN t1.driving_odometer > 1200 THEN 0 ELSE t1.driving_odometer END AS valid_odometer,
                        CASE WHEN t1.driving_odometer_yesterday > 1200 THEN 0 ELSE t1.driving_odometer_yesterday END AS valid_odometer_yesterday,
                        CASE WHEN COALESCE(t1.is_over_emission, 0) != 0 THEN CAST(COALESCE(t1.is_over_emission, 0) AS INT) ELSE 0 END AS emission_current,
                        CASE WHEN COALESCE(t1.is_over_emission, 0) != 0 THEN CAST(COALESCE(t1.is_over_emission_yesterday, 0) AS INT) ELSE 0 END AS emission_yesterday
                    FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                    <where>
                        <if test="ptDate != null and ptDate != ''">
                            t1.pt_date = #{ptDate}
                        </if>
                        <!-- 省份过滤条件 -->
                        AND t1.province = (SELECT province_nm FROM dw_dim.dim_area_info_extend_whole_country WHERE province_adcode = #{provinceAdcode} LIMIT 1)
                        <if test="epaType != null and epaType != ''">
                            AND t1.epa_type = #{epaType}
                        </if>
                        <if test="vehicleType != null and vehicleType != ''">
                            AND t1.vehicle_type = #{vehicleType}
                        </if>
                        <if test="engineModel != null and engineModel != ''">
                            AND t1.engine_model = #{engineModel}
                        </if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">
                            AND t1.engine_manufacturer_name = #{engineManufacturerName}
                        </if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                            AND t1.manufacturer_build_name = #{manufacturerBuildName}
                        </if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">
                            AND t1.vehicle_loads = #{vehicleLoads}
                        </if>
                    </where>
                ) t1
                INNER JOIN
                (SELECT DISTINCT province_nm, province_adcode
                FROM dw_dim.dim_area_info_extend_whole_country
                WHERE province_adcode = #{provinceAdcode}) t2
                ON t2.province_nm = t1.province
                GROUP BY
                    t2.province_nm, t2.province_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.pt_date
                    </trim>
            </when>
            <!-- 3. 市汇总数据 -->
            <when test="(provinceAdcode != null and provinceAdcode != '') and (cityAdcode != null and cityAdcode != '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    -- 累计行驶里程
                    ROUND(SUM(valid_odometer), 4) AS drivingOdometer,
                    -- 昨日总行驶里程
                    ROUND(SUM(valid_odometer_yesterday), 4) AS drivingOdometerYesterday,
                    CASE
                        WHEN SUM(valid_odometer_yesterday) > 0 THEN
                        ROUND((SUM(valid_odometer) - SUM(valid_odometer_yesterday)) / SUM(valid_odometer_yesterday), 4)
                        ELSE 0
                    END AS odometerGrowthRate,
                    -- 超排车辆数
                    SUM(emission_current) AS overEmissionCars,
                    SUM(emission_yesterday) AS overEmissionCarsYesterday,
                    CASE
                        WHEN SUM(emission_current) > 0 THEN
                        ROUND((SUM(emission_current) - SUM(emission_yesterday)) / SUM(emission_yesterday), 4)
                        ELSE 0
                    END AS emissionGrowthRate,
                    t1.pt_date AS ptDate
                FROM (
                    SELECT
                        t1.driving_odometer,
                        t1.driving_odometer_yesterday,
                        t1.is_over_emission,
                        t1.is_over_emission_yesterday,
                        t1.pt_date,
                        t1.province,
                        t1.city,
                        t1.county,
                        <if test="epaType != null and epaType != ''">t1.epa_type,</if>
                        <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type,</if>
                        <if test="engineModel != null and engineModel != ''">t1.engine_model,</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name,</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name,</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads,</if>
                        CASE WHEN t1.driving_odometer > 1200 THEN 0 ELSE t1.driving_odometer END AS valid_odometer,
                        CASE WHEN t1.driving_odometer_yesterday > 1200 THEN 0 ELSE t1.driving_odometer_yesterday END AS valid_odometer_yesterday,
                        CASE WHEN COALESCE(t1.is_over_emission, 0) != 0 THEN CAST(COALESCE(t1.is_over_emission, 0) AS INT) ELSE 0 END AS emission_current,
                        CASE WHEN COALESCE(t1.is_over_emission, 0) != 0 THEN CAST(COALESCE(t1.is_over_emission_yesterday, 0) AS INT) ELSE 0 END AS emission_yesterday
                    FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                    <where>
                        <if test="ptDate != null and ptDate != ''">
                            t1.pt_date = #{ptDate}
                        </if>
                        <!-- 市过滤条件 -->
                        AND t1.province = (SELECT province_nm FROM dw_dim.dim_area_info_extend_whole_country WHERE city_adcode = #{cityAdcode} LIMIT 1)
                        AND t1.city = (SELECT city_nm FROM dw_dim.dim_area_info_extend_whole_country WHERE city_adcode = #{cityAdcode} LIMIT 1)
                        <if test="epaType != null and epaType != ''">
                            AND t1.epa_type = #{epaType}
                        </if>
                        <if test="vehicleType != null and vehicleType != ''">
                            AND t1.vehicle_type = #{vehicleType}
                        </if>
                        <if test="engineModel != null and engineModel != ''">
                            AND t1.engine_model = #{engineModel}
                        </if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">
                            AND t1.engine_manufacturer_name = #{engineManufacturerName}
                        </if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                            AND t1.manufacturer_build_name = #{manufacturerBuildName}
                        </if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">
                            AND t1.vehicle_loads = #{vehicleLoads}
                        </if>
                    </where>
                ) t1
                INNER JOIN
                (SELECT DISTINCT province_nm, province_adcode,
                city_nm, city_adcode
                FROM dw_dim.dim_area_info_extend_whole_country
                WHERE city_adcode = #{cityAdcode}) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city
                GROUP BY
                    t2.province_nm, t2.province_adcode,
                    t2.city_nm, t2.city_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.pt_date
                    </trim>
            </when>
            <!-- 4. 区级数据：传省份+市+区代码 - 查询该区的详细数据 -->
            <when test="(provinceAdcode != null and provinceAdcode != '') and (cityAdcode != null and cityAdcode != '') and (districtAdcode != null and districtAdcode != '')">
                <!-- 查询指定区的详细数据 -->
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    t2.district_nm AS districtNm,
                    t2.district_adcode AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    -- 累计行驶里程
                    ROUND(SUM(valid_odometer), 4) AS drivingOdometer,
                    -- 昨日总行驶里程
                    ROUND(SUM(valid_odometer_yesterday), 4) AS drivingOdometerYesterday,
                    CASE
                        WHEN SUM(valid_odometer_yesterday) > 0 THEN
                        ROUND((SUM(valid_odometer) - SUM(valid_odometer_yesterday)) / SUM(valid_odometer_yesterday), 4)
                        ELSE 0
                    END AS odometerGrowthRate,
                    -- 超排车辆数
                    SUM(emission_current) AS overEmissionCars,
                    SUM(emission_yesterday) AS overEmissionCarsYesterday,
                    CASE
                        WHEN SUM(emission_current) > 0 THEN
                        ROUND((SUM(emission_current) - SUM(emission_yesterday)) / SUM(emission_yesterday), 4)
                        ELSE 0
                    END AS emissionGrowthRate,
                    t1.pt_date AS ptDate
                FROM (
                    SELECT
                        t1.driving_odometer,
                        t1.driving_odometer_yesterday,
                        t1.is_over_emission,
                        t1.is_over_emission_yesterday,
                        t1.pt_date,
                        t1.province,
                        t1.city,
                        t1.county,
                        <if test="epaType != null and epaType != ''">t1.epa_type,</if>
                        <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type,</if>
                        <if test="engineModel != null and engineModel != ''">t1.engine_model,</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name,</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name,</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads,</if>
                        CASE WHEN t1.driving_odometer > 1200 THEN 0 ELSE t1.driving_odometer END AS valid_odometer,
                        CASE WHEN t1.driving_odometer_yesterday > 1200 THEN 0 ELSE t1.driving_odometer_yesterday END AS valid_odometer_yesterday,
                        CASE WHEN COALESCE(t1.is_over_emission, 0) != 0 THEN CAST(COALESCE(t1.is_over_emission, 0) AS INT) ELSE 0 END AS emission_current,
                        CASE WHEN COALESCE(t1.is_over_emission, 0) != 0 THEN CAST(COALESCE(t1.is_over_emission_yesterday, 0) AS INT) ELSE 0 END AS emission_yesterday
                    FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                    <where>
                        <if test="ptDate != null and ptDate != ''">
                            t1.pt_date = #{ptDate}
                        </if>
                        <if test="epaType != null and epaType != ''">
                            AND t1.epa_type = #{epaType}
                        </if>
                        <if test="vehicleType != null and vehicleType != ''">
                            AND t1.vehicle_type = #{vehicleType}
                        </if>
                        <if test="engineModel != null and engineModel != ''">
                            AND t1.engine_model = #{engineModel}
                        </if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">
                            AND t1.engine_manufacturer_name = #{engineManufacturerName}
                        </if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                            AND t1.manufacturer_build_name = #{manufacturerBuildName}
                        </if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">
                            AND t1.vehicle_loads = #{vehicleLoads}
                        </if>
                    </where>
                ) t1
                INNER JOIN
                (SELECT DISTINCT province_nm, province_adcode,
                city_nm, city_adcode,
                district_nm, district_adcode
                FROM dw_dim.dim_area_info_extend_whole_country
                WHERE district_adcode = #{districtAdcode}) t2
                ON t2.province_nm = t1.province
                AND t2.city_nm = t1.city
                AND t2.district_nm = t1.county
                GROUP BY
                    t2.province_nm, t2.province_adcode,
                    t2.city_nm, t2.city_adcode,
                    t2.district_nm, t2.district_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.pt_date
                    </trim>
            </when>
        </choose>
    </select>

    <!-- 获取在线车辆数统计 -->
    <select id="getTotalOnlineCars" resultType="com.portal.shiyunhui_daily_report.domain.VehicleOnlineStat"
            parameterType="com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam">
        <choose>
            <!-- 1. 全国汇总数据 -->
            <when test="(provinceAdcode == null or provinceAdcode == '') and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    '全国' AS provinceNm,
                    'TOTAL' AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    <if test="epaType != null and epaType != ''">epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    SUM(online_cars) AS onlineCars,
                    SUM(online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                        WHEN SUM(online_cars) > 0 THEN
                        ROUND((SUM(online_cars) - SUM(online_cars_yesterday)) / SUM(online_cars_yesterday), 4)
                        ELSE 0
                    END AS growthRate,
                    pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_whole_country
                <where>
                    <if test="ptDate != null and ptDate != ''">
                        pt_date = #{ptDate}
                    </if>
                    <if test="epaType != null and epaType != ''">
                        AND epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND engine_manufacturer_name = #{engineManufacturerName}
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND manufacturer_build_name = #{manufacturerBuildName}
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND vehicle_loads = #{vehicleLoads}
                    </if>
                </where>
                GROUP BY
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, vehicle_loads</if>
                        , pt_date
                    </trim>
            </when>

            <!-- 2. 省份汇总数据 -->
            <when test="(provinceAdcode != null and provinceAdcode != '') and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                        WHEN SUM(t1.online_cars) > 0 THEN
                        ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                        ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_province t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode FROM dw_dim.dim_area_info_extend_whole_country WHERE province_adcode = #{provinceAdcode}) t2
                ON t2.province_nm = t1.province
                <where>
                    <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                    </if>
                    <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                    </if>
                </where>
                GROUP BY
                    t2.province_nm, t2.province_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.pt_date
                    </trim>
            </when>

            <!-- 3. 市汇总数据 -->
            <when test="(provinceAdcode != null and provinceAdcode != '') and (cityAdcode != null and cityAdcode != '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                        WHEN SUM(t1.online_cars) > 0 THEN
                        ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                        ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_city t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode, city_nm, city_adcode FROM dw_dim.dim_area_info_extend_whole_country WHERE city_adcode = #{cityAdcode}) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city
                <where>
                    <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                    </if>
                    <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                    </if>
                </where>
                GROUP BY
                    t2.province_nm, t2.province_adcode, t2.city_nm, t2.city_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.pt_date
                    </trim>
            </when>

            <!-- 4. 区汇总数据 -->
            <when test="(provinceAdcode != null and provinceAdcode != '') and (cityAdcode != null and cityAdcode != '') and (districtAdcode != null and districtAdcode != '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    t2.district_nm AS districtNm,
                    t2.district_adcode AS districtAdcode,
                    <if test="epaType != null and epaType != ''">t1.epa_type AS epaType,</if>
                    <if test="epaType == null or epaType == ''">NULL AS epaType,</if>
                    <if test="vehicleType != null and vehicleType != ''">t1.vehicle_type AS vehicleType,</if>
                    <if test="vehicleType == null or vehicleType == ''">NULL AS vehicleType,</if>
                    <if test="engineModel != null and engineModel != ''">t1.engine_model AS engineModel,</if>
                    <if test="engineModel == null or engineModel == ''">NULL AS engineModel,</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">t1.engine_manufacturer_name AS engineManufacturerName,</if>
                    <if test="engineManufacturerName == null or engineManufacturerName == ''">NULL AS engineManufacturerName,</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">t1.manufacturer_build_name AS manufacturerBuildName,</if>
                    <if test="manufacturerBuildName == null or manufacturerBuildName == ''">NULL AS manufacturerBuildName,</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">t1.vehicle_loads AS vehicleLoads,</if>
                    <if test="vehicleLoads == null or vehicleLoads == ''">NULL AS vehicleLoads,</if>
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                        WHEN SUM(t1.online_cars) > 0 THEN
                        ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                        ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_county t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode, city_nm, city_adcode, district_nm, district_adcode FROM dw_dim.dim_area_info_extend_whole_country WHERE district_adcode = #{districtAdcode}) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city AND t2.district_nm = t1.county
                <where>
                    <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                    </if>
                    <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                    </if>
                </where>
                GROUP BY
                    t2.province_nm, t2.province_adcode, t2.city_nm, t2.city_adcode, t2.district_nm, t2.district_adcode,
                    <trim prefixOverrides=",">
                        <if test="epaType != null and epaType != ''">, t1.epa_type</if>
                        <if test="vehicleType != null and vehicleType != ''">, t1.vehicle_type</if>
                        <if test="engineModel != null and engineModel != ''">, t1.engine_model</if>
                        <if test="engineManufacturerName != null and engineManufacturerName != ''">, t1.engine_manufacturer_name</if>
                        <if test="manufacturerBuildName != null and manufacturerBuildName != ''">, t1.manufacturer_build_name</if>
                        <if test="vehicleLoads != null and vehicleLoads != ''">, t1.vehicle_loads</if>
                        , t1.pt_date
                    </trim>
            </when>
        </choose>
    </select>

    <!-- 获取热点区域统计数据 -->
    <select id="getHotAreaStats" resultType="com.portal.shiyunhui_daily_report.domain.VehicleHotArea"
            parameterType="com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam">
        <choose>
            <!-- 没传省市区参数，返回全国上线车辆数最多的省份数据 -->
            <when test="(provinceAdcode == null or provinceAdcode == '') and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t1.province AS hotArea,
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                        WHEN SUM(t1.online_cars) > 0 THEN
                        ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                        ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_province t1
                <where>
                    <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                    </if>
                    <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                    </if>
                </where>
                GROUP BY t1.province, t1.pt_date
                <if test="epaType != null and epaType != ''">
                    , t1.epa_type
                </if>
                <if test="vehicleType != null and vehicleType != ''">
                    , t1.vehicle_type
                </if>
                <if test="engineModel != null and engineModel != ''">
                    , t1.engine_model
                </if>
                <if test="engineManufacturerName != null and engineManufacturerName != ''">
                    , t1.engine_manufacturer_name
                </if>
                <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                    , t1.manufacturer_build_name
                </if>
                <if test="vehicleLoads != null and vehicleLoads != ''">
                    , t1.vehicle_loads
                </if>
                ORDER BY onlineCars DESC
                LIMIT 10
            </when>

            <!-- 传入省参数，返回该省上线车辆数最多的市的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t1.city AS hotArea,
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                        WHEN SUM(t1.online_cars) > 0 THEN
                        ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                        ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_city t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode FROM dw_dim.dim_area_info_extend_whole_country WHERE province_adcode = #{provinceAdcode}) t2
                ON t1.province = t2.province_nm
                <where>
                    <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                    </if>
                    <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                    </if>
                </where>
                GROUP BY t1.city, t1.pt_date
                <if test="epaType != null and epaType != ''">
                    , t1.epa_type
                </if>
                <if test="vehicleType != null and vehicleType != ''">
                    , t1.vehicle_type
                </if>
                <if test="engineModel != null and engineModel != ''">
                    , t1.engine_model
                </if>
                <if test="engineManufacturerName != null and engineManufacturerName != ''">
                    , t1.engine_manufacturer_name
                </if>
                <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                    , t1.manufacturer_build_name
                </if>
                <if test="vehicleLoads != null and vehicleLoads != ''">
                    , t1.vehicle_loads
                </if>
                ORDER BY onlineCars DESC
                LIMIT 10
            </when>

            <!-- 传入省+市参数，返回该市上线车辆数最多的区域数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and cityAdcode != null and cityAdcode != '' and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t1.county AS hotArea,
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                        WHEN SUM(t1.online_cars) > 0 THEN
                        ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                        ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_county t1
                INNER JOIN (SELECT DISTINCT province_nm, city_nm, city_adcode FROM dw_dim.dim_area_info_extend_whole_country WHERE city_adcode = #{cityAdcode}) t2
                ON t1.province = t2.province_nm AND t1.city = t2.city_nm
                <where>
                    <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                    </if>
                    <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                    </if>
                </where>
                GROUP BY t1.county, t1.pt_date
                <if test="epaType != null and epaType != ''">
                    , t1.epa_type
                </if>
                <if test="vehicleType != null and vehicleType != ''">
                    , t1.vehicle_type
                </if>
                <if test="engineModel != null and engineModel != ''">
                    , t1.engine_model
                </if>
                <if test="engineManufacturerName != null and engineManufacturerName != ''">
                    , t1.engine_manufacturer_name
                </if>
                <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                    , t1.manufacturer_build_name
                </if>
                <if test="vehicleLoads != null and vehicleLoads != ''">
                    , t1.vehicle_loads
                </if>
                ORDER BY onlineCars DESC
                LIMIT 10
            </when>

            <!-- 传入省+市+区参数，返回该区具体数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and cityAdcode != null and cityAdcode != '' and districtAdcode != null and districtAdcode != ''">
                SELECT
                    t1.county AS hotArea,
                    SUM(t1.online_cars) AS onlineCars,
                    SUM(t1.online_cars_yesterday) AS onlineCarsYesterday,
                    CASE
                        WHEN SUM(t1.online_cars) > 0 THEN
                        ROUND((SUM(t1.online_cars) - SUM(t1.online_cars_yesterday)) / SUM(t1.online_cars_yesterday), 4)
                        ELSE 0
                    END AS growthRate,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_online_county t1
                INNER JOIN (SELECT DISTINCT province_nm, city_nm, district_nm, district_adcode FROM dw_dim.dim_area_info_extend_whole_country WHERE district_adcode = #{districtAdcode}) t2
                ON t1.province = t2.province_nm AND t1.city = t2.city_nm AND t1.county = t2.district_nm
                <where>
                    <if test="ptDate != null and ptDate != ''">
                        t1.pt_date = #{ptDate}
                    </if>
                    <if test="epaType != null and epaType != ''">
                        AND t1.epa_type = #{epaType}
                    </if>
                    <if test="vehicleType != null and vehicleType != ''">
                        AND t1.vehicle_type = #{vehicleType}
                    </if>
                    <if test="engineModel != null and engineModel != ''">
                        AND t1.engine_model = #{engineModel}
                    </if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">
                        AND t1.engine_manufacturer_name = #{engineManufacturerName}
                    </if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                        AND t1.manufacturer_build_name = #{manufacturerBuildName}
                    </if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">
                        AND t1.vehicle_loads = #{vehicleLoads}
                    </if>
                </where>
                GROUP BY t1.county, t1.pt_date
                <if test="epaType != null and epaType != ''">
                    , t1.epa_type
                </if>
                <if test="vehicleType != null and vehicleType != ''">
                    , t1.vehicle_type
                </if>
                <if test="engineModel != null and engineModel != ''">
                    , t1.engine_model
                </if>
                <if test="engineManufacturerName != null and engineManufacturerName != ''">
                    , t1.engine_manufacturer_name
                </if>
                <if test="manufacturerBuildName != null and manufacturerBuildName != ''">
                    , t1.manufacturer_build_name
                </if>
                <if test="vehicleLoads != null and vehicleLoads != ''">
                    , t1.vehicle_loads
                </if>
                ORDER BY onlineCars DESC
                LIMIT 10
            </when>
            
            <otherwise>
                SELECT
                    NULL AS hotArea,
                    0 AS onlineCars,
                    0 AS onlineCarsYesterday,
                    0 AS growthRate,
                    #{ptDate} AS ptDate
                FROM DUAL
                WHERE 1=0
            </otherwise>
        </choose>
    </select>

    <!-- 获取维度值列表 -->
    <select id="getDimensionValues" resultType="String"
            parameterType="com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam">
        <choose>
            <when test="dimensionType == 'epaType'">
                SELECT DISTINCT epa_vehicle_type
                FROM dw_ods.sys_vehicle_basis_sync
                WHERE epa_vehicle_type IS NOT NULL AND epa_vehicle_type != ''
                ORDER BY epa_vehicle_type
            </when>
            <when test="dimensionType == 'vehicleType'">
                SELECT DISTINCT vehicle_type
                FROM dw_ods.sys_vehicle_basis_sync
                WHERE vehicle_type IS NOT NULL AND vehicle_type != ''
                ORDER BY vehicle_type
            </when>
            <when test="dimensionType == 'engineModel'">
                SELECT DISTINCT engine_model
                FROM dw_ods.sys_vehicle_basis_sync
                WHERE engine_model IS NOT NULL AND engine_model != ''
                ORDER BY engine_model
            </when>
            <when test="dimensionType == 'engineManufacturerName'">
                SELECT DISTINCT engine_manufacturer_name
                FROM dw_ods.sys_vehicle_basis_sync
                WHERE engine_manufacturer_name IS NOT NULL AND engine_manufacturer_name != ''
                ORDER BY engine_manufacturer_name
            </when>
            <when test="dimensionType == 'manufacturerBuildName'">
                SELECT DISTINCT manufacturer_build_name
                FROM dw_ods.sys_vehicle_basis_sync
                WHERE manufacturer_build_name IS NOT NULL AND manufacturer_build_name != ''
                ORDER BY manufacturer_build_name
            </when>
            <when test="dimensionType == 'vehicleLoads'">
                SELECT DISTINCT vehicle_loads
                FROM dw_ods.sys_vehicle_basis_sync
                WHERE vehicle_loads IS NOT NULL AND vehicle_loads != ''
                ORDER BY vehicle_loads
            </when>
            <otherwise>
                SELECT 'Invalid dimensionType' as error_message
            </otherwise>
        </choose>
    </select>

    <!-- 根据adcode获取围栏几何数据 -->
    <select id="getGeometryByAdcode" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT DISTINCT geometry
        FROM dw_dim.dim_area_info_whole_country
        WHERE adcode = #{adcode}
    </select>

    <!-- 获取超排车辆数据 -->
    <select id="getVehicleOverEmission" resultType="com.portal.shiyunhui_daily_report.domain.VehicleOverEmission"
            parameterType="com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam">
        <choose>
            <!-- 没传省市区参数，默认查询四川省数据 -->
            <when test="(provinceAdcode == null or provinceAdcode == '') and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    t1.epa_type AS epaType,
                    t1.vehicle_type AS vehicleType,
                    t1.engine_model AS engineModel,
                    t1.engine_manufacturer_name AS engineManufacturerName,
                    t1.manufacturer_build_name AS manufacturerBuildName,
                    t1.vehicle_loads AS vehicleLoads,
                    t1.vin,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province
                <where>
                    t1.pt_date = #{ptDate} AND t2.province_adcode = '510000' AND t1.is_over_emission = 1
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
            </when>

            <!-- 传入省参数，查询对应省份的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and (cityAdcode == null or cityAdcode == '') and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    NULL AS cityNm,
                    NULL AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    t1.epa_type AS epaType,
                    t1.vehicle_type AS vehicleType,
                    t1.engine_model AS engineModel,
                    t1.engine_manufacturer_name AS engineManufacturerName,
                    t1.manufacturer_build_name AS manufacturerBuildName,
                    t1.vehicle_loads AS vehicleLoads,
                    t1.vin,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province
                <where>
                    t1.pt_date = #{ptDate} AND t2.province_adcode = #{provinceAdcode} AND t1.is_over_emission = 1
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
            </when>

            <!-- 传入省+市参数，查询对应市的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and cityAdcode != null and cityAdcode != '' and (districtAdcode == null or districtAdcode == '')">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    NULL AS districtNm,
                    NULL AS districtAdcode,
                    t1.epa_type AS epaType,
                    t1.vehicle_type AS vehicleType,
                    t1.engine_model AS engineModel,
                    t1.engine_manufacturer_name AS engineManufacturerName,
                    t1.manufacturer_build_name AS manufacturerBuildName,
                    t1.vehicle_loads AS vehicleLoads,
                    t1.vin,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode, city_nm, city_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city
                <where>
                    t1.pt_date = #{ptDate} AND t2.city_adcode = #{cityAdcode} AND t1.is_over_emission = 1
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
            </when>

            <!-- 传入省+市+区参数，查询对应区的数据 -->
            <when test="provinceAdcode != null and provinceAdcode != '' and cityAdcode != null and cityAdcode != '' and districtAdcode != null and districtAdcode != ''">
                SELECT
                    t2.province_nm AS provinceNm,
                    t2.province_adcode AS provinceAdcode,
                    t2.city_nm AS cityNm,
                    t2.city_adcode AS cityAdcode,
                    t2.district_nm AS districtNm,
                    t2.district_adcode AS districtAdcode,
                    t1.epa_type AS epaType,
                    t1.vehicle_type AS vehicleType,
                    t1.engine_model AS engineModel,
                    t1.engine_manufacturer_name AS engineManufacturerName,
                    t1.manufacturer_build_name AS manufacturerBuildName,
                    t1.vehicle_loads AS vehicleLoads,
                    t1.vin,
                    t1.pt_date AS ptDate
                FROM dw_ads.ads_daily_report_vehicle_drive_odometer t1
                INNER JOIN (SELECT DISTINCT province_nm, province_adcode, city_nm, city_adcode, district_nm, district_adcode FROM dw_dim.dim_area_info_extend_whole_country) t2
                ON t2.province_nm = t1.province AND t2.city_nm = t1.city AND t2.district_nm = t1.county
                <where>
                    t1.pt_date = #{ptDate} AND t2.district_adcode = #{districtAdcode} AND t1.is_over_emission = 1
                    <if test="epaType != null and epaType != ''">AND t1.epa_type = #{epaType}</if>
                    <if test="vehicleType != null and vehicleType != ''">AND t1.vehicle_type = #{vehicleType}</if>
                    <if test="engineModel != null and engineModel != ''">AND t1.engine_model = #{engineModel}</if>
                    <if test="engineManufacturerName != null and engineManufacturerName != ''">AND t1.engine_manufacturer_name = #{engineManufacturerName}</if>
                    <if test="manufacturerBuildName != null and manufacturerBuildName != ''">AND t1.manufacturer_build_name = #{manufacturerBuildName}</if>
                    <if test="vehicleLoads != null and vehicleLoads != ''">AND t1.vehicle_loads = #{vehicleLoads}</if>
                </where>
            </when>
        </choose>
    </select>

</mapper>