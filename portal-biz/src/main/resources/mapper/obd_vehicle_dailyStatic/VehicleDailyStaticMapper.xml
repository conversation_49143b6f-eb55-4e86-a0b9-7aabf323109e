<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.portal.obd_vehicle_dailyStatic.mapper.VehicleDailyStaticMapper">

    <!-- 定义AreaCity的resultMap -->
    <resultMap id="AreaCityResult" type="com.portal.obd_vehicle_dailyStatic.domain.AreaCity">
        <result property="adcode" column="adcode" />
        <result property="name" column="name" />
        <result property="center" column="center" />
        <result property="geometry" column="geometry" javaType="Object" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 获取所有有数据的省份 -->
    <select id="getProvinceList" resultMap="AreaCityResult">
        select adcode,
	           name,
	           center,
	          `geometry` 
        from dw_dim.dim_area_info_whole_country
        where `level` = 'province'
    </select>

    <!-- 根据父级ID获取区域列表（城市或区县） -->
    <select id="getAreaListByPid" resultMap="AreaCityResult">
        select adcode,
               name,
               center,
               `geometry` 
        from dw_dim.dim_area_info_whole_country
        where parent_adcode = '${parentAdcode}'
    </select>
    
    <!-- 按燃料类型统计车辆数据 -->
    <select id="getVehicleStatsByFuelType" resultType="com.portal.obd_vehicle_dailyStatic.domain.VehicleDailyStatic"
            parameterType="com.portal.obd_vehicle_dailyStatic.dto.VehicleDailyStaticQueryParam">
        WITH vehicle_stats AS (
            SELECT 
                epa_vehicle_type AS type,
                COUNT(DISTINCT vin) AS type_count, 
                pt_date
                <if test="province != null and province != ''">
                    , province
                </if>
                <if test="city != null and city != ''">
                    , city
                </if>
                <if test="county != null and county != ''">
                    , county
                </if>
            FROM dw_dwm.dwm_online_vehicle_pageshow
            WHERE pt_date = #{ptDate}
              AND epa_vehicle_type != '未知类型' 
              AND actual_agreement != '未知协议'
              <if test="province != null and province != ''">
                  AND province = #{province}
              </if>
              <if test="city != null and city != ''">
                  AND city = #{city}
              </if>
              <if test="county != null and county != ''">
                  AND county = #{county}
              </if>
            GROUP BY epa_vehicle_type, pt_date
            <if test="province != null and province != ''">
                , province
            </if>
            <if test="city != null and city != ''">
                , city
            </if>
            <if test="county != null and county != ''">
                , county
            </if>
        ),
        total_vehicles AS (
            SELECT 
                COUNT(DISTINCT vin) AS total_count,
                pt_date
                <if test="province != null and province != ''">
                    , province
                </if>
                <if test="city != null and city != ''">
                    , city
                </if>
                <if test="county != null and county != ''">
                    , county
                </if>
            FROM dw_dwm.dwm_online_vehicle_pageshow
            WHERE pt_date = #{ptDate}
              AND epa_vehicle_type != '未知类型' 
              AND actual_agreement != '未知协议'
              <if test="province != null and province != ''">
                  AND province = #{province}
              </if>
              <if test="city != null and city != ''">
                  AND city = #{city}
              </if>
              <if test="county != null and county != ''">
                  AND county = #{county}
              </if>
            GROUP BY pt_date
            <if test="province != null and province != ''">
                , province
            </if>
            <if test="city != null and city != ''">
                , city
            </if>
            <if test="county != null and county != ''">
                , county
            </if>
        )
        SELECT 
            v.type,
            v.type_count AS typeCount,
            t.total_count AS totalCount,
            v.pt_date AS ptDate
            <if test="province != null and province != ''">
                , v.province
            </if>
            <if test="city != null and city != ''">
                , v.city
            </if>
            <if test="county != null and county != ''">
                , v.county
            </if>
        FROM vehicle_stats v
        INNER JOIN total_vehicles t ON t.pt_date = v.pt_date
        <if test="province != null and province != ''">
            AND t.province = v.province
        </if>
        <if test="city != null and city != ''">
            AND t.city = v.city
        </if>
        <if test="county != null and county != ''">
            AND t.county = v.county
        </if>
        ORDER BY 
        <choose>
            <when test="orderByColumn != null and orderByColumn != ''">
                ${orderByColumn} <if test="isAsc == 'asc'">ASC</if><if test="isAsc != 'asc'">DESC</if>
            </when>
            <otherwise>
                v.type_count DESC
            </otherwise>
        </choose>
    </select>
    
    <!-- 按车辆协议统计车辆数据 -->
    <select id="getVehicleStatsByAgreement" resultType="com.portal.obd_vehicle_dailyStatic.domain.VehicleDailyStatic"
            parameterType="com.portal.obd_vehicle_dailyStatic.dto.VehicleDailyStaticQueryParam">
        WITH vehicle_stats AS (
            SELECT 
                actual_agreement AS type,
                COUNT(DISTINCT vin) AS type_count,
                pt_date
                <if test="province != null and province != ''">
                    , province
                </if>
                <if test="city != null and city != ''">
                    , city
                </if>
                <if test="county != null and county != ''">
                    , county
                </if>
            FROM dw_dwm.dwm_online_vehicle_pageshow
            WHERE pt_date = #{ptDate}
              AND epa_vehicle_type != '未知类型' 
              AND actual_agreement != '未知协议'
              <if test="province != null and province != ''">
                  AND province = #{province}
              </if>
              <if test="city != null and city != ''">
                  AND city = #{city}
              </if>
              <if test="county != null and county != ''">
                  AND county = #{county}
              </if>
            GROUP BY actual_agreement, pt_date
            <if test="province != null and province != ''">
                , province
            </if>
            <if test="city != null and city != ''">
                , city
            </if>
            <if test="county != null and county != ''">
                , county
            </if>
        ),
        total_vehicles AS (
            SELECT 
                COUNT(DISTINCT vin) AS total_count,
                pt_date
                <if test="province != null and province != ''">
                    , province
                </if>
                <if test="city != null and city != ''">
                    , city
                </if>
                <if test="county != null and county != ''">
                    , county
                </if>
            FROM dw_dwm.dwm_online_vehicle_pageshow
            WHERE pt_date = #{ptDate}
              AND epa_vehicle_type != '未知类型' 
              AND actual_agreement != '未知协议'
              <if test="province != null and province != ''">
                  AND province = #{province}
              </if>
              <if test="city != null and city != ''">
                  AND city = #{city}
              </if>
              <if test="county != null and county != ''">
                  AND county = #{county}
              </if>
            GROUP BY pt_date
            <if test="province != null and province != ''">
                , province
            </if>
            <if test="city != null and city != ''">
                , city
            </if>
            <if test="county != null and county != ''">
                , county
            </if>
        )
        SELECT 
            v.type,
            v.type_count AS typeCount,
            t.total_count AS totalCount,
            v.pt_date AS ptDate
            <if test="province != null and province != ''">
                , v.province
            </if>
            <if test="city != null and city != ''">
                , v.city
            </if>
            <if test="county != null and county != ''">
                , v.county
            </if>
        FROM vehicle_stats v
        INNER JOIN total_vehicles t ON t.pt_date = v.pt_date
        <if test="province != null and province != ''">
            AND t.province = v.province
        </if>
        <if test="city != null and city != ''">
            AND t.city = v.city
        </if>
        <if test="county != null and county != ''">
            AND t.county = v.county
        </if>
        ORDER BY 
        <choose>
            <when test="orderByColumn != null and orderByColumn != ''">
                ${orderByColumn} <if test="isAsc == 'asc'">ASC</if><if test="isAsc != 'asc'">DESC</if>
            </when>
            <otherwise>
                v.type_count DESC
            </otherwise>
        </choose>
    </select>

</mapper> 