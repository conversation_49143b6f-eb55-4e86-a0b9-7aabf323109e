<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.portal.area_city_query.mapper.AreaInfoMapper">

    <!-- 获取所有省份列表 -->
    <select id="getAllProvinces" resultType="com.portal.area_city_query.domain.AreaInfo">
        SELECT DISTINCT 
               province_adcode AS code,
               province_nm AS name
        FROM dw_dim.dim_area_info_extend_whole_country
        WHERE province_nm IS NOT NULL
        ORDER BY province_nm
    </select>

    <!-- 根据省份编码获取下属城市列表 -->
    <select id="getCitiesByProvinceCode" resultType="com.portal.area_city_query.domain.AreaInfo">
        SELECT DISTINCT 
               city_adcode AS code,
               city_nm AS name
        FROM dw_dim.dim_area_info_extend_whole_country
        WHERE province_adcode = #{provinceCode}
          AND city_nm IS NOT NULL
        ORDER BY city_nm
    </select>

    <!-- 根据城市编码获取下属区县列表 -->
    <select id="getDistrictsByCityCode" resultType="com.portal.area_city_query.domain.AreaInfo">
        SELECT DISTINCT 
               district_adcode AS code,
               district_nm AS name
        FROM dw_dim.dim_area_info_extend_whole_country
        WHERE city_adcode = #{cityCode}
          AND district_nm IS NOT NULL
        ORDER BY district_nm
    </select>

</mapper> 