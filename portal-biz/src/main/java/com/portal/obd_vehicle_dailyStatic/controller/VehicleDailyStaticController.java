package com.portal.obd_vehicle_dailyStatic.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.portal.common.annotation.Log;
import com.portal.common.core.controller.BaseController;
import com.portal.common.core.domain.AjaxResult;
import com.portal.common.core.page.TableDataInfo;
import com.portal.common.enums.BusinessType;
import com.portal.common.utils.poi.ExcelUtil;
import com.portal.obd_vehicle_dailyStatic.domain.AreaCity;
import com.portal.obd_vehicle_dailyStatic.domain.VehicleDailyStatic;
import com.portal.obd_vehicle_dailyStatic.dto.VehicleDailyStaticQueryParam;
import com.portal.obd_vehicle_dailyStatic.service.IVehicleDailyStaticService;

/**
 * 车辆每日统计控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/daily_static")
public class VehicleDailyStaticController extends BaseController {
    
    @Autowired
    private IVehicleDailyStaticService vehicleDailyStaticService;
    
    /**
     * 获取所有有数据的省份
     */
    @PreAuthorize("@ss.hasPermi('daily_static:province_list')")
    @GetMapping("/province_list")
    public AjaxResult getProvinceList() {
        List<AreaCity> list = vehicleDailyStaticService.getProvinceList();
        // 处理geometry字段，将字符串转换为JSON对象，并设置type字段
        for (AreaCity areaCity : list) {
            processGeometryField(areaCity);
            areaCity.setType("province");
        }
        return AjaxResult.success(list);
    }
    
    /**
     * 根据父级编码获取区域列表（城市或区县）
     */
    @PreAuthorize("@ss.hasPermi('daily_static:area_list')")
    @GetMapping("/area_list/{parentAdcode}")
    public AjaxResult getAreaListByPid(@PathVariable("parentAdcode") String parentAdcode) {
        List<AreaCity> list = vehicleDailyStaticService.getAreaListByPid(parentAdcode);
        // 处理geometry字段，将字符串转换为JSON对象
        for (AreaCity areaCity : list) {
            processGeometryField(areaCity);
            areaCity.setType("area");
        }
        return AjaxResult.success(list);
    }
    
    /**
     * 处理geometry字段，将JSON字符串转换为JSON对象
     */
    private void processGeometryField(AreaCity areaCity) {
        try {
            if (areaCity.getGeometry() instanceof String) {
                String geometryStr = (String) areaCity.getGeometry();
                if (geometryStr != null && !geometryStr.isEmpty()) {
                    // 解析JSON字符串为JSONObject对象
                    JSONObject jsonObject = JSON.parseObject(geometryStr);
                    // 将JSONObject设置回geometry字段
                    areaCity.setGeometry(jsonObject);
                }
            }
        } catch (Exception e) {
            logger.error("处理geometry字段失败", e);
        }
    }
    
    /**
     * 按燃料类型统计车辆数据（分页查询）
     */
    @PreAuthorize("@ss.hasPermi('daily_static:fuel_type_stats')")
    @GetMapping("/fuel_type_stats")
    public TableDataInfo getVehicleStatsByFuelType(VehicleDailyStaticQueryParam queryParam) {
        startPage();
        List<VehicleDailyStatic> list = vehicleDailyStaticService.getVehicleStatsByFuelType(queryParam);
        return getDataTable(list);
    }
    
    /**
     * 导出按燃料类型统计的车辆数据
     */
    @PreAuthorize("@ss.hasPermi('daily_static:fuel_type_stats:export')")
    @Log(title = "燃料类型车辆统计数据", businessType = BusinessType.EXPORT)
    @PostMapping("/fuel_type_stats/export")
    public void exportVehicleStatsByFuelType(HttpServletResponse response, @RequestBody(required = false) VehicleDailyStaticQueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new VehicleDailyStaticQueryParam();
        }
        
        List<VehicleDailyStatic> list;
        
        // 判断是否导出当前页
        if (Boolean.TRUE.equals(queryParam.getExportCurrentPage())) {
            // 使用前端传递的分页参数
            Integer pageNum = queryParam.getPageNum() != null ? queryParam.getPageNum() : 1;
            Integer pageSize = queryParam.getPageSize() != null ? queryParam.getPageSize() : 10;
            
            PageHelper.startPage(pageNum, pageSize);
            
            list = vehicleDailyStaticService.getVehicleStatsByFuelType(queryParam);
            // 获取当前页数据
            list = new PageInfo<>(list).getList();
        } else {
            // 导出全部数据
            list = vehicleDailyStaticService.getVehicleStatsByFuelType(queryParam);
        }
        
        ExcelUtil<VehicleDailyStatic> util = new ExcelUtil<>(VehicleDailyStatic.class);
        util.exportExcel(response, list, "燃料类型车辆统计数据");
    }
    
    /**
     * 按车辆协议统计车辆数据（分页查询）
     */
    @PreAuthorize("@ss.hasPermi('daily_static:agreement_stats')")
    @GetMapping("/agreement_stats")
    public TableDataInfo getVehicleStatsByAgreement(VehicleDailyStaticQueryParam queryParam) {
        startPage();
        List<VehicleDailyStatic> list = vehicleDailyStaticService.getVehicleStatsByAgreement(queryParam);
        return getDataTable(list);
    }
    
    /**
     * 导出按车辆协议统计的车辆数据
     */
    @PreAuthorize("@ss.hasPermi('daily_static:agreement_stats:export')")
    @Log(title = "车辆协议统计数据", businessType = BusinessType.EXPORT)
    @PostMapping("/agreement_stats/export")
    public void exportVehicleStatsByAgreement(HttpServletResponse response, @RequestBody(required = false) VehicleDailyStaticQueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new VehicleDailyStaticQueryParam();
        }
        
        List<VehicleDailyStatic> list;
        
        // 判断是否导出当前页
        if (Boolean.TRUE.equals(queryParam.getExportCurrentPage())) {
            // 使用前端传递的分页参数
            Integer pageNum = queryParam.getPageNum() != null ? queryParam.getPageNum() : 1;
            Integer pageSize = queryParam.getPageSize() != null ? queryParam.getPageSize() : 10;
            
            PageHelper.startPage(pageNum, pageSize);
            
            list = vehicleDailyStaticService.getVehicleStatsByAgreement(queryParam);
            // 获取当前页数据
            list = new PageInfo<>(list).getList();
        } else {
            // 导出全部数据
            list = vehicleDailyStaticService.getVehicleStatsByAgreement(queryParam);
        }
        
        ExcelUtil<VehicleDailyStatic> util = new ExcelUtil<>(VehicleDailyStatic.class);
        util.exportExcel(response, list, "车辆协议统计数据");
    }
} 