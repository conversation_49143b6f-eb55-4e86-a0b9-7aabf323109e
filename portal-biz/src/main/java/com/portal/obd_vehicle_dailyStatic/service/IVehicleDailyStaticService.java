package com.portal.obd_vehicle_dailyStatic.service;

import java.util.List;

import com.portal.obd_vehicle_dailyStatic.domain.AreaCity;
import com.portal.obd_vehicle_dailyStatic.domain.VehicleDailyStatic;
import com.portal.obd_vehicle_dailyStatic.dto.VehicleDailyStaticQueryParam;

/**
 * 车辆每日统计服务接口
 * 
 * <AUTHOR>
 */
public interface IVehicleDailyStaticService {
    
    /**
     * 获取所有有数据的省份
     * 
     * @return 省份列表
     */
    List<AreaCity> getProvinceList();
    
    /**
     * 根据父级编码获取区域列表（城市或区县）
     * 
     * @param parentAdcode 父级区域编码
     * @return 区域列表
     */
    List<AreaCity> getAreaListByPid(String parentAdcode);
    
    /**
     * 按燃料类型统计车辆数据
     * 
     * @param queryParam 查询参数
     * @return 燃料类型统计数据列表
     */
    List<VehicleDailyStatic> getVehicleStatsByFuelType(VehicleDailyStaticQueryParam queryParam);
    
    /**
     * 按车辆协议统计车辆数据
     * 
     * @param queryParam 查询参数
     * @return 车辆协议统计数据列表
     */
    List<VehicleDailyStatic> getVehicleStatsByAgreement(VehicleDailyStaticQueryParam queryParam);
} 