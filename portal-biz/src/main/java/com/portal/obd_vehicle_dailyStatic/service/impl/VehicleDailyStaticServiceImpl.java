package com.portal.obd_vehicle_dailyStatic.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.portal.common.annotation.DataSource;
import com.portal.common.enums.DataSourceType;
import com.portal.obd_vehicle_dailyStatic.domain.AreaCity;
import com.portal.obd_vehicle_dailyStatic.domain.VehicleDailyStatic;
import com.portal.obd_vehicle_dailyStatic.dto.VehicleDailyStaticQueryParam;
import com.portal.obd_vehicle_dailyStatic.mapper.VehicleDailyStaticMapper;
import com.portal.obd_vehicle_dailyStatic.service.IVehicleDailyStaticService;

/**
 * 车辆每日统计服务实现
 * 
 * <AUTHOR>
 */
@Service
public class VehicleDailyStaticServiceImpl implements IVehicleDailyStaticService {

    @Autowired
    private VehicleDailyStaticMapper vehicleDailyStaticMapper;

    /**
     * 获取所有有数据的省份
     * 
     * @return 省份列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<AreaCity> getProvinceList() {
        return vehicleDailyStaticMapper.getProvinceList();
    }
    
    /**
     * 根据父级编码获取区域列表（城市或区县）
     * 
     * @param parentAdcode 父级区域编码
     * @return 区域列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<AreaCity> getAreaListByPid(String parentAdcode) {
        return vehicleDailyStaticMapper.getAreaListByPid(parentAdcode);
    }
    
    /**
     * 按燃料类型统计车辆数据
     * 
     * @param queryParam 查询参数
     * @return 燃料类型统计数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleDailyStatic> getVehicleStatsByFuelType(VehicleDailyStaticQueryParam queryParam) {
        return vehicleDailyStaticMapper.getVehicleStatsByFuelType(queryParam);
    }
    
    /**
     * 按车辆协议统计车辆数据
     * 
     * @param queryParam 查询参数
     * @return 车辆协议统计数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleDailyStatic> getVehicleStatsByAgreement(VehicleDailyStaticQueryParam queryParam) {
        return vehicleDailyStaticMapper.getVehicleStatsByAgreement(queryParam);
    }
} 