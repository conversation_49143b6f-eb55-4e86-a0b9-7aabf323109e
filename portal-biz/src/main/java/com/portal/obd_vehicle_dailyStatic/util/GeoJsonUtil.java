package com.portal.obd_vehicle_dailyStatic.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * GeoJSON工具类
 * 
 * <AUTHOR>
 */
public class GeoJsonUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(GeoJsonUtil.class);
    
    /**
     * 将GeoJSON字符串转换为JSONObject对象
     * 
     * @param geoJsonString GeoJSON字符串
     * @return JSONObject对象，如果解析失败则返回null
     */
    public static JSONObject parseGeoJson(String geoJsonString) {
        if (geoJsonString == null || geoJsonString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return JSON.parseObject(geoJsonString);
        } catch (Exception e) {
            logger.error("解析GeoJSON失败: {}", geoJsonString, e);
            return null;
        }
    }
    
    /**
     * 将JSONObject对象转换为GeoJSON字符串
     * 
     * @param geoJson JSONObject对象
     * @return GeoJSON字符串，如果转换失败则返回null
     */
    public static String toGeoJsonString(JSONObject geoJson) {
        if (geoJson == null) {
            return null;
        }
        
        try {
            return JSON.toJSONString(geoJson);
        } catch (Exception e) {
            logger.error("转换GeoJSON失败", e);
            return null;
        }
    }
} 