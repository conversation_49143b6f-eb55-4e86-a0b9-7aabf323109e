package com.portal.obd_vehicle_dailyStatic.domain;

/**
 * 区域城市信息
 * 
 * <AUTHOR>
 */
public class AreaCity {
    private String adcode;    // 区域编码
    private String name;      // 区域名称
    private String center;    // 区域中心点坐标
    private Object geometry;  // 区域几何形状
    private String type;      // 区域类型（province/area）
    
    public String getAdcode() {
        return adcode;
    }

    public void setAdcode(String adcode) {
        this.adcode = adcode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    
    public String getCenter() {
        return center;
    }
    
    public void setCenter(String center) {
        this.center = center;
    }
    
    public Object getGeometry() {
        return geometry;
    }
    
    public void setGeometry(Object geometry) {
        this.geometry = geometry;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    // 为了兼容旧代码，保留getId方法
    public String getId() {
        return adcode;
    }
    
    // 为了兼容旧代码，保留setId方法
    public void setId(String id) {
        this.adcode = id;
    }

    @Override
    public String toString() {
        return "AreaCity{" +
                "adcode='" + adcode + '\'' +
                ", name='" + name + '\'' +
                ", center='" + center + '\'' +
                ", geometry=" + geometry +
                ", type='" + type + '\'' +
                '}';
    }
} 