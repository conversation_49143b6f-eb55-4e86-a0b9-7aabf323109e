package com.portal.obd_vehicle_dailyStatic.domain;

/**
 * 车辆每日统计数据
 * 
 * <AUTHOR>
 */
public class VehicleDailyStatic {
    
    private String type;              // 类型（车辆协议类型或燃料类型）
    private Integer typeCount;        // 该类型的车辆数量
    private Integer totalCount;       // 总车辆数量
    private String ptDate;            // 统计日期
    private String province;          // 省份
    private String city;              // 城市
    private String county;            // 区县
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public Integer getTypeCount() {
        return typeCount;
    }
    
    public void setTypeCount(Integer typeCount) {
        this.typeCount = typeCount;
    }
    
    public Integer getTotalCount() {
        return totalCount;
    }
    
    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }
    
    public String getPtDate() {
        return ptDate;
    }
    
    public void setPtDate(String ptDate) {
        this.ptDate = ptDate;
    }
    
    public String getProvince() {
        return province;
    }
    
    public void setProvince(String province) {
        this.province = province;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getCounty() {
        return county;
    }
    
    public void setCounty(String county) {
        this.county = county;
    }
    
    @Override
    public String toString() {
        return "VehicleDailyStatic{" +
                "type='" + type + '\'' +
                ", typeCount=" + typeCount +
                ", totalCount=" + totalCount +
                ", ptDate='" + ptDate + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", county='" + county + '\'' +
                '}';
    }
} 