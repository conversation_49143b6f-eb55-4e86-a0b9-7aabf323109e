package com.portal.obd_vehicle_dailyStatic.dto;

/**
 * 车辆每日统计查询参数
 * 
 * <AUTHOR>
 */
public class VehicleDailyStaticQueryParam {
    private String ptDate;                  // 统计日期，必填
    private String province;                // 省份，选填
    private String city;                    // 城市，选填
    private String county;                  // 区县，选填
    private Boolean exportCurrentPage;      // 是否导出当前页，选填
    private Integer pageNum;                // 当前页码
    private Integer pageSize;               // 每页记录数
    private String orderByColumn;           // 排序列
    private String isAsc;                   // 排序方向（desc或asc）
    
    public String getPtDate() {
        return ptDate;
    }

    public void setPtDate(String ptDate) {
        this.ptDate = ptDate;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }
    
    public Boolean getExportCurrentPage() {
        return exportCurrentPage;
    }

    public void setExportCurrentPage(Boolean exportCurrentPage) {
        this.exportCurrentPage = exportCurrentPage;
    }
    
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    
    public String getOrderByColumn() {
        return orderByColumn;
    }

    public void setOrderByColumn(String orderByColumn) {
        this.orderByColumn = orderByColumn;
    }

    public String getIsAsc() {
        return isAsc;
    }

    public void setIsAsc(String isAsc) {
        this.isAsc = isAsc;
    }

    @Override
    public String toString() {
        return "VehicleDailyStaticQueryParam{" +
                "ptDate='" + ptDate + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", county='" + county + '\'' +
                ", exportCurrentPage=" + exportCurrentPage +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", orderByColumn='" + orderByColumn + '\'' +
                ", isAsc='" + isAsc + '\'' +
                '}';
    }
} 