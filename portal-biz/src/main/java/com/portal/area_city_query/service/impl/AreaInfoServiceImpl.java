package com.portal.area_city_query.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.portal.area_city_query.domain.AreaInfo;
import com.portal.area_city_query.mapper.AreaInfoMapper;
import com.portal.area_city_query.service.IAreaInfoService;
import com.portal.common.annotation.DataSource;
import com.portal.common.enums.DataSourceType;

/**
 * 区域信息服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class AreaInfoServiceImpl implements IAreaInfoService {
    @Autowired
    private AreaInfoMapper areaInfoMapper;

    /**
     * 获取所有省份列表
     * 
     * @return 省份列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<AreaInfo> getAllProvinces() {
        return areaInfoMapper.getAllProvinces();
    }

    /**
     * 根据省份编码获取下属城市列表
     * 
     * @param provinceCode 省份编码
     * @return 城市列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<AreaInfo> getCitiesByProvinceCode(String provinceCode) {
        return areaInfoMapper.getCitiesByProvinceCode(provinceCode);
    }

    /**
     * 根据城市编码获取下属区县列表
     * 
     * @param cityCode 城市编码
     * @return 区县列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<AreaInfo> getDistrictsByCityCode(String cityCode) {
        return areaInfoMapper.getDistrictsByCityCode(cityCode);
    }
} 