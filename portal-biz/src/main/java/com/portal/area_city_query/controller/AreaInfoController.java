package com.portal.area_city_query.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.portal.area_city_query.domain.AreaInfo;
import com.portal.area_city_query.service.IAreaInfoService;
import com.portal.common.core.controller.BaseController;
import com.portal.common.core.domain.AjaxResult;

/**
 * 区域信息控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/area")
public class AreaInfoController extends BaseController {
    @Autowired
    private IAreaInfoService areaInfoService;
    
    /**
     * 获取区域列表
     * 
     * @param type 区域类型：province-省份，city-城市，district-区县
     * @param parentCode 父级区域编码（获取省份时不需要传）
     * @return 区域列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:list')")
    @GetMapping("/list")
    public AjaxResult getAreaList(@RequestParam(value = "type", defaultValue = "province") String type,
                                  @RequestParam(value = "parentCode", required = false) String parentCode) {
        List<AreaInfo> list;
        
        switch (type) {
            case "city":
                if (parentCode == null || parentCode.isEmpty()) {
                    return AjaxResult.error("获取城市列表时必须提供省份编码");
                }
                list = areaInfoService.getCitiesByProvinceCode(parentCode);
                break;
            case "district":
                if (parentCode == null || parentCode.isEmpty()) {
                    return AjaxResult.error("获取区县列表时必须提供城市编码");
                }
                list = areaInfoService.getDistrictsByCityCode(parentCode);
                break;
            case "province":
            default:
                list = areaInfoService.getAllProvinces();
                break;
        }
        
        return AjaxResult.success(list);
    }
    
    /**
     * 获取所有省份列表
     * 
     * @return 省份列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:province_list')")
    @GetMapping("/province_list")
    public AjaxResult getProvinceList() {
        List<AreaInfo> list = areaInfoService.getAllProvinces();
        return AjaxResult.success(list);
    }
    
    /**
     * 根据省份编码获取城市列表
     * 
     * @param provinceCode 省份编码
     * @return 城市列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:city_list')")
    @GetMapping("/city_list/{provinceCode}")
    public AjaxResult getCityList(@PathVariable("provinceCode") String provinceCode) {
        List<AreaInfo> list = areaInfoService.getCitiesByProvinceCode(provinceCode);
        return AjaxResult.success(list);
    }
    
    /**
     * 根据城市编码获取区县列表
     * 
     * @param cityCode 城市编码
     * @return 区县列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:district_list')")
    @GetMapping("/district_list/{cityCode}")
    public AjaxResult getDistrictList(@PathVariable("cityCode") String cityCode) {
        List<AreaInfo> list = areaInfoService.getDistrictsByCityCode(cityCode);
        return AjaxResult.success(list);
    }
} 