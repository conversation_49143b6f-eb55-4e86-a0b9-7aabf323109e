package com.portal.area_city_query.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.portal.area_city_query.domain.AreaInfo;

/**
 * 区域信息数据层
 * 
 * <AUTHOR>
 */
@Mapper
public interface AreaInfoMapper {
    /**
     * 获取所有省份列表
     */
    List<AreaInfo> getAllProvinces();
    
    /**
     * 根据省份编码获取下属城市列表
     * 
     * @param provinceCode 省份编码
     * @return 城市列表
     */
    List<AreaInfo> getCitiesByProvinceCode(@Param("provinceCode") String provinceCode);
    
    /**
     * 根据城市编码获取下属区县列表
     * 
     * @param cityCode 城市编码
     * @return 区县列表
     */
    List<AreaInfo> getDistrictsByCityCode(@Param("cityCode") String cityCode);
} 