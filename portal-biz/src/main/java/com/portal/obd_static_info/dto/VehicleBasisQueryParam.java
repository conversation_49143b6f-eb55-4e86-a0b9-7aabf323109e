package com.portal.obd_static_info.dto;

import java.util.Map;
import java.util.HashMap;

/**
 * 车辆基础信息查询参数
 * 
 * <AUTHOR>
 */
public class VehicleBasisQueryParam {
    /** 车辆vin号 */
    private String vin;
    
    /** 车牌号 */
    private String vehicleLicense;
    
    /** 燃料类型 */
    private String epaVehicleType;
    
    /** 车辆类型 */
    private String vehicleType;
    
    /** 发动机型号 */
    private String engineModel;
    
    /** 发动机生产厂家 */
    private String engineManufacturerName;
    
    /** 车辆生产企业名称 */
    private String manufacturerBuildName;
    
    /** 总质量 */
    private String vehicleLoads;
    
    /** 排放阶段 */
    private String engineEmissionLeveltype;
    
    /** 车辆联网状态 */
    private String connectStatus;
    
    /** 车籍地 */
    private String vehicleDomicile;
    
    /** 首次上线状态 */
    private String firstOnlineStatus;
    
    /** 车辆同步状态 */
    private String syncStatus;
    
    /** 开始时间 */
    private String beginTime;
    
    /** 结束时间 */
    private String endTime;
    
    /** 动态查询参数，用于支持通用查询 */
    private Map<String, Object> params;
    
    public VehicleBasisQueryParam() {
        params = new HashMap<>();
    }
    
    public Map<String, Object> getParams() {
        return params;
    }
    
    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
    
    public void addParam(String key, Object value) {
        if (this.params == null) {
            this.params = new HashMap<>();
        }
        this.params.put(key, value);
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getVehicleLicense() {
        return vehicleLicense;
    }

    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense;
    }

    public String getEpaVehicleType() {
        return epaVehicleType;
    }

    public void setEpaVehicleType(String epaVehicleType) {
        this.epaVehicleType = epaVehicleType;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel;
    }

    public String getEngineManufacturerName() {
        return engineManufacturerName;
    }

    public void setEngineManufacturerName(String engineManufacturerName) {
        this.engineManufacturerName = engineManufacturerName;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getVehicleLoads() {
        return vehicleLoads;
    }

    public void setVehicleLoads(String vehicleLoads) {
        this.vehicleLoads = vehicleLoads;
    }

    public String getEngineEmissionLeveltype() {
        return engineEmissionLeveltype;
    }

    public void setEngineEmissionLeveltype(String engineEmissionLeveltype) {
        this.engineEmissionLeveltype = engineEmissionLeveltype;
    }

    public String getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(String connectStatus) {
        this.connectStatus = connectStatus;
    }

    public String getVehicleDomicile() {
        return vehicleDomicile;
    }

    public void setVehicleDomicile(String vehicleDomicile) {
        this.vehicleDomicile = vehicleDomicile;
    }

    public String getFirstOnlineStatus() {
        return firstOnlineStatus;
    }

    public void setFirstOnlineStatus(String firstOnlineStatus) {
        this.firstOnlineStatus = firstOnlineStatus;
    }

    public String getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
} 