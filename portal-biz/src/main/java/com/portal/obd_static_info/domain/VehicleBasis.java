package com.portal.obd_static_info.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.portal.common.annotation.Excel;
import com.portal.common.core.domain.BaseEntity;

/**
 * 车辆基础信息对象 sys_vehicle_basis_sync
 * 
 * <AUTHOR>
 */
public class VehicleBasis extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 车辆vin号 */
    @Excel(name = "车辆vin号")
    private String vin;

    /** 信息公开编号 */
    @Excel(name = "信息公开编号")
    private String enpInfoPublicNum;

    /** 燃料类型(1:柴油车 2:燃气车 3:双燃料汽车 4:油电混合动力 5:气电混合动力 6:甲醇燃料汽车) */
    @Excel(name = "燃料类型", readConverterExp = "1=柴油车,2=燃气车,3=双燃料汽车,4=油电混合动力,5=气电混合动力,6=甲醇燃料汽车")
    private String epaVehicleType;

    /** 车辆分类(M1、M2、M3、N1、N2、N3) */
    @Excel(name = "车辆分类")
    private String vehCatalogue;

    /** veh_catalogue的详细信息，N2(非城市车辆) */
    @Excel(name = "车辆分类详细信息")
    private String vehCatalogueDetail;

    /** 排放阶段(1:国六 2:国五) */
    @Excel(name = "排放阶段", readConverterExp = "1=国六,2=国五")
    private String engineEmissionLeveltype;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String vehicleLicense;

    /** 车籍地 */
    @Excel(name = "车籍地")
    private String vehicleDomicile;

    /** 备案协议类型(1:GB17691（国六除HJ之外）2:HJ征求意见稿 3:HJ1239-2021 4:国五) */
    @Excel(name = "备案协议类型", readConverterExp = "1=GB17691（国六除HJ之外）,2=HJ征求意见稿,3=HJ1239-2021,4=国五")
    private String registerAgreement;

    /** 激活模式(0:null,无需激活 1:需激活) */
    @Excel(name = "激活模式", readConverterExp = "0=无需激活,1=需激活")
    private String vehRegisterMode;

    /** 车辆备案企业名称 */
    @Excel(name = "车辆备案企业名称")
    private String companyName;

    /** 企业平台名称:地方/企业 */
    @Excel(name = "企业平台名称")
    private String platformName;

    /** 平台账号名称 */
    @Excel(name = "平台账号名称")
    private String platformAccountName;

    /** 平台建设单位 */
    @Excel(name = "平台建设单位")
    private String constructionUnit;

    /** 平台建设方式(1:自建（其它全按自建算）2:托管 3:第三方托管) */
    @Excel(name = "平台建设方式", readConverterExp = "1=自建,2=托管,3=第三方托管")
    private String platformMode;

    /** 车辆生产企业名称 */
    @Excel(name = "车辆生产企业名称")
    private String manufacturerBuildName;

    /** 静态备案时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "静态备案时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String registerTime;

    /** 车辆同步状态(0:未同步 1:已同步 2:同步失败) */
    @Excel(name = "车辆同步状态", readConverterExp = "0=未同步,1=已同步,2=同步失败")
    private String syncStatus;

    /** 车辆同步时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "车辆同步时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String syncTime;

    /** 终端激活状态(0:激活成功 1:芯片ID错误 2:芯片已备案 3:密钥错误 4:VIN错误 5.不需要激活) */
    @Excel(name = "终端激活状态", readConverterExp = "0=激活成功,1=芯片ID错误,2=芯片已备案,3=密钥错误,4=VIN错误,5=不需要激活")
    private String activationStatus;

    /** 终端激活时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "终端激活时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String activationTime;

    /** 首次上线时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "首次上线时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String firstOnlineTime;

    /** 首次上线状态(0:未上线 1:已上线) */
    @Excel(name = "首次上线状态", readConverterExp = "0=未上线,1=已上线")
    private String firstOnlineStatus;

    /** 车辆联网状态(车辆同步状态为1且首次上线状态为1则车辆已联网，否则为未联网 1:已联网 2:未联网) */
    @Excel(name = "车辆联网状态", readConverterExp = "1=已联网,2=未联网")
    private String connectStatus;

    /** 车辆联网时间(首次上线时间和车辆同步时间的较大值（时间靠后）为车辆联网时间，时间格式：%Y%m%d%H%M%S) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "车辆联网时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String connectTime;

    /** 车辆型号 */
    @Excel(name = "车辆型号")
    private String vehicleModel;

    /** 车辆类型(车型匹配的第一个数字代表车辆类别 1代表载货汽车 2代表越野汽车 3代表自卸汽车 4代表牵引汽车 5代表专用汽车 6代表客车 7代表轿车 8代表半挂车以及专用半挂车) */
    @Excel(name = "车辆类型", readConverterExp = "1=载货汽车,2=越野汽车,3=自卸汽车,4=牵引汽车,5=专用汽车,6=客车,7=轿车,8=半挂车及专用半挂车")
    private String vehicleType;

    /** 总质量(载货汽车、越野汽车、自卸汽车、牵引汽车、专用汽车及半挂车的车型匹配的第二、三个数字代表车辆类别，如16就是16吨 */
    @Excel(name = "总质量")
    private String vehicleLoads;

    /** 最大载重 */
    @Excel(name = "最大载重")
    private String maxLoads;

    /** 发动机型号 */
    @Excel(name = "发动机型号")
    private String engineModel;

    /** 发动机生产厂家 */
    @Excel(name = "发动机生产厂家")
    private String engineManufacturerName;

    /** 终端型号 */
    @Excel(name = "终端型号")
    private String tboxModel;

    /** 终端生产厂家 */
    @Excel(name = "终端生产厂家")
    private String tboxManufacturerName;

    /** 三位标识符 */
    @Excel(name = "三位标识符")
    private String chipPrefix;

    /** 芯片型号 */
    @Excel(name = "芯片型号")
    private String chipModel;

    /** 芯片制造企业 */
    @Excel(name = "芯片制造企业")
    private String chipManufacturerName;

    /** 生产日期(国产)/进口日期(国产) */
    @Excel(name = "生产/进口日期")
    private String manufactureDate;

    /** 国别 */
    @Excel(name = "国别")
    private String country;

    /** 最大净功率 */
    @Excel(name = "最大净功率")
    private String maximumPower;

    /** 最大净功率转速 */
    @Excel(name = "最大净功率转速")
    private String maximumEngineRotationlSpeed;

    /** 额定功率 */
    @Excel(name = "额定功率")
    private String ratedCapacity;

    /** 额定功率转速 */
    @Excel(name = "额定功率转速")
    private String ratedCapacityRotationlSpeed;

    /** 最大净扭矩 */
    @Excel(name = "最大净扭矩")
    private String maximumTorque;

    /** 最大净扭矩转速 */
    @Excel(name = "最大净扭矩转速")
    private String maximumTorqueRotationlSpeed;

    /** 燃料供给系统形式 */
    @Excel(name = "燃料供给系统形式")
    private String fuelFeedSystem;

    /** 排气后处理系统形式 */
    @Excel(name = "排气后处理系统形式")
    private String exhaustAftertreatment;

    /** 车辆备案企业类型 */
    @Excel(name = "车辆备案企业类型")
    private String companyType;

    /** 车辆底盘生产厂家 */
    @Excel(name = "车辆底盘生产厂家")
    private String chassisCompanyName;

    /** 马力 */
    @Excel(name = "马力")
    private String enginePower;

    /** 扭矩 */
    @Excel(name = "扭矩")
    private String engineTorque;

    /** 发动机生产地址 */
    @Excel(name = "发动机生产地址")
    private String engineProductionAddress;

    /** 环保公开信息接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "环保公开信息接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String publicTime;

    /** 实际协议类型(1:GB17691（国六除HJ之外）2:HJ征求意见稿 3:HJ1239-2021 4:国五) */
    @Excel(name = "实际协议类型", readConverterExp = "1=GB17691（国六除HJ之外）,2=HJ征求意见稿,3=HJ1239-2021,4=国五")
    private String actualAgreement;

    /** 专用车类型 */
    @Excel(name = "专用车类型")
    private String specialVehType;

    /** 专用车术语 */
    @Excel(name = "专用车术语")
    private String specialVehTerm;

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getEnpInfoPublicNum() {
        return enpInfoPublicNum;
    }

    public void setEnpInfoPublicNum(String enpInfoPublicNum) {
        this.enpInfoPublicNum = enpInfoPublicNum;
    }

    public String getEpaVehicleType() {
        return epaVehicleType;
    }

    public void setEpaVehicleType(String epaVehicleType) {
        this.epaVehicleType = epaVehicleType;
    }

    public String getVehCatalogue() {
        return vehCatalogue;
    }

    public void setVehCatalogue(String vehCatalogue) {
        this.vehCatalogue = vehCatalogue;
    }

    public String getVehCatalogueDetail() {
        return vehCatalogueDetail;
    }

    public void setVehCatalogueDetail(String vehCatalogueDetail) {
        this.vehCatalogueDetail = vehCatalogueDetail;
    }

    public String getEngineEmissionLeveltype() {
        return engineEmissionLeveltype;
    }

    public void setEngineEmissionLeveltype(String engineEmissionLeveltype) {
        this.engineEmissionLeveltype = engineEmissionLeveltype;
    }

    public String getVehicleLicense() {
        return vehicleLicense;
    }

    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense;
    }

    public String getVehicleDomicile() {
        return vehicleDomicile;
    }

    public void setVehicleDomicile(String vehicleDomicile) {
        this.vehicleDomicile = vehicleDomicile;
    }

    public String getRegisterAgreement() {
        return registerAgreement;
    }

    public void setRegisterAgreement(String registerAgreement) {
        this.registerAgreement = registerAgreement;
    }

    public String getVehRegisterMode() {
        return vehRegisterMode;
    }

    public void setVehRegisterMode(String vehRegisterMode) {
        this.vehRegisterMode = vehRegisterMode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getPlatformAccountName() {
        return platformAccountName;
    }

    public void setPlatformAccountName(String platformAccountName) {
        this.platformAccountName = platformAccountName;
    }

    public String getConstructionUnit() {
        return constructionUnit;
    }

    public void setConstructionUnit(String constructionUnit) {
        this.constructionUnit = constructionUnit;
    }

    public String getPlatformMode() {
        return platformMode;
    }

    public void setPlatformMode(String platformMode) {
        this.platformMode = platformMode;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(String registerTime) {
        this.registerTime = registerTime;
    }

    public String getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getSyncTime() {
        return syncTime;
    }

    public void setSyncTime(String syncTime) {
        this.syncTime = syncTime;
    }

    public String getActivationStatus() {
        return activationStatus;
    }

    public void setActivationStatus(String activationStatus) {
        this.activationStatus = activationStatus;
    }

    public String getActivationTime() {
        return activationTime;
    }

    public void setActivationTime(String activationTime) {
        this.activationTime = activationTime;
    }

    public String getFirstOnlineTime() {
        return firstOnlineTime;
    }

    public void setFirstOnlineTime(String firstOnlineTime) {
        this.firstOnlineTime = firstOnlineTime;
    }

    public String getFirstOnlineStatus() {
        return firstOnlineStatus;
    }

    public void setFirstOnlineStatus(String firstOnlineStatus) {
        this.firstOnlineStatus = firstOnlineStatus;
    }

    public String getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(String connectStatus) {
        this.connectStatus = connectStatus;
    }

    public String getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(String connectTime) {
        this.connectTime = connectTime;
    }

    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getVehicleLoads() {
        return vehicleLoads;
    }

    public void setVehicleLoads(String vehicleLoads) {
        this.vehicleLoads = vehicleLoads;
    }

    public String getMaxLoads() {
        return maxLoads;
    }

    public void setMaxLoads(String maxLoads) {
        this.maxLoads = maxLoads;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel;
    }

    public String getEngineManufacturerName() {
        return engineManufacturerName;
    }

    public void setEngineManufacturerName(String engineManufacturerName) {
        this.engineManufacturerName = engineManufacturerName;
    }

    public String getTboxModel() {
        return tboxModel;
    }

    public void setTboxModel(String tboxModel) {
        this.tboxModel = tboxModel;
    }

    public String getTboxManufacturerName() {
        return tboxManufacturerName;
    }

    public void setTboxManufacturerName(String tboxManufacturerName) {
        this.tboxManufacturerName = tboxManufacturerName;
    }

    public String getChipPrefix() {
        return chipPrefix;
    }

    public void setChipPrefix(String chipPrefix) {
        this.chipPrefix = chipPrefix;
    }

    public String getChipModel() {
        return chipModel;
    }

    public void setChipModel(String chipModel) {
        this.chipModel = chipModel;
    }

    public String getChipManufacturerName() {
        return chipManufacturerName;
    }

    public void setChipManufacturerName(String chipManufacturerName) {
        this.chipManufacturerName = chipManufacturerName;
    }

    public String getManufactureDate() {
        return manufactureDate;
    }

    public void setManufactureDate(String manufactureDate) {
        this.manufactureDate = manufactureDate;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getMaximumPower() {
        return maximumPower;
    }

    public void setMaximumPower(String maximumPower) {
        this.maximumPower = maximumPower;
    }

    public String getMaximumEngineRotationlSpeed() {
        return maximumEngineRotationlSpeed;
    }

    public void setMaximumEngineRotationlSpeed(String maximumEngineRotationlSpeed) {
        this.maximumEngineRotationlSpeed = maximumEngineRotationlSpeed;
    }

    public String getRatedCapacity() {
        return ratedCapacity;
    }

    public void setRatedCapacity(String ratedCapacity) {
        this.ratedCapacity = ratedCapacity;
    }

    public String getRatedCapacityRotationlSpeed() {
        return ratedCapacityRotationlSpeed;
    }

    public void setRatedCapacityRotationlSpeed(String ratedCapacityRotationlSpeed) {
        this.ratedCapacityRotationlSpeed = ratedCapacityRotationlSpeed;
    }

    public String getMaximumTorque() {
        return maximumTorque;
    }

    public void setMaximumTorque(String maximumTorque) {
        this.maximumTorque = maximumTorque;
    }

    public String getMaximumTorqueRotationlSpeed() {
        return maximumTorqueRotationlSpeed;
    }

    public void setMaximumTorqueRotationlSpeed(String maximumTorqueRotationlSpeed) {
        this.maximumTorqueRotationlSpeed = maximumTorqueRotationlSpeed;
    }

    public String getFuelFeedSystem() {
        return fuelFeedSystem;
    }

    public void setFuelFeedSystem(String fuelFeedSystem) {
        this.fuelFeedSystem = fuelFeedSystem;
    }

    public String getExhaustAftertreatment() {
        return exhaustAftertreatment;
    }

    public void setExhaustAftertreatment(String exhaustAftertreatment) {
        this.exhaustAftertreatment = exhaustAftertreatment;
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getChassisCompanyName() {
        return chassisCompanyName;
    }

    public void setChassisCompanyName(String chassisCompanyName) {
        this.chassisCompanyName = chassisCompanyName;
    }

    public String getEnginePower() {
        return enginePower;
    }

    public void setEnginePower(String enginePower) {
        this.enginePower = enginePower;
    }

    public String getEngineTorque() {
        return engineTorque;
    }

    public void setEngineTorque(String engineTorque) {
        this.engineTorque = engineTorque;
    }

    public String getEngineProductionAddress() {
        return engineProductionAddress;
    }

    public void setEngineProductionAddress(String engineProductionAddress) {
        this.engineProductionAddress = engineProductionAddress;
    }

    public String getPublicTime() {
        return publicTime;
    }

    public void setPublicTime(String publicTime) {
        this.publicTime = publicTime;
    }

    public String getActualAgreement() {
        return actualAgreement;
    }

    public void setActualAgreement(String actualAgreement) {
        this.actualAgreement = actualAgreement;
    }

    public String getSpecialVehType() {
        return specialVehType;
    }

    public void setSpecialVehType(String specialVehType) {
        this.specialVehType = specialVehType;
    }

    public String getSpecialVehTerm() {
        return specialVehTerm;
    }

    public void setSpecialVehTerm(String specialVehTerm) {
        this.specialVehTerm = specialVehTerm;
    }

    @Override
    public String toString() {
        return "VehicleBasis [vin=" + vin + ", enpInfoPublicNum=" + enpInfoPublicNum + ", epaVehicleType="
                + epaVehicleType + ", vehCatalogue=" + vehCatalogue + ", vehCatalogueDetail=" + vehCatalogueDetail
                + ", engineEmissionLeveltype=" + engineEmissionLeveltype + ", vehicleLicense=" + vehicleLicense
                + ", vehicleDomicile=" + vehicleDomicile + ", registerAgreement=" + registerAgreement
                + ", vehRegisterMode=" + vehRegisterMode + ", companyName=" + companyName + ", platformName="
                + platformName + ", platformAccountName=" + platformAccountName + ", constructionUnit=" + constructionUnit
                + ", platformMode=" + platformMode + ", manufacturerBuildName=" + manufacturerBuildName
                + ", registerTime=" + registerTime + ", syncStatus=" + syncStatus + ", syncTime=" + syncTime
                + ", activationStatus=" + activationStatus + ", activationTime=" + activationTime + ", firstOnlineTime="
                + firstOnlineTime + ", firstOnlineStatus=" + firstOnlineStatus + ", connectStatus=" + connectStatus
                + ", connectTime=" + connectTime + ", vehicleModel=" + vehicleModel + ", vehicleType=" + vehicleType
                + ", vehicleLoads=" + vehicleLoads + ", maxLoads=" + maxLoads + ", engineModel=" + engineModel
                + ", engineManufacturerName=" + engineManufacturerName + ", tboxModel=" + tboxModel
                + ", tboxManufacturerName=" + tboxManufacturerName + ", chipPrefix=" + chipPrefix + ", chipModel="
                + chipModel + ", chipManufacturerName=" + chipManufacturerName + ", manufactureDate=" + manufactureDate
                + ", country=" + country + ", maximumPower=" + maximumPower + ", maximumEngineRotationlSpeed="
                + maximumEngineRotationlSpeed + ", ratedCapacity=" + ratedCapacity + ", ratedCapacityRotationlSpeed="
                + ratedCapacityRotationlSpeed + ", maximumTorque=" + maximumTorque + ", maximumTorqueRotationlSpeed="
                + maximumTorqueRotationlSpeed + ", fuelFeedSystem=" + fuelFeedSystem + ", exhaustAftertreatment="
                + exhaustAftertreatment + ", companyType=" + companyType + ", chassisCompanyName=" + chassisCompanyName
                + ", enginePower=" + enginePower + ", engineTorque=" + engineTorque + ", engineProductionAddress="
                + engineProductionAddress + ", publicTime=" + publicTime + ", actualAgreement=" + actualAgreement
                + ", specialVehType=" + specialVehType + ", specialVehTerm=" + specialVehTerm + "]";
    }
} 