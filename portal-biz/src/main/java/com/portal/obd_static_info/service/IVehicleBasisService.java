package com.portal.obd_static_info.service;

import java.util.List;
import com.portal.obd_static_info.domain.VehicleBasis;
import com.portal.obd_static_info.dto.VehicleBasisQueryParam;

/**
 * 车辆基础信息服务接口
 * 
 * <AUTHOR>
 */
public interface IVehicleBasisService {
    /**
     * 查询车辆基础信息列表
     * 
     * @param queryParam 查询参数
     * @return 车辆基础信息集合
     */
    List<VehicleBasis> selectVehicleBasisList(VehicleBasisQueryParam queryParam);
    
    /**
     * 根据VIN查询车辆基础信息
     * 
     * @param vin 车辆VIN码
     * @return 车辆基础信息
     */
    VehicleBasis selectVehicleBasisByVin(String vin);
    
    /**
     * 导出车辆基础信息
     * 
     * @param queryParam 查询参数
     * @return 车辆基础信息集合
     */
    List<VehicleBasis> exportVehicleBasis(VehicleBasisQueryParam queryParam);
    
    /**
     * 通用字段值查询
     * 
     * @param fieldName 字段名称
     * @return 字段值列表
     */
    List<String> selectFieldValues(String fieldName);
} 