package com.portal.obd_static_info.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.portal.obd_static_info.domain.VehicleBasis;
import com.portal.obd_static_info.dto.VehicleBasisQueryParam;
import com.portal.obd_static_info.mapper.VehicleBasisMapper;
import com.portal.obd_static_info.service.IVehicleBasisService;
import com.portal.common.annotation.DataSource;
import com.portal.common.enums.DataSourceType;

/**
 * 车辆基础信息服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VehicleBasisServiceImpl implements IVehicleBasisService {
    @Autowired
    private VehicleBasisMapper vehicleBasisMapper;

    /**
     * 查询车辆基础信息列表
     * 
     * @param queryParam 查询参数
     * @return 车辆基础信息集合
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleBasis> selectVehicleBasisList(VehicleBasisQueryParam queryParam) {
        return vehicleBasisMapper.selectVehicleBasisList(queryParam);
    }
    
    /**
     * 根据VIN查询车辆基础信息
     * 
     * @param vin 车辆VIN码
     * @return 车辆基础信息
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public VehicleBasis selectVehicleBasisByVin(String vin) {
        return vehicleBasisMapper.selectVehicleBasisByVin(vin);
    }
    
    /**
     * 导出车辆基础信息
     * 
     * @param queryParam 查询参数
     * @return 车辆基础信息集合
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleBasis> exportVehicleBasis(VehicleBasisQueryParam queryParam) {
        return vehicleBasisMapper.selectVehicleBasisList(queryParam);
    }
    
    /**
     * 通用字段值查询
     * 
     * @param fieldName 字段名称
     * @return 字段值列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<String> selectFieldValues(String fieldName) {
        // 安全检查，防止SQL注入
        String safeFieldName = getSafeFieldName(fieldName);
        if (safeFieldName == null) {
            return null;
        }
        return vehicleBasisMapper.selectFieldValues(safeFieldName);
    }
    
    /**
     * @param fieldName 字段名称
     * @return 安全的字段名
     */
    private String getSafeFieldName(String fieldName) {
        // 通用查询的字段名列表
        String[] allowedFields = {
            "vin", "enp_info_public_num", "epa_vehicle_type", "veh_catalogue", "veh_catalogue_detail",
            "engine_emission_leveltype", "vehicle_license", "vehicle_domicile", "register_agreement", 
            "veh_register_mode", "company_name", "platform_name", "platform_account_name", 
            "construction_unit", "platform_mode", "manufacturer_build_name", "register_time", 
            "sync_status", "sync_time", "activation_status", "activation_time", "first_online_time", 
            "first_online_status", "connect_status", "connect_time", "vehicle_model", "vehicle_type", 
            "vehicle_loads", "max_loads", "engine_model", "engine_manufacturer_name", "tbox_model", 
            "tbox_manufacturer_name", "chip_prefix", "chip_model", "chip_manufacturer_name", 
            "manufacture_date", "country", "maximum_power", "maximum_engine_rotationl_speed", 
            "rated_capacity", "rated_capacity_rotationl_speed", "maximum_torque", 
            "maximum_torque_rotationl_speed", "fuel_feed_system", "exhaust_aftertreatment", 
            "company_type", "chassis_company_name", "engine_power", "engine_torque", 
            "engine_production_address", "public_time", "actual_agreement", "special_veh_type", 
            "special_veh_term"
        };
        
        // 检查传入字段是否在列表中
        for (String allowed : allowedFields) {
            if (allowed.equals(fieldName)) {
                return fieldName;
            }
        }
        
        // 如果字段名不在允许列表中，返回null
        return null;
    }
} 