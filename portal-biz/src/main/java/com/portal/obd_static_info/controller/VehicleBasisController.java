package com.portal.obd_static_info.controller;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.portal.common.annotation.Log;
import com.portal.common.core.controller.BaseController;
import com.portal.common.core.domain.AjaxResult;
import com.portal.common.core.page.TableDataInfo;
import com.portal.common.enums.BusinessType;
import com.portal.common.utils.poi.ExcelUtil;
import com.portal.obd_static_info.domain.VehicleBasis;
import com.portal.obd_static_info.dto.VehicleBasisQueryParam;
import com.portal.obd_static_info.service.IVehicleBasisService;

/**
 * 车辆基础信息Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/obd_static_info/vehicle")
public class VehicleBasisController extends BaseController {
    @Autowired
    private IVehicleBasisService vehicleBasisService;

    /**
     * 查询车辆基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:list')")
    @GetMapping("/list")
    public TableDataInfo list(VehicleBasisQueryParam queryParam) {
        startPage();
        List<VehicleBasis> list = vehicleBasisService.selectVehicleBasisList(queryParam);
        return getDataTable(list);
    }

    /**
     * 通用查询接口
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:list')")
    @PostMapping("/query")
    public TableDataInfo query(@RequestBody Map<String, Object> params) {
        startPage();
        VehicleBasisQueryParam queryParam = new VehicleBasisQueryParam();

        // 将Map参数映射到对象属性
        if (params.get("vin") != null) {
            queryParam.setVin(params.get("vin").toString());
        }
        if (params.get("epaVehicleType") != null) {
            queryParam.setEpaVehicleType(params.get("epaVehicleType").toString());
        }
        if (params.get("vehicleLicense") != null) {
            queryParam.setVehicleLicense(params.get("vehicleLicense").toString());
        }
        if (params.get("vehicleType") != null) {
            queryParam.setVehicleType(params.get("vehicleType").toString());
        }
        if (params.get("engineModel") != null) {
            queryParam.setEngineModel(params.get("engineModel").toString());
        }
        if (params.get("engineManufacturerName") != null) {
            queryParam.setEngineManufacturerName(params.get("engineManufacturerName").toString());
        }
        if (params.get("manufacturerBuildName") != null) {
            queryParam.setManufacturerBuildName(params.get("manufacturerBuildName").toString());
        }
        if (params.get("vehicleLoads") != null) {
            queryParam.setVehicleLoads(params.get("vehicleLoads").toString());
        }
        if (params.get("engineEmissionLeveltype") != null) {
            queryParam.setEngineEmissionLeveltype(params.get("engineEmissionLeveltype").toString());
        }
        if (params.get("connectStatus") != null) {
            queryParam.setConnectStatus(params.get("connectStatus").toString());
        }
        if (params.get("vehicleDomicile") != null) {
            queryParam.setVehicleDomicile(params.get("vehicleDomicile").toString());
        }
        if (params.get("firstOnlineStatus") != null) {
            queryParam.setFirstOnlineStatus(params.get("firstOnlineStatus").toString());
        }
        if (params.get("syncStatus") != null) {
            queryParam.setSyncStatus(params.get("syncStatus").toString());
        }
        if (params.get("beginTime") != null) {
            queryParam.setBeginTime(params.get("beginTime").toString());
        }
        if (params.get("endTime") != null) {
            queryParam.setEndTime(params.get("endTime").toString());
        }

        // 同时保留params用于动态查询
        queryParam.setParams(params);

        List<VehicleBasis> list = vehicleBasisService.selectVehicleBasisList(queryParam);
        return getDataTable(list);
    }
    
    /**
     * 通用字段值查询接口
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:list')")
    @GetMapping("/field/{fieldName}")
    public AjaxResult getFieldValues(@PathVariable("fieldName") String fieldName) {
        List<String> list = vehicleBasisService.selectFieldValues(fieldName);
        return success(list);
    }

    /**
     * 导出车辆基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:export')")
    @Log(title = "车辆基础信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(VehicleBasisQueryParam queryParam) {
        List<VehicleBasis> list = vehicleBasisService.exportVehicleBasis(queryParam);
        ExcelUtil<VehicleBasis> util = new ExcelUtil<VehicleBasis>(VehicleBasis.class);
        return util.exportExcel(list, "车辆基础信息数据");
    }
    
    /**
     * 通用导出接口
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:export')")
    @Log(title = "车辆基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportByParams")
    public AjaxResult exportByParams(@RequestBody Map<String, Object> params) {
        VehicleBasisQueryParam queryParam = new VehicleBasisQueryParam();
        queryParam.setParams(params);
        List<VehicleBasis> list = vehicleBasisService.exportVehicleBasis(queryParam);
        ExcelUtil<VehicleBasis> util = new ExcelUtil<VehicleBasis>(VehicleBasis.class);
        return util.exportExcel(list, "车辆基础信息数据");
    }

    /**
     * 获取车辆基础信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:query')")
    @GetMapping(value = "/{vin}")
    public AjaxResult getInfo(@PathVariable("vin") String vin) {
        return success(vehicleBasisService.selectVehicleBasisByVin(vin));
    }
} 