package com.portal.obd_static_info.controller;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.portal.common.annotation.Log;
import com.portal.common.core.controller.BaseController;
import com.portal.common.core.domain.AjaxResult;
import com.portal.common.core.page.TableDataInfo;
import com.portal.common.enums.BusinessType;
import com.portal.common.utils.poi.ExcelUtil;
import com.portal.obd_static_info.domain.VehicleBasis;
import com.portal.obd_static_info.dto.VehicleBasisQueryParam;
import com.portal.obd_static_info.service.IVehicleBasisService;

/**
 * 车辆基础信息Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/obd_static_info/vehicle")
public class VehicleBasisController extends BaseController {
    @Autowired
    private IVehicleBasisService vehicleBasisService;

    /**
     * 查询车辆基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:list')")
    @GetMapping("/list")
    public TableDataInfo list(VehicleBasisQueryParam queryParam) {
        startPage();
        List<VehicleBasis> list = vehicleBasisService.selectVehicleBasisList(queryParam);
        return getDataTable(list);
    }

    /**
     * 通用查询接口 - 支持数据库表中任意字段的动态查询
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:list')")
    @PostMapping("/query")
    public TableDataInfo query(@RequestBody Map<String, Object> params) {
        startPage();
        VehicleBasisQueryParam queryParam = new VehicleBasisQueryParam();
        // 直接使用动态参数，支持所有数据库字段
        queryParam.setParams(params);
        List<VehicleBasis> list = vehicleBasisService.selectVehicleBasisList(queryParam);
        return getDataTable(list);
    }

    /**
     * 通用字段值查询接口
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:list')")
    @GetMapping("/field/{fieldName}")
    public AjaxResult getFieldValues(@PathVariable("fieldName") String fieldName) {
        List<String> list = vehicleBasisService.selectFieldValues(fieldName);
        return success(list);
    }

    /**
     * 导出车辆基础信息列表 - 支持动态参数查询
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:export')")
    @Log(title = "车辆基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(@RequestBody Map<String, Object> params) {
        VehicleBasisQueryParam queryParam = new VehicleBasisQueryParam();
        queryParam.setParams(params);
        List<VehicleBasis> list = vehicleBasisService.exportVehicleBasis(queryParam);
        ExcelUtil<VehicleBasis> util = new ExcelUtil<VehicleBasis>(VehicleBasis.class);
        return util.exportExcel(list, "车辆基础信息数据");
    }

    /**
     * 获取车辆基础信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('obd_static_info:vehicle:query')")
    @GetMapping(value = "/{vin}")
    public AjaxResult getInfo(@PathVariable("vin") String vin) {
        return success(vehicleBasisService.selectVehicleBasisByVin(vin));
    }
} 