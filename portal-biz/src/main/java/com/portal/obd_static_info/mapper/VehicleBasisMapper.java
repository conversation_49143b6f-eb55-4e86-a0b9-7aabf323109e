package com.portal.obd_static_info.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.portal.obd_static_info.domain.VehicleBasis;
import com.portal.obd_static_info.dto.VehicleBasisQueryParam;

/**
 * 车辆基础信息数据层
 * 
 * <AUTHOR>
 */
@Mapper
public interface VehicleBasisMapper {
    /**
     * 查询车辆基础信息列表
     * 
     * @param queryParam 查询参数
     * @return 车辆基础信息集合
     */
    List<VehicleBasis> selectVehicleBasisList(VehicleBasisQueryParam queryParam);
    
    /**
     * 根据VIN查询车辆基础信息
     * 
     * @param vin 车辆VIN码
     * @return 车辆基础信息
     */
    VehicleBasis selectVehicleBasisByVin(String vin);
    
    /**
     * 通用字段值查询
     * 
     * @param fieldName 字段名称
     * @return 字段值列表
     */
    List<String> selectFieldValues(@Param("fieldName") String fieldName);
} 