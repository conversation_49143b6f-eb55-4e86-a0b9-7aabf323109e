package com.portal.shiyunhui_daily_report.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

import org.springframework.web.bind.annotation.PathVariable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.portal.common.annotation.Log;
import com.portal.common.enums.BusinessType;
import com.portal.common.utils.poi.ExcelUtil;

import com.portal.common.core.controller.BaseController;
import com.portal.common.core.domain.AjaxResult;
import com.portal.common.core.page.TableDataInfo;
import com.portal.shiyunhui_daily_report.domain.VehicleDriveOdometer;
import com.portal.shiyunhui_daily_report.domain.VehicleHotArea;
import com.portal.shiyunhui_daily_report.domain.VehicleHotHour;
import com.portal.shiyunhui_daily_report.domain.VehicleOnlineStat;
import com.portal.shiyunhui_daily_report.domain.VehicleTotalOdometer;
import com.portal.shiyunhui_daily_report.domain.VehicleOverEmission;
import com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam;
import com.portal.shiyunhui_daily_report.service.IVehicleOnlineService;

/**
 * 车辆统计控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/shiyunhui/vehicle")
public class VehicleOnlineController extends BaseController {
    @Autowired
    private IVehicleOnlineService vehicleOnlineService;
    
    /**
     * 获取车辆上线统计数据
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:online_stats')")
    @GetMapping("/online_stats")
    public AjaxResult getVehicleOnlineStats(VehicleOnlineQueryParam queryParam) {
        List<VehicleOnlineStat> list = vehicleOnlineService.getVehicleOnlineStats(queryParam);
        return AjaxResult.success(list);
    }

    /**
     * 获取单车每日行驶里程
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:drive_odometer')")
    @GetMapping("/drive_odometer")
    public TableDataInfo getVehicleDriveOdometer(VehicleOnlineQueryParam queryParam) {
        startPage();
        List<VehicleDriveOdometer> list = vehicleOnlineService.getVehicleDriveOdometer(queryParam);
        return getDataTable(list);
    }

    /**
     * 获取热点时段车辆数
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:hot_hour')")
    @GetMapping("/hot_hour")
    public AjaxResult getVehicleHotHour(VehicleOnlineQueryParam queryParam) {
        List<VehicleHotHour> list = vehicleOnlineService.getVehicleHotHour(queryParam);
        return AjaxResult.success(list);
    }

    /**
     * 获取热点区域车辆数
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:hot_area')")
    @GetMapping("/hot_area")
    public AjaxResult getVehicleHotArea(VehicleOnlineQueryParam queryParam) {
        List<VehicleHotArea> list = vehicleOnlineService.getVehicleHotArea(queryParam);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取总行驶里程和超排车辆数统计
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:total_odometer')")
    @GetMapping("/total_odometer")
    public AjaxResult getTotalOdometer(VehicleOnlineQueryParam queryParam) {
        List<VehicleTotalOdometer> list = vehicleOnlineService.getTotalOdometer(queryParam);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取上线车辆数最多的城市
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:hot_area_stats')")
    @GetMapping("/hot_area_stats")
    public AjaxResult getHotAreaStats(VehicleOnlineQueryParam queryParam) {
        List<VehicleHotArea> list = vehicleOnlineService.getHotAreaStats(queryParam);
        return AjaxResult.success(list);
    }

    /**
     * 获取维度值列表
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:dimension_values')")
    @GetMapping("/dimension_values")
    public AjaxResult getDimensionValues(VehicleOnlineQueryParam queryParam) {
        List<String> list = vehicleOnlineService.getDimensionValues(queryParam);
        return AjaxResult.success(list);
    }

    /**
     * 导出单车每日行驶里程数据
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:drive_odometer:export')")
    @Log(title = "单车每日行驶里程", businessType = BusinessType.EXPORT)
    @PostMapping("/drive_odometer/export")
    public void exportVehicleDriveOdometer(HttpServletResponse response, @RequestBody(required = false) VehicleOnlineQueryParam queryParam) {
        // 参数处理
        if (queryParam == null) {
            queryParam = new VehicleOnlineQueryParam();
        }

        // 分页处理
        List<VehicleDriveOdometer> list;
        if (Boolean.TRUE.equals(queryParam.getExportCurrentPage())) {
            // 导出当前页
            PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
            list = vehicleOnlineService.getVehicleDriveOdometer(queryParam);
            list = new PageInfo<>(list).getList();
        } else {
            // 导出全部数据
            list = vehicleOnlineService.getVehicleDriveOdometer(queryParam);
        }

        // Excel 导出
        ExcelUtil<VehicleDriveOdometer> util = new ExcelUtil<>(VehicleDriveOdometer.class);
        util.exportExcel(response, list, "单车每日行驶里程数据");
    }

    /**
     * 根据adcode获取围栏几何数据
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:geometry')")
    @GetMapping("/geometry/{adcode}")
    public AjaxResult getGeometryByAdcode(@PathVariable String adcode) {
        String geometry = vehicleOnlineService.getGeometryByAdcode(adcode);
        if (geometry != null) {
            try {
                // 将几何数据字符串解析为JSON对象，避免转义
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode geometryJson = objectMapper.readTree(geometry);

                // 构建响应
                Map<String, Object> result = new HashMap<>();
                result.put("code", 200);
                result.put("msg", "操作成功");
                result.put("data", geometryJson);

                return AjaxResult.success().put("code", 200).put("msg", "操作成功").put("data", geometryJson);
            } catch (Exception e) {
                return AjaxResult.error("几何数据格式错误");
            }
        } else {
            return AjaxResult.error("未找到对应的围栏数据");
        }
    }

    /**
     * 获取在线车辆数统计
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:total_online_cars')")
    @GetMapping("/total_online_cars")
    public AjaxResult getTotalOnlineCars(VehicleOnlineQueryParam queryParam) {
        List<VehicleOnlineStat> list = vehicleOnlineService.getTotalOnlineCars(queryParam);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取超排车辆数据
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:over_emission')")
    @GetMapping("/over_emission")
    public TableDataInfo getVehicleOverEmission(VehicleOnlineQueryParam queryParam) {
        startPage();
        List<VehicleOverEmission> list = vehicleOnlineService.getVehicleOverEmission(queryParam);
        return getDataTable(list);
    }
    
    /**
     * 导出超排车辆数据
     */
    @PreAuthorize("@ss.hasPermi('shiyunhui:vehicle:over_emission:export')")
    @Log(title = "超排车辆数据", businessType = BusinessType.EXPORT)
    @PostMapping("/over_emission/export")
    public void exportVehicleOverEmission(HttpServletResponse response, @RequestBody(required = false) VehicleOnlineQueryParam queryParam) {
        // 参数处理
        if (queryParam == null) {
            queryParam = new VehicleOnlineQueryParam();
        }

        // 分页处理
        List<VehicleOverEmission> list;
        if (Boolean.TRUE.equals(queryParam.getExportCurrentPage())) {
            // 导出当前页
            PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
            list = vehicleOnlineService.getVehicleOverEmission(queryParam);
            list = new PageInfo<>(list).getList();
        } else {
            // 导出全部数据
            list = vehicleOnlineService.getVehicleOverEmission(queryParam);
        }

        // Excel 导出
        ExcelUtil<VehicleOverEmission> util = new ExcelUtil<>(VehicleOverEmission.class);
        util.exportExcel(response, list, "超排车辆数据");
    }
}