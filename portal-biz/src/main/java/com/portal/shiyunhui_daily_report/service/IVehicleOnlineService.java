package com.portal.shiyunhui_daily_report.service;

import java.util.List;
import com.portal.shiyunhui_daily_report.domain.VehicleDriveOdometer;
import com.portal.shiyunhui_daily_report.domain.VehicleHotArea;
import com.portal.shiyunhui_daily_report.domain.VehicleHotHour;
import com.portal.shiyunhui_daily_report.domain.VehicleOnlineStat;
import com.portal.shiyunhui_daily_report.domain.VehicleTotalOdometer;
import com.portal.shiyunhui_daily_report.domain.VehicleOverEmission;
import com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam;

/**
 * 车辆统计服务接口
 * 
 * <AUTHOR>
 */
public interface IVehicleOnlineService {
    /**
     * 获取车辆上线统计数据
     * 
     * @param queryParam 查询参数
     * @return 统计结果列表
     */
    List<VehicleOnlineStat> getVehicleOnlineStats(VehicleOnlineQueryParam queryParam);

    /**
     * 获取单车每日行驶里程
     * 
     * @param queryParam 查询参数
     * @return 行驶里程列表
     */
    List<VehicleDriveOdometer> getVehicleDriveOdometer(VehicleOnlineQueryParam queryParam);

    /**
     * 获取热点时段车辆数
     * 
     * @param queryParam 查询参数
     * @return 热点时段列表
     */
    List<VehicleHotHour> getVehicleHotHour(VehicleOnlineQueryParam queryParam);

    /**
     * 获取热点区域车辆数
     * 
     * @param queryParam 查询参数
     * @return 热点区域列表
     */
    List<VehicleHotArea> getVehicleHotArea(VehicleOnlineQueryParam queryParam);
    
    /**
     * 获取总行驶里程和超排车辆数统计
     * 
     * @param queryParam 查询参数
     * @return 统计结果列表
     */
    List<VehicleTotalOdometer> getTotalOdometer(VehicleOnlineQueryParam queryParam);
    
    /**
     * 获取上线车辆数最多的城市
     *
     * @param queryParam 查询参数
     * @return 今日和昨日车辆数最多的城市
     */
    List<VehicleHotArea> getHotAreaStats(VehicleOnlineQueryParam queryParam);

    /**
     * 获取维度值列表
     *
     * @param queryParam 查询参数（包含dimensionType和地区过滤条件）
     * @return 维度值列表
     */
    List<String> getDimensionValues(VehicleOnlineQueryParam queryParam);

    /**
     * 根据adcode获取围栏几何数据
     *
     * @param adcode 区域代码
     * @return 原始几何数据
     */
    String getGeometryByAdcode(String adcode);

    /**
     * 获取在线车辆数统计
     *
     * @param queryParam 查询参数
     * @return 在线车辆数统计结果列表
     */
    List<VehicleOnlineStat> getTotalOnlineCars(VehicleOnlineQueryParam queryParam);
    
    /**
     * 获取超排车辆数据
     *
     * @param queryParam 查询参数
     * @return 超排车辆数据列表
     */
    List<VehicleOverEmission> getVehicleOverEmission(VehicleOnlineQueryParam queryParam);
}