package com.portal.shiyunhui_daily_report.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.portal.shiyunhui_daily_report.domain.VehicleDriveOdometer;
import com.portal.shiyunhui_daily_report.domain.VehicleHotArea;
import com.portal.shiyunhui_daily_report.domain.VehicleHotHour;
import com.portal.shiyunhui_daily_report.domain.VehicleOnlineStat;
import com.portal.shiyunhui_daily_report.domain.VehicleTotalOdometer;
import com.portal.shiyunhui_daily_report.domain.VehicleOverEmission;
import com.portal.shiyunhui_daily_report.dto.VehicleOnlineQueryParam;
import com.portal.shiyunhui_daily_report.mapper.VehicleOnlineMapper;
import com.portal.shiyunhui_daily_report.service.IVehicleOnlineService;
import com.portal.common.annotation.DataSource;
import com.portal.common.enums.DataSourceType;

/**
 * 车辆统计服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VehicleOnlineServiceImpl implements IVehicleOnlineService {
    @Autowired
    private VehicleOnlineMapper vehicleOnlineMapper;

    /**
     * 获取车辆上线统计数据
     *
     * @param queryParam 查询参数
     * @return 统计结果列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleOnlineStat> getVehicleOnlineStats(VehicleOnlineQueryParam queryParam) {
        // 如果未传入日期，设置为当天
        if (queryParam.getPtDate() == null) {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            queryParam.setPtDate(today.format(formatter));
        }

        return vehicleOnlineMapper.getVehicleOnlineStats(queryParam);
    }

    /**
     * 获取单车每日行驶里程
     * 
     * @param queryParam 查询参数
     * @return 行驶里程列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleDriveOdometer> getVehicleDriveOdometer(VehicleOnlineQueryParam queryParam) {
        // 如果未传入日期，设置为当天
        if (queryParam.getPtDate() == null) {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            queryParam.setPtDate(today.format(formatter));
        }
        
        return vehicleOnlineMapper.getVehicleDriveOdometer(queryParam);
    }

    /**
     * 获取热点时段车辆数
     * 
     * @param queryParam 查询参数
     * @return 热点时段列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleHotHour> getVehicleHotHour(VehicleOnlineQueryParam queryParam) {
        // 如果未传入日期，设置为当天
        if (queryParam.getPtDate() == null) {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            queryParam.setPtDate(today.format(formatter));
        }
        
        return vehicleOnlineMapper.getVehicleHotHour(queryParam);
    }

    /**
     * 获取热点区域车辆数
     * 
     * @param queryParam 查询参数
     * @return 热点区域列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleHotArea> getVehicleHotArea(VehicleOnlineQueryParam queryParam) {
        // 如果未传入日期，设置为当天
        if (queryParam.getPtDate() == null) {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            queryParam.setPtDate(today.format(formatter));
        }
        
        return vehicleOnlineMapper.getVehicleHotArea(queryParam);
    }
    
    /**
     * 获取总行驶里程和超排车辆数统计
     * 
     * @param queryParam 查询参数
     * @return 统计结果列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleTotalOdometer> getTotalOdometer(VehicleOnlineQueryParam queryParam) {
        // 如果未传入日期，设置为当天
        if (queryParam.getPtDate() == null) {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            queryParam.setPtDate(today.format(formatter));
        }
        
        return vehicleOnlineMapper.getTotalOdometer(queryParam);
    }
    
    /**
     * 获取上线车辆数最多的城市
     *
     * @param queryParam 查询参数
     * @return 今日和昨日车辆数最多的城市
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleHotArea> getHotAreaStats(VehicleOnlineQueryParam queryParam) {
        // 如果未传入日期，设置为当天
        if (queryParam.getPtDate() == null) {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            queryParam.setPtDate(today.format(formatter));
        }

        return vehicleOnlineMapper.getHotAreaStats(queryParam);
    }

    /**
     * 获取维度值列表
     *
     * @param queryParam 查询参数（包含dimensionType和地区过滤条件）
     * @return 维度值列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<String> getDimensionValues(VehicleOnlineQueryParam queryParam) {
        return vehicleOnlineMapper.getDimensionValues(queryParam);
    }

    /**
     * 根据adcode获取围栏几何数据
     *
     * @param adcode 区域代码
     * @return 原始几何数据
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public String getGeometryByAdcode(String adcode) {
        String geometry = vehicleOnlineMapper.getGeometryByAdcode(adcode);
        if (geometry != null && !geometry.isEmpty()) {
            // 直接返回原始几何数据，不进行压缩
            return geometry;
        }
        return null;
    }

    /**
     * 获取在线车辆数统计
     *
     * @param queryParam 查询参数
     * @return 在线车辆数统计结果列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleOnlineStat> getTotalOnlineCars(VehicleOnlineQueryParam queryParam) {
        // 如果未传入日期，设置为当天
        if (queryParam.getPtDate() == null) {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            queryParam.setPtDate(today.format(formatter));
        }

        return vehicleOnlineMapper.getTotalOnlineCars(queryParam);
    }
    
    /**
     * 获取超排车辆数据
     *
     * @param queryParam 查询参数
     * @return 超排车辆数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleOverEmission> getVehicleOverEmission(VehicleOnlineQueryParam queryParam) {
        // 如果未传入日期，设置为当天
        if (queryParam.getPtDate() == null) {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            queryParam.setPtDate(today.format(formatter));
        }
        
        return vehicleOnlineMapper.getVehicleOverEmission(queryParam);
    }
}