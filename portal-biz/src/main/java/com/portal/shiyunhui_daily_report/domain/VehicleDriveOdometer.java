package com.portal.shiyunhui_daily_report.domain;

import com.portal.common.annotation.Excel;

/**
 * 单车每日行驶里程实体类
 *
 * <AUTHOR>
 */
public class VehicleDriveOdometer {
    /** 省份名称 */
    @Excel(name = "省份名称")
    private String provinceNm;

    /** 省份编码 */
    @Excel(name = "省份编码")
    private String provinceAdcode;

    /** 城市名称 */
    @Excel(name = "城市名称")
    private String cityNm;

    /** 城市编码 */
    @Excel(name = "城市编码")
    private String cityAdcode;

    /** 区县名称 */
    @Excel(name = "区县名称")
    private String districtNm;

    /** 区县编码 */
    @Excel(name = "区县编码")
    private String districtAdcode;

    /** 燃料类型 */
    @Excel(name = "燃料类型", readConverterExp = "1=柴油车,2=燃气车,3=双燃料汽车,4=油电混合动力,5=气电混合动力,6=甲醇燃料汽车")
    private String epaType;

    /** 车辆类型 */
    @Excel(name = "车辆类型", readConverterExp = "1=载货汽车,2=越野汽车,3=自卸汽车,4=牵引汽车,5=专用汽车,6=客车,7=轿车,8=半挂车")
    private String vehicleType;

    /** 发动机型号 */
    @Excel(name = "发动机型号")
    private String engineModel;

    /** 发动机制造商名称 */
    @Excel(name = "发动机制造商")
    private String engineManufacturerName;

    /** 整车制造商名称 */
    @Excel(name = "整车制造商")
    private String manufacturerBuildName;

    /** 车辆载重 */
    @Excel(name = "车辆载重(吨)")
    private String vehicleLoads;

    /** 车辆VIN码 */
    @Excel(name = "车辆VIN码")
    private String vin;

    /** 行驶里程(km) */
    @Excel(name = "行驶里程(km)")
    private Double drivingOdometer;

    /** 统计日期 */
    @Excel(name = "统计日期", dateFormat = "yyyy-MM-dd")
    private String ptDate;

    public String getProvinceNm() {
        return provinceNm;
    }

    public void setProvinceNm(String provinceNm) {
        this.provinceNm = provinceNm;
    }

    public String getProvinceAdcode() {
        return provinceAdcode;
    }

    public void setProvinceAdcode(String provinceAdcode) {
        this.provinceAdcode = provinceAdcode;
    }

    public String getCityNm() {
        return cityNm;
    }

    public void setCityNm(String cityNm) {
        this.cityNm = cityNm;
    }

    public String getCityAdcode() {
        return cityAdcode;
    }

    public void setCityAdcode(String cityAdcode) {
        this.cityAdcode = cityAdcode;
    }

    public String getDistrictNm() {
        return districtNm;
    }

    public void setDistrictNm(String districtNm) {
        this.districtNm = districtNm;
    }

    public String getDistrictAdcode() {
        return districtAdcode;
    }

    public void setDistrictAdcode(String districtAdcode) {
        this.districtAdcode = districtAdcode;
    }

    public String getEpaType() {
        return epaType;
    }

    public void setEpaType(String epaType) {
        this.epaType = epaType;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel;
    }

    public String getEngineManufacturerName() {
        return engineManufacturerName;
    }

    public void setEngineManufacturerName(String engineManufacturerName) {
        this.engineManufacturerName = engineManufacturerName;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getVehicleLoads() {
        return vehicleLoads;
    }

    public void setVehicleLoads(String vehicleLoads) {
        this.vehicleLoads = vehicleLoads;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Double getDrivingOdometer() {
        return drivingOdometer;
    }

    public void setDrivingOdometer(Double drivingOdometer) {
        this.drivingOdometer = drivingOdometer;
    }

    public String getPtDate() {
        return ptDate;
    }

    public void setPtDate(String ptDate) {
        this.ptDate = ptDate;
    }
} 