package com.portal.shiyunhui_daily_report.domain;

import java.io.Serializable;
import com.portal.common.annotation.Excel;

/**
 * 超排车辆信息对象
 * 
 * <AUTHOR>
 */
public class VehicleOverEmission implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 省份名称 */
    @Excel(name = "省份")
    private String provinceNm;

    /** 省份代码 */
    @Excel(name = "省份代码")
    private String provinceAdcode;

    /** 城市名称 */
    @Excel(name = "城市")
    private String cityNm;

    /** 城市代码 */
    @Excel(name = "城市代码")
    private String cityAdcode;

    /** 区县名称 */
    @Excel(name = "区县")
    private String districtNm;

    /** 区县代码 */
    @Excel(name = "区县代码")
    private String districtAdcode;

    /** 排放标准 */
    @Excel(name = "排放标准")
    private String epaType;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String vehicleType;

    /** 发动机型号 */
    @Excel(name = "发动机型号")
    private String engineModel;

    /** 发动机制造商 */
    @Excel(name = "发动机制造商")
    private String engineManufacturerName;

    /** 车辆制造商 */
    @Excel(name = "车辆制造商")
    private String manufacturerBuildName;

    /** 车辆载重 */
    @Excel(name = "车辆载重")
    private String vehicleLoads;

    /** 车辆VIN码 */
    @Excel(name = "车辆VIN码")
    private String vin;

    /** 日期 */
    @Excel(name = "日期")
    private String ptDate;

    public String getProvinceNm() {
        return provinceNm;
    }

    public void setProvinceNm(String provinceNm) {
        this.provinceNm = provinceNm;
    }

    public String getProvinceAdcode() {
        return provinceAdcode;
    }

    public void setProvinceAdcode(String provinceAdcode) {
        this.provinceAdcode = provinceAdcode;
    }

    public String getCityNm() {
        return cityNm;
    }

    public void setCityNm(String cityNm) {
        this.cityNm = cityNm;
    }

    public String getCityAdcode() {
        return cityAdcode;
    }

    public void setCityAdcode(String cityAdcode) {
        this.cityAdcode = cityAdcode;
    }

    public String getDistrictNm() {
        return districtNm;
    }

    public void setDistrictNm(String districtNm) {
        this.districtNm = districtNm;
    }

    public String getDistrictAdcode() {
        return districtAdcode;
    }

    public void setDistrictAdcode(String districtAdcode) {
        this.districtAdcode = districtAdcode;
    }

    public String getEpaType() {
        return epaType;
    }

    public void setEpaType(String epaType) {
        this.epaType = epaType;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel;
    }

    public String getEngineManufacturerName() {
        return engineManufacturerName;
    }

    public void setEngineManufacturerName(String engineManufacturerName) {
        this.engineManufacturerName = engineManufacturerName;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getVehicleLoads() {
        return vehicleLoads;
    }

    public void setVehicleLoads(String vehicleLoads) {
        this.vehicleLoads = vehicleLoads;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getPtDate() {
        return ptDate;
    }

    public void setPtDate(String ptDate) {
        this.ptDate = ptDate;
    }
} 