package com.portal.shiyunhui_daily_report.domain;

import com.fasterxml.jackson.annotation.JsonRawValue;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.zip.GZIPOutputStream;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

import com.portal.common.utils.GeometryCompressionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 车辆上线统计结果
 *
 * <AUTHOR>
 */
public class VehicleOnlineStat {

    /** 静态几何数据压缩缓存 */
    private static final Map<String, String> GEOMETRY_CACHE = new ConcurrentHashMap<>();
    /** 省份名称 */
    private String provinceNm;

    /** 省份编码 */
    private String provinceAdcode;

    /** 省份中心点 */
    private String provinceCenter;

    /** 省份几何信息 */
    private String provinceGeometry;

    /** 省份几何信息压缩缓存 */
    private transient String provinceGeometryCompressed;

    /** 城市名称 */
    private String cityNm;

    /** 城市编码 */
    private String cityAdcode;

    /** 城市中心点 */
    private String cityCenter;

    /** 城市几何信息 */
    private String cityGeometry;

    /** 城市几何信息压缩缓存 */
    private transient String cityGeometryCompressed;

    /** 区县名称 */
    private String districtNm;

    /** 区县编码 */
    private String districtAdcode;

    /** 区县中心点 */
    private String districtCenter;

    /** 区县几何信息 */
    private String districtGeometry;

    /** 区县几何信息压缩缓存 */
    private transient String districtGeometryCompressed;

    /** 燃料类型 */
    private String epaType;

    /** 车辆类型 */
    private String vehicleType;

    /** 发动机型号 */
    private String engineModel;

    /** 发动机制造商名称 */
    private String engineManufacturerName;

    /** 整车制造商名称 */
    private String manufacturerBuildName;

    /** 车辆载重 */
    private String vehicleLoads;

    /** 在线车辆数 */
    private Integer onlineCars;
    
    /** 昨日在线车辆数 */
    private Integer onlineCarsYesterday;
    
    /** 同比增长率 */
    private Double growthRate;

    /** 统计日期 */
    private String ptDate;

    public String getProvinceNm() {
        return provinceNm;
    }

    public void setProvinceNm(String provinceNm) {
        this.provinceNm = provinceNm;
    }

    public String getProvinceAdcode() {
        return provinceAdcode;
    }

    public void setProvinceAdcode(String provinceAdcode) {
        this.provinceAdcode = provinceAdcode;
    }

    public String getProvinceCenter() {
        return provinceCenter;
    }

    public void setProvinceCenter(String provinceCenter) {
        this.provinceCenter = provinceCenter;
    }

    @JsonRawValue
    public String getProvinceGeometry() {
        if (provinceGeometry == null) {
            return null;
        }

        // 使用静态缓存避免重复压缩相同的几何数据
        String cacheKey = "province_" + provinceGeometry.hashCode();
        String compressed = GEOMETRY_CACHE.get(cacheKey);
        if (compressed == null) {
            compressed = GeometryCompressionUtil.compressGeometry(provinceGeometry);
            GEOMETRY_CACHE.put(cacheKey, compressed);
        }
        return compressed;
    }

    public void setProvinceGeometry(String provinceGeometry) {
        this.provinceGeometry = provinceGeometry;
    }

    public String getCityNm() {
        return cityNm;
    }

    public void setCityNm(String cityNm) {
        this.cityNm = cityNm;
    }

    public String getCityAdcode() {
        return cityAdcode;
    }

    public void setCityAdcode(String cityAdcode) {
        this.cityAdcode = cityAdcode;
    }

    public String getCityCenter() {
        return cityCenter;
    }

    public void setCityCenter(String cityCenter) {
        this.cityCenter = cityCenter;
    }

    @JsonRawValue
    public String getCityGeometry() {
        if (cityGeometry == null) {
            return null;
        }

        // 使用静态缓存避免重复压缩相同的几何数据
        String cacheKey = "city_" + cityGeometry.hashCode();
        String compressed = GEOMETRY_CACHE.get(cacheKey);
        if (compressed == null) {
            compressed = GeometryCompressionUtil.compressGeometry(cityGeometry);
            GEOMETRY_CACHE.put(cacheKey, compressed);
        }
        return compressed;
    }

    public void setCityGeometry(String cityGeometry) {
        this.cityGeometry = cityGeometry;
    }

    public String getDistrictNm() {
        return districtNm;
    }

    public void setDistrictNm(String districtNm) {
        this.districtNm = districtNm;
    }

    public String getDistrictAdcode() {
        return districtAdcode;
    }

    public void setDistrictAdcode(String districtAdcode) {
        this.districtAdcode = districtAdcode;
    }

    public String getDistrictCenter() {
        return districtCenter;
    }

    public void setDistrictCenter(String districtCenter) {
        this.districtCenter = districtCenter;
    }

    @JsonRawValue
    public String getDistrictGeometry() {
        if (districtGeometry == null) {
            return null;
        }

        // 使用静态缓存避免重复压缩相同的几何数据
        String cacheKey = "district_" + districtGeometry.hashCode();
        String compressed = GEOMETRY_CACHE.get(cacheKey);
        if (compressed == null) {
            compressed = GeometryCompressionUtil.compressGeometry(districtGeometry);
            GEOMETRY_CACHE.put(cacheKey, compressed);
        }
        return compressed;
    }

    public void setDistrictGeometry(String districtGeometry) {
        this.districtGeometry = districtGeometry;
    }

    public String getEpaType() {
        return epaType;
    }

    public void setEpaType(String epaType) {
        this.epaType = epaType;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel;
    }

    public String getEngineManufacturerName() {
        return engineManufacturerName;
    }

    public void setEngineManufacturerName(String engineManufacturerName) {
        this.engineManufacturerName = engineManufacturerName;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getVehicleLoads() {
        return vehicleLoads;
    }

    public void setVehicleLoads(String vehicleLoads) {
        this.vehicleLoads = vehicleLoads;
    }

    public Integer getOnlineCars() {
        return onlineCars;
    }

    public void setOnlineCars(Integer onlineCars) {
        this.onlineCars = onlineCars;
    }
    
    public Integer getOnlineCarsYesterday() {
        return onlineCarsYesterday;
    }

    public void setOnlineCarsYesterday(Integer onlineCarsYesterday) {
        this.onlineCarsYesterday = onlineCarsYesterday;
    }
    
    public Double getGrowthRate() {
        return growthRate;
    }

    public void setGrowthRate(Double growthRate) {
        this.growthRate = growthRate;
    }

    public String getPtDate() {
        return ptDate;
    }

    public void setPtDate(String ptDate) {
        this.ptDate = ptDate;
    }
}