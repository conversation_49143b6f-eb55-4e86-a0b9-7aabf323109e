package com.portal.shiyunhui_daily_report.domain;

/**
 * 热点时段车辆数实体类
 *
 * <AUTHOR>
 */
public class VehicleHotHour {
    /** 省份名称 */
    private String provinceNm;

    /** 省份编码 */
    private String provinceAdcode;

    /** 城市名称 */
    private String cityNm;

    /** 城市编码 */
    private String cityAdcode;

    /** 区县名称 */
    private String districtNm;

    /** 区县编码 */
    private String districtAdcode;

    /** 燃料类型 */
    private String epaType;

    /** 车辆类型 */
    private String vehicleType;

    /** 发动机型号 */
    private String engineModel;

    /** 发动机制造商名称 */
    private String engineManufacturerName;

    /** 整车制造商名称 */
    private String manufacturerBuildName;

    /** 车辆载重 */
    private String vehicleLoads;

    /** 小时（0-23） */
    private Integer hour;

    /** 在线车辆数 */
    private Integer onlineCars;

    /** 统计日期 */
    private String ptDate;

    public String getProvinceNm() {
        return provinceNm;
    }

    public void setProvinceNm(String provinceNm) {
        this.provinceNm = provinceNm;
    }

    public String getProvinceAdcode() {
        return provinceAdcode;
    }

    public void setProvinceAdcode(String provinceAdcode) {
        this.provinceAdcode = provinceAdcode;
    }

    public String getCityNm() {
        return cityNm;
    }

    public void setCityNm(String cityNm) {
        this.cityNm = cityNm;
    }

    public String getCityAdcode() {
        return cityAdcode;
    }

    public void setCityAdcode(String cityAdcode) {
        this.cityAdcode = cityAdcode;
    }

    public String getDistrictNm() {
        return districtNm;
    }

    public void setDistrictNm(String districtNm) {
        this.districtNm = districtNm;
    }

    public String getDistrictAdcode() {
        return districtAdcode;
    }

    public void setDistrictAdcode(String districtAdcode) {
        this.districtAdcode = districtAdcode;
    }

    public String getEpaType() {
        return epaType;
    }

    public void setEpaType(String epaType) {
        this.epaType = epaType;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel;
    }

    public String getEngineManufacturerName() {
        return engineManufacturerName;
    }

    public void setEngineManufacturerName(String engineManufacturerName) {
        this.engineManufacturerName = engineManufacturerName;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getVehicleLoads() {
        return vehicleLoads;
    }

    public void setVehicleLoads(String vehicleLoads) {
        this.vehicleLoads = vehicleLoads;
    }

    public Integer getHour() {
        return hour;
    }

    public void setHour(Integer hour) {
        this.hour = hour;
    }

    public Integer getOnlineCars() {
        return onlineCars;
    }

    public void setOnlineCars(Integer onlineCars) {
        this.onlineCars = onlineCars;
    }

    public String getPtDate() {
        return ptDate;
    }

    public void setPtDate(String ptDate) {
        this.ptDate = ptDate;
    }
} 