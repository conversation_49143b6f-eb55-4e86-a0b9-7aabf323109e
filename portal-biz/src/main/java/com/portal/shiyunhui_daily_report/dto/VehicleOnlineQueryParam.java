package com.portal.shiyunhui_daily_report.dto;

/**
 * 车辆上线统计查询参数
 * 
 * <AUTHOR>
 */
public class VehicleOnlineQueryParam {
    /** 省份编码 */
    private String provinceAdcode;
    
    /** 省份名称 */
    private String provinceNm;
    
    /** 省份中心点 */
    private String provinceCenter;
    
    /** 省份几何信息 */
    private String provinceGeometry;
    
    /** 城市编码 */
    private String cityAdcode;
    
    /** 城市名称 */
    private String cityNm;
    
    /** 城市中心点 */
    private String cityCenter;
    
    /** 城市几何信息 */
    private String cityGeometry;
    
    /** 区县编码 */
    private String districtAdcode;
    
    /** 区县名称 */
    private String districtNm;
    
    /** 区县中心点 */
    private String districtCenter;
    
    /** 区县几何信息 */
    private String districtGeometry;
    
    /** 燃料类型 */
    private String epaType;
    
    /** 车辆类型 */
    private String vehicleType;
    
    /** 发动机型号 */
    private String engineModel;
    
    /** 发动机制造商名称 */
    private String engineManufacturerName;
    
    /** 整车制造商名称 */
    private String manufacturerBuildName;
    
    /** 车辆载重 */
    private String vehicleLoads;
    
    /** 统计日期（格式：yyyy-MM-dd） */
    private String ptDate;

    /** 维度类型（用于维度值查询：epaType, vehicleType, engineModel, engineManufacturerName, manufacturerBuildName, vehicleLoads） */
    private String dimensionType;

    /** 是否导出当前页 */
    private Boolean exportCurrentPage;

    /** 页码 */
    private Integer pageNum;

    /** 页大小 */
    private Integer pageSize;

    public String getProvinceAdcode() {
        return provinceAdcode;
    }

    public void setProvinceAdcode(String provinceAdcode) {
        this.provinceAdcode = provinceAdcode;
    }
    
    public String getProvinceNm() {
        return provinceNm;
    }

    public void setProvinceNm(String provinceNm) {
        this.provinceNm = provinceNm;
    }

    public String getProvinceCenter() {
        return provinceCenter;
    }

    public void setProvinceCenter(String provinceCenter) {
        this.provinceCenter = provinceCenter;
    }

    public String getProvinceGeometry() {
        return provinceGeometry;
    }

    public void setProvinceGeometry(String provinceGeometry) {
        this.provinceGeometry = provinceGeometry;
    }

    public String getCityAdcode() {
        return cityAdcode;
    }

    public void setCityAdcode(String cityAdcode) {
        this.cityAdcode = cityAdcode;
    }
    
    public String getCityNm() {
        return cityNm;
    }

    public void setCityNm(String cityNm) {
        this.cityNm = cityNm;
    }

    public String getCityCenter() {
        return cityCenter;
    }

    public void setCityCenter(String cityCenter) {
        this.cityCenter = cityCenter;
    }

    public String getCityGeometry() {
        return cityGeometry;
    }

    public void setCityGeometry(String cityGeometry) {
        this.cityGeometry = cityGeometry;
    }

    public String getDistrictAdcode() {
        return districtAdcode;
    }

    public void setDistrictAdcode(String districtAdcode) {
        this.districtAdcode = districtAdcode;
    }

    public String getDistrictNm() {
        return districtNm;
    }

    public void setDistrictNm(String districtNm) {
        this.districtNm = districtNm;
    }

    public String getDistrictCenter() {
        return districtCenter;
    }

    public void setDistrictCenter(String districtCenter) {
        this.districtCenter = districtCenter;
    }

    public String getDistrictGeometry() {
        return districtGeometry;
    }

    public void setDistrictGeometry(String districtGeometry) {
        this.districtGeometry = districtGeometry;
    }

    public String getEpaType() {
        return epaType;
    }

    public void setEpaType(String epaType) {
        this.epaType = epaType;
    }
    
    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel;
    }

    public String getEngineManufacturerName() {
        return engineManufacturerName;
    }

    public void setEngineManufacturerName(String engineManufacturerName) {
        this.engineManufacturerName = engineManufacturerName;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getVehicleLoads() {
        return vehicleLoads;
    }

    public void setVehicleLoads(String vehicleLoads) {
        this.vehicleLoads = vehicleLoads;
    }

    public String getPtDate() {
        return ptDate;
    }

    public void setPtDate(String ptDate) {
        this.ptDate = ptDate;
    }

    public String getDimensionType() {
        return dimensionType;
    }

    public void setDimensionType(String dimensionType) {
        this.dimensionType = dimensionType;
    }

    public Boolean getExportCurrentPage() {
        return exportCurrentPage;
    }

    public void setExportCurrentPage(Boolean exportCurrentPage) {
        this.exportCurrentPage = exportCurrentPage;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}