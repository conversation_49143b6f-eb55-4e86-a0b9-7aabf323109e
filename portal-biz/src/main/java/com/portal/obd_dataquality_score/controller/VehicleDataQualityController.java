package com.portal.obd_dataquality_score.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.portal.common.annotation.Log;
import com.portal.common.core.controller.BaseController;
import com.portal.common.core.domain.AjaxResult;
import com.portal.common.core.page.TableDataInfo;
import com.portal.common.enums.BusinessType;
import com.portal.common.utils.StringUtils;
import com.portal.common.utils.poi.ExcelUtil;
import com.portal.common.utils.sql.SqlUtil;
import com.portal.obd_dataquality_score.domain.DataExecption;
import com.portal.obd_dataquality_score.domain.DataLossrate;
import com.portal.obd_dataquality_score.domain.DataOutboundaryRate;
import com.portal.obd_dataquality_score.domain.DieselVinLossrateList;
import com.portal.obd_dataquality_score.domain.DieselVinOutboundaryList;
import com.portal.obd_dataquality_score.domain.GasVinLossrateList;
import com.portal.obd_dataquality_score.domain.GasVinOutboundaryList;
import com.portal.obd_dataquality_score.domain.TransDataLossrate;
import com.portal.obd_dataquality_score.domain.VehicleDataSummary;
import com.portal.obd_dataquality_score.dto.VehicleDataQueryParam;
import com.portal.obd_dataquality_score.service.IVehicleDataQualityService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 车辆数据质量控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dataquality_score")
public class VehicleDataQualityController extends BaseController {
    
    @Autowired
    private IVehicleDataQualityService vehicleDataQualityService;
    
    /**
     * 获取车辆数据质量摘要信息
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:online_info')")
    @GetMapping("/online_info")
    public AjaxResult getVehicleDataSummary(VehicleDataQueryParam queryParam) {
        List<VehicleDataSummary> list = vehicleDataQualityService.getVehicleDataSummary(queryParam);
        return AjaxResult.success(list);
    }
    
    /**
     * 按照车辆类型分组获取车辆数据质量摘要信息
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:online_info_byVtype')")
    @GetMapping("/online_info_byVtype")
    public AjaxResult getVehicleDataSummaryByVtype(VehicleDataQueryParam queryParam) {
        List<VehicleDataSummary> list = vehicleDataQualityService.getVehicleDataSummaryByVtype(queryParam);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取数据越界率指标数据
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:outboundary_rate')")
    @GetMapping("/outboundary_rate")
    public AjaxResult getEngineIndicatorData(VehicleDataQueryParam queryParam) {
        List<DataOutboundaryRate> list = vehicleDataQualityService.getEngineIndicatorData(queryParam);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取数据缺失率指标数据
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:lossrate_rate')")
    @GetMapping("/lossrate_rate")
    public AjaxResult getEngineLossRateData(VehicleDataQueryParam queryParam) {
        List<DataLossrate> list = vehicleDataQualityService.getEngineLossRateData(queryParam);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取传输数据缺失率指标数据
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:trans_outboundary_rate')")
    @GetMapping("/trans_outboundary_rate")
    public AjaxResult getTransDataLossrate(VehicleDataQueryParam queryParam) {
        List<TransDataLossrate> list = vehicleDataQualityService.getTransDataLossrate(queryParam);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取数据异常率指标数据
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:data_exception')")
    @GetMapping("/data_exception")
    public AjaxResult getDataExecption(VehicleDataQueryParam queryParam) {
        List<DataExecption> list = vehicleDataQualityService.getDataExecption(queryParam);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取VIN码的数据越界率指标数据（根据epaVehicleType判断柴油或燃气车型）
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:diesel_vin_outboundary')")
    @GetMapping("/diesel_vin_outboundary")
    public TableDataInfo getDieselVinOutboundaryList(VehicleDataQueryParam queryParam) {
        // 使用自定义分页，不进行字段名转换
        startOrderByWithoutConvert();
        startPage();
        // 如果是柴油车型（epaVehicleType为1或4）或未指定类型，则查询柴油车型数据
        if (queryParam.getEpaVehicleType() == null || "1".equals(queryParam.getEpaVehicleType()) || "4".equals(queryParam.getEpaVehicleType())) {
            // 确保传入柴油车型参数
            if (queryParam.getEpaVehicleType() == null) {
                queryParam.setEpaVehicleType("1");
            }
            List<DieselVinOutboundaryList> list = vehicleDataQualityService.getDieselVinOutboundaryList(queryParam);
            return getDataTable(list);
        } else {
            // 其他情况返回空数据
            return getDataTable(null);
        }
    }
    
    /**
     * 导出VIN码的数据越界率指标数据（根据epaVehicleType判断柴油或燃气车型）
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:diesel_vin_outboundary:export')")
    @Log(title = "柴油车型VIN码的数据越界率", businessType = BusinessType.EXPORT)
    @PostMapping("/diesel_vin_outboundary/export")
    public void exportDieselVinOutboundaryList(HttpServletResponse response, @RequestBody(required = false) VehicleDataQueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new VehicleDataQueryParam();
            queryParam.setEpaVehicleType("1"); // 默认为柴油车型
        } else if (queryParam.getEpaVehicleType() == null) {
            queryParam.setEpaVehicleType("1"); // 默认为柴油车型
        }
        
        // 如果是柴油车型（epaVehicleType为1或4），则查询柴油车型数据
        if ("1".equals(queryParam.getEpaVehicleType()) || "4".equals(queryParam.getEpaVehicleType())) {
            List<DieselVinOutboundaryList> list;
            
            // 判断是否导出当前页
            if (Boolean.TRUE.equals(queryParam.getExportCurrentPage())) {
                // 使用前端传递的分页参数
                Integer pageNum = queryParam.getPageNum() != null ? queryParam.getPageNum() : 1;
                Integer pageSize = queryParam.getPageSize() != null ? queryParam.getPageSize() : 10;
                
                // 使用请求体中的排序参数
                if (StringUtils.isNotEmpty(queryParam.getOrderByColumn())) {
                    String orderBy = queryParam.getOrderByColumn() + " " + (StringUtils.isNotEmpty(queryParam.getIsAsc()) ? queryParam.getIsAsc() : "asc");
                    PageHelper.startPage(pageNum, pageSize).setOrderBy(SqlUtil.escapeOrderBySql(orderBy));
                } else {
                    PageHelper.startPage(pageNum, pageSize);
                }
                
                list = vehicleDataQualityService.getDieselVinOutboundaryList(queryParam);
                // 获取当前页数据
                list = new PageInfo<>(list).getList();
            } else {
                // 导出全部数据
                list = vehicleDataQualityService.getDieselVinOutboundaryList(queryParam);
            }
            
            ExcelUtil<DieselVinOutboundaryList> util = new ExcelUtil<DieselVinOutboundaryList>(DieselVinOutboundaryList.class);
            util.exportExcel(response, list, "柴油车型VIN码的数据越界率");
        }   
    }
    
    /**
     * 获取VIN码的数据越界率指标数据（根据epaVehicleType判断柴油或燃气车型）
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:gas_vin_outboundary')")
    @GetMapping("/gas_vin_outboundary")
    public TableDataInfo getGasVinOutboundaryList(VehicleDataQueryParam queryParam) {
        // 使用自定义分页，不进行字段名转换
        startOrderByWithoutConvert();
        startPage();
        // 如果是燃气车型（epaVehicleType为2或5）或未指定类型，则查询燃气车型数据
        if (queryParam.getEpaVehicleType() == null || "2".equals(queryParam.getEpaVehicleType()) || "5".equals(queryParam.getEpaVehicleType())) {
            // 确保传入燃气车型参数
            if (queryParam.getEpaVehicleType() == null) {
                queryParam.setEpaVehicleType("2");
            }
            List<GasVinOutboundaryList> list = vehicleDataQualityService.getGasVinOutboundaryList(queryParam);
            return getDataTable(list);
        } else {
            // 其他情况返回空数据
            return getDataTable(null);
        }
    }
    
    /**
     * 导出VIN码的数据越界率指标数据（根据epaVehicleType判断柴油或燃气车型）
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:gas_vin_outboundary:export')")
    @Log(title = "燃气车型VIN码的数据越界率", businessType = BusinessType.EXPORT)
    @PostMapping("/gas_vin_outboundary/export")
    public void exportGasVinOutboundaryList(HttpServletResponse response, @RequestBody(required = false) VehicleDataQueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new VehicleDataQueryParam();
            queryParam.setEpaVehicleType("2"); // 默认为燃气车型
        } else if (queryParam.getEpaVehicleType() == null) {
            queryParam.setEpaVehicleType("2"); // 默认为燃气车型
        }
        
        // 如果是燃气车型（epaVehicleType为2或5），则查询燃气车型数据
        if ("2".equals(queryParam.getEpaVehicleType()) || "5".equals(queryParam.getEpaVehicleType())) {
            List<GasVinOutboundaryList> list;
            
            // 判断是否导出当前页
            if (Boolean.TRUE.equals(queryParam.getExportCurrentPage())) {
                // 使用前端传递的分页参数
                Integer pageNum = queryParam.getPageNum() != null ? queryParam.getPageNum() : 1;
                Integer pageSize = queryParam.getPageSize() != null ? queryParam.getPageSize() : 10;
                
                // 使用请求体中的排序参数
                if (StringUtils.isNotEmpty(queryParam.getOrderByColumn())) {
                    String orderBy = queryParam.getOrderByColumn() + " " + (StringUtils.isNotEmpty(queryParam.getIsAsc()) ? queryParam.getIsAsc() : "asc");
                    PageHelper.startPage(pageNum, pageSize).setOrderBy(SqlUtil.escapeOrderBySql(orderBy));
                } else {
                    PageHelper.startPage(pageNum, pageSize);
                }
                
                list = vehicleDataQualityService.getGasVinOutboundaryList(queryParam);
                // 获取当前页数据
                list = new PageInfo<>(list).getList();
            } else {
                // 导出全部数据
                list = vehicleDataQualityService.getGasVinOutboundaryList(queryParam);
            }
            
            ExcelUtil<GasVinOutboundaryList> util = new ExcelUtil<GasVinOutboundaryList>(GasVinOutboundaryList.class);
            util.exportExcel(response, list, "燃气车型VIN码的数据越界率");
        }
    }
    
    /**
     * 获取VIN码的数据缺失率指标数据（根据epaVehicleType判断柴油或燃气车型）
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:diesel_vin_lossrate')")
    @GetMapping("/diesel_vin_lossrate")
    public TableDataInfo getDieselVinLossrateList(VehicleDataQueryParam queryParam) {
        // 使用自定义分页，不进行字段名转换
        startOrderByWithoutConvert();
        startPage();
        // 如果是柴油车型（epaVehicleType为1或4）或未指定类型，则查询柴油车型数据
        if (queryParam.getEpaVehicleType() == null || "1".equals(queryParam.getEpaVehicleType()) || "4".equals(queryParam.getEpaVehicleType())) {
            if (queryParam.getEpaVehicleType() == null) {
                queryParam.setEpaVehicleType("1");
            }
            List<DieselVinLossrateList> list = vehicleDataQualityService.getDieselVinLossrateList(queryParam);
            return getDataTable(list);
        } else {
            // 其他情况返回空数据
            return getDataTable(null);
        }
    }
    
    /**
     * 导出VIN码的数据缺失率指标数据（根据epaVehicleType判断柴油或燃气车型）
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:diesel_vin_lossrate:export')")
    @Log(title = "柴油车型VIN码的数据缺失率", businessType = BusinessType.EXPORT)
    @PostMapping("/diesel_vin_lossrate/export")
    public void exportDieselVinLossrateList(HttpServletResponse response, @RequestBody(required = false) VehicleDataQueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new VehicleDataQueryParam();
            queryParam.setEpaVehicleType("1"); // 默认为柴油车型
        } else if (queryParam.getEpaVehicleType() == null) {
            queryParam.setEpaVehicleType("1"); // 默认为柴油车型
        }
        
        // 如果是柴油车型（epaVehicleType为1或4），则查询柴油车型数据
        if ("1".equals(queryParam.getEpaVehicleType()) || "4".equals(queryParam.getEpaVehicleType())) {
            List<DieselVinLossrateList> list;
            
            // 判断是否导出当前页
            if (Boolean.TRUE.equals(queryParam.getExportCurrentPage())) {
                // 使用前端传递的分页参数
                Integer pageNum = queryParam.getPageNum() != null ? queryParam.getPageNum() : 1;
                Integer pageSize = queryParam.getPageSize() != null ? queryParam.getPageSize() : 10;
                
                // 使用请求体中的排序参数
                if (StringUtils.isNotEmpty(queryParam.getOrderByColumn())) {
                    String orderBy = queryParam.getOrderByColumn() + " " + (StringUtils.isNotEmpty(queryParam.getIsAsc()) ? queryParam.getIsAsc() : "asc");
                    PageHelper.startPage(pageNum, pageSize).setOrderBy(SqlUtil.escapeOrderBySql(orderBy));
                } else {
                    PageHelper.startPage(pageNum, pageSize);
                }
                
                list = vehicleDataQualityService.getDieselVinLossrateList(queryParam);
                // 获取当前页数据
                list = new PageInfo<>(list).getList();
            } else {
                // 导出全部数据
                list = vehicleDataQualityService.getDieselVinLossrateList(queryParam);
            }
            
            ExcelUtil<DieselVinLossrateList> util = new ExcelUtil<DieselVinLossrateList>(DieselVinLossrateList.class);
            util.exportExcel(response, list, "柴油车型VIN码的数据缺失率");
        }
    }
    
    /**
     * 获取VIN码的数据缺失率指标数据（根据epaVehicleType判断柴油或燃气车型）
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:gas_vin_lossrate')")
    @GetMapping("/gas_vin_lossrate")
    public TableDataInfo getGasVinLossrateList(VehicleDataQueryParam queryParam) {
        // 使用自定义分页，不进行字段名转换
        startOrderByWithoutConvert();
        startPage();
        // 如果是燃气车型（epaVehicleType为2或5）或未指定类型，则查询燃气车型数据
        if (queryParam.getEpaVehicleType() == null || "2".equals(queryParam.getEpaVehicleType()) || "5".equals(queryParam.getEpaVehicleType())) {
            if (queryParam.getEpaVehicleType() == null) {
                queryParam.setEpaVehicleType("2");
            }
            List<GasVinLossrateList> list = vehicleDataQualityService.getGasVinLossrateList(queryParam);
            return getDataTable(list);
        } else {
            // 其他情况返回空数据
            return getDataTable(null);
        }
    }
    
    /**
     * 导出VIN码的数据缺失率指标数据（根据epaVehicleType判断柴油或燃气车型）
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:gas_vin_lossrate:export')")
    @Log(title = "燃气车型VIN码的数据缺失率", businessType = BusinessType.EXPORT)
    @PostMapping("/gas_vin_lossrate/export")
    public void exportGasVinLossrateList(HttpServletResponse response, @RequestBody(required = false) VehicleDataQueryParam queryParam) {
        if (queryParam == null) {
            queryParam = new VehicleDataQueryParam();
            queryParam.setEpaVehicleType("2"); // 默认为燃气车型
        } else if (queryParam.getEpaVehicleType() == null) {
            queryParam.setEpaVehicleType("2"); // 默认为燃气车型
        }
        
        // 如果是燃气车型（epaVehicleType为2或5），则查询燃气车型数据
        if ("2".equals(queryParam.getEpaVehicleType()) || "5".equals(queryParam.getEpaVehicleType())) {
            List<GasVinLossrateList> list;
            
            // 判断是否导出当前页
            if (Boolean.TRUE.equals(queryParam.getExportCurrentPage())) {
                // 使用前端传递的分页参数
                Integer pageNum = queryParam.getPageNum() != null ? queryParam.getPageNum() : 1;
                Integer pageSize = queryParam.getPageSize() != null ? queryParam.getPageSize() : 10;
                
                // 使用请求体中的排序参数
                if (StringUtils.isNotEmpty(queryParam.getOrderByColumn())) {
                    String orderBy = queryParam.getOrderByColumn() + " " + (StringUtils.isNotEmpty(queryParam.getIsAsc()) ? queryParam.getIsAsc() : "asc");
                    PageHelper.startPage(pageNum, pageSize).setOrderBy(SqlUtil.escapeOrderBySql(orderBy));
                } else {
                    PageHelper.startPage(pageNum, pageSize);
                }
                
                list = vehicleDataQualityService.getGasVinLossrateList(queryParam);
                // 获取当前页数据
                list = new PageInfo<>(list).getList();
            } else {
                // 导出全部数据
                list = vehicleDataQualityService.getGasVinLossrateList(queryParam);
            }
            
            ExcelUtil<GasVinLossrateList> util = new ExcelUtil<GasVinLossrateList>(GasVinLossrateList.class);
            util.exportExcel(response, list, "燃气车型VIN码的数据缺失率");
        }
    }
    
    /**
     * 获取企业名称列表
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:manufacturer_list')")
    @GetMapping("/manufacturer_list")
    public AjaxResult getManufacturerBuildNameList() {
        List<String> list = vehicleDataQualityService.getManufacturerBuildNameList();
        return AjaxResult.success(list);
    }
    
    /**
     * 获取发动机生产厂家列表
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:engine_manufacturer_list')")
    @GetMapping("/engine_manufacturer_list")
    public AjaxResult getEngineManufacturerNameList() {
        List<String> list = vehicleDataQualityService.getEngineManufacturerNameList();
        return AjaxResult.success(list);
    }
    
    /**
     * 获取发动机型号列表
     */
    @PreAuthorize("@ss.hasPermi('dataquality_score:engine_model_list')")
    @GetMapping("/engine_model_list")
    public AjaxResult getEngineModelList(@RequestParam String engineManufacturerName) {
        List<String> list = vehicleDataQualityService.getEngineModelList(engineManufacturerName);
        return AjaxResult.success(list);
    }
} 