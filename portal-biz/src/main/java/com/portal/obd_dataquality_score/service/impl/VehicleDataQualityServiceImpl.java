package com.portal.obd_dataquality_score.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.portal.common.annotation.DataSource;
import com.portal.common.enums.DataSourceType;
import com.portal.obd_dataquality_score.domain.DataExecption;
import com.portal.obd_dataquality_score.domain.DataLossrate;
import com.portal.obd_dataquality_score.domain.DataOutboundaryRate;
import com.portal.obd_dataquality_score.domain.DieselVinLossrateList;
import com.portal.obd_dataquality_score.domain.DieselVinOutboundaryList;
import com.portal.obd_dataquality_score.domain.GasVinLossrateList;
import com.portal.obd_dataquality_score.domain.GasVinOutboundaryList;
import com.portal.obd_dataquality_score.domain.TransDataLossrate;
import com.portal.obd_dataquality_score.domain.VehicleDataSummary;
import com.portal.obd_dataquality_score.dto.VehicleDataQueryParam;
import com.portal.obd_dataquality_score.mapper.VehicleDataQualityMapper;
import com.portal.obd_dataquality_score.service.IVehicleDataQualityService;

/**
 * 车辆数据质量服务实现
 * 
 * <AUTHOR>
 */
@Service
public class VehicleDataQualityServiceImpl implements IVehicleDataQualityService {

    @Autowired
    private VehicleDataQualityMapper vehicleDataQualityMapper;

    /**
     * 获取车辆数据质量摘要信息
     * 
     * @param queryParam 查询参数，包含月份、企业名称等条件
     * @return 车辆数据摘要列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleDataSummary> getVehicleDataSummary(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getVehicleDataSummary(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getEpaVehicleType(),
                queryParam.getActualAgreement(),
                queryParam.getEngineManufacturerName(),
                queryParam.getEngineModel()
        );
    }
    
    /**
     * 按照车辆类型分组获取车辆数据质量摘要信息
     * 
     * @param queryParam 查询参数，包含月份、企业名称等条件
     * @return 按车辆类型分组的车辆数据摘要列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<VehicleDataSummary> getVehicleDataSummaryByVtype(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getVehicleDataSummaryByVtype(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getActualAgreement()
        );
    }
    
    /**
     * 获取数据越界率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型（可选）
     * @param actualAgreement 协议类型（可选）
     * @return 数据越界率指标数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<DataOutboundaryRate> getEngineIndicatorData(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getEngineIndicatorData(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getEpaVehicleType(),
                queryParam.getActualAgreement(),
                queryParam.getEngineManufacturerName(),
                queryParam.getEngineModel()
        );
    }
    
    /**
     * 获取数据缺失率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型（可选）
     * @param actualAgreement 协议类型（可选）
     * @return 数据缺失率指标数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<DataLossrate> getEngineLossRateData(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getEngineLossRateData(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getEpaVehicleType(),
                queryParam.getActualAgreement(),
                queryParam.getEngineManufacturerName(),
                queryParam.getEngineModel()
        );
    }
    
    /**
     * 获取传输数据缺失率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型
     * @return 传输数据缺失率指标数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<TransDataLossrate> getTransDataLossrate(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getTransDataLossrate(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getEpaVehicleType(),
                queryParam.getActualAgreement(),
                queryParam.getEngineManufacturerName(),
                queryParam.getEngineModel()
        );
    }

    /**
     * 获取数据异常率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型
     * @return 数据异常率指标数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<DataExecption> getDataExecption(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getDataExecption(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getEpaVehicleType(),
                queryParam.getActualAgreement(),
                queryParam.getEngineManufacturerName(),
                queryParam.getEngineModel()
        );
    }
    
    /**
     * 获取企业名称列表
     * 
     * @return 企业名称列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<String> getManufacturerBuildNameList() {
        return vehicleDataQualityMapper.getManufacturerBuildNameList();
    }
    
    /**
     * 获取发动机生产厂家列表
     * 
     * @return 发动机生产厂家列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<String> getEngineManufacturerNameList() {
        return vehicleDataQualityMapper.getEngineManufacturerNameList();
    }
    
    /**
     * 获取发动机型号列表
     * 
     * @param engineManufacturerName 发动机生产厂家
     * @return 发动机型号列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<String> getEngineModelList(String engineManufacturerName) {
        return vehicleDataQualityMapper.getEngineModelList(engineManufacturerName);
    }

    /**
     * 获取柴油车型VIN码的数据越界率指标数据
     * 
     * @param queryParam 查询参数
     * @return 柴油车型VIN码的数据越界率指标数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<DieselVinOutboundaryList> getDieselVinOutboundaryList(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getDieselVinOutboundaryList(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getEpaVehicleType(),
                queryParam.getActualAgreement(),
                queryParam.getEngineManufacturerName(),
                queryParam.getEngineModel(),
                queryParam.getVin()
        );
    }
    
    /**
     * 获取燃气车型VIN码的数据越界率指标数据
     * 
     * @param queryParam 查询参数
     * @return 燃气车型VIN码的数据越界率指标数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<GasVinOutboundaryList> getGasVinOutboundaryList(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getGasVinOutboundaryList(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getEpaVehicleType(),
                queryParam.getActualAgreement(),
                queryParam.getEngineManufacturerName(),
                queryParam.getEngineModel(),
                queryParam.getVin()
        );
    }
    
    /**
     * 获取柴油车型VIN码的数据缺失率指标数据
     * 
     * @param queryParam 查询参数
     * @return 柴油车型VIN码的数据缺失率指标数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<DieselVinLossrateList> getDieselVinLossrateList(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getDieselVinLossrateList(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getEpaVehicleType(),
                queryParam.getActualAgreement(),
                queryParam.getEngineManufacturerName(),
                queryParam.getEngineModel(),
                queryParam.getVin()
        );
    }
    
    /**
     * 获取燃气车型VIN码的数据缺失率指标数据
     * 
     * @param queryParam 查询参数
     * @return 燃气车型VIN码的数据缺失率指标数据列表
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<GasVinLossrateList> getGasVinLossrateList(VehicleDataQueryParam queryParam) {
        return vehicleDataQualityMapper.getGasVinLossrateList(
                queryParam.getPtMonth(),
                queryParam.getManufacturerBuildName(),
                queryParam.getEpaVehicleType(),
                queryParam.getActualAgreement(),
                queryParam.getEngineManufacturerName(),
                queryParam.getEngineModel(),
                queryParam.getVin()
        );
    }
} 