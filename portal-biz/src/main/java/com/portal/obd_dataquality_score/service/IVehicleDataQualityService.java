package com.portal.obd_dataquality_score.service;

import java.util.List;

import com.portal.obd_dataquality_score.domain.DataExecption;
import com.portal.obd_dataquality_score.domain.DataLossrate;
import com.portal.obd_dataquality_score.domain.DataOutboundaryRate;
import com.portal.obd_dataquality_score.domain.DieselVinLossrateList;
import com.portal.obd_dataquality_score.domain.DieselVinOutboundaryList;
import com.portal.obd_dataquality_score.domain.GasVinLossrateList;
import com.portal.obd_dataquality_score.domain.GasVinOutboundaryList;
import com.portal.obd_dataquality_score.domain.TransDataLossrate;
import com.portal.obd_dataquality_score.domain.VehicleDataSummary;
import com.portal.obd_dataquality_score.dto.VehicleDataQueryParam;

/**
 * 车辆数据质量服务接口
 * 
 * <AUTHOR>
 */
public interface IVehicleDataQualityService {
    /**
     * 获取车辆数据质量摘要信息
     * 
     * @param queryParam 查询参数，包含月份、企业名称等条件
     * @return 车辆数据摘要列表
     */
    List<VehicleDataSummary> getVehicleDataSummary(VehicleDataQueryParam queryParam);
    
    /**
     * 按照车辆类型分组获取车辆数据质量摘要信息
     * 
     * @param queryParam 查询参数，包含月份、企业名称等条件
     * @return 按车辆类型分组的车辆数据摘要列表
     */
    List<VehicleDataSummary> getVehicleDataSummaryByVtype(VehicleDataQueryParam queryParam);
    
    /**
     * 获取数据越界率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型（可选）
     * @param actualAgreement 协议类型（可选）
     * @return 数据越界率指标数据列表
     */
    List<DataOutboundaryRate> getEngineIndicatorData(VehicleDataQueryParam queryParam);
    
    /**
     * 获取数据缺失率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型（可选）
     * @param actualAgreement 协议类型（可选）
     * @return 数据缺失率指标数据列表
     */
    List<DataLossrate> getEngineLossRateData(VehicleDataQueryParam queryParam);
    
    /**
     * 获取传输数据缺失率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型
     * @return 传输数据缺失率指标数据列表
     */
    List<TransDataLossrate> getTransDataLossrate(VehicleDataQueryParam queryParam);
    
    /**
     * 获取数据异常率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型
     * @return 数据异常率指标数据列表
     */
    List<DataExecption> getDataExecption(VehicleDataQueryParam queryParam);
    
    /**
     * 获取柴油车型VIN码的数据越界率指标数据
     * 
     * @param queryParam 查询参数
     * @return 柴油车型VIN码的数据越界率指标数据列表
     */
    List<DieselVinOutboundaryList> getDieselVinOutboundaryList(VehicleDataQueryParam queryParam);
    
    /**
     * 获取燃气车型VIN码的数据越界率指标数据
     * 
     * @param queryParam 查询参数
     * @return 燃气车型VIN码的数据越界率指标数据列表
     */
    List<GasVinOutboundaryList> getGasVinOutboundaryList(VehicleDataQueryParam queryParam);
    
    /**
     * 获取柴油车型VIN码的数据缺失率指标数据
     * 
     * @param queryParam 查询参数
     * @return 柴油车型VIN码的数据缺失率指标数据列表
     */
    List<DieselVinLossrateList> getDieselVinLossrateList(VehicleDataQueryParam queryParam);
    
    /**
     * 获取燃气车型VIN码的数据缺失率指标数据
     * 
     * @param queryParam 查询参数
     * @return 燃气车型VIN码的数据缺失率指标数据列表
     */
    List<GasVinLossrateList> getGasVinLossrateList(VehicleDataQueryParam queryParam);
    
    /**
     * 获取企业名称列表
     * 
     * @return 企业名称列表
     */
    List<String> getManufacturerBuildNameList();
    
    /**
     * 获取发动机生产厂家列表
     * 
     * @return 发动机生产厂家列表
     */
    List<String> getEngineManufacturerNameList();
    
    /**
     * 获取发动机型号列表
     * 
     * @param engineManufacturerName 发动机生产厂家
     * @return 发动机型号列表
     */
    List<String> getEngineModelList(String engineManufacturerName);
} 