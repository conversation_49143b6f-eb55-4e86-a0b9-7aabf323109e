package com.portal.obd_dataquality_score.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.portal.obd_dataquality_score.domain.DataExecption;
import com.portal.obd_dataquality_score.domain.DataLossrate;
import com.portal.obd_dataquality_score.domain.DataOutboundaryRate;
import com.portal.obd_dataquality_score.domain.DieselVinLossrateList;
import com.portal.obd_dataquality_score.domain.DieselVinOutboundaryList;
import com.portal.obd_dataquality_score.domain.GasVinLossrateList;
import com.portal.obd_dataquality_score.domain.GasVinOutboundaryList;
import com.portal.obd_dataquality_score.domain.TransDataLossrate;
import com.portal.obd_dataquality_score.domain.VehicleDataSummary;

/**
 * 车辆数据质量Mapper接口
 * 
 * <AUTHOR>
 */
public interface VehicleDataQualityMapper {
    /**
     * 获取车辆数据质量摘要信息
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型（可选）
     * @param actualAgreement 协议类型（可选）
     * @param engineManufacturerName 发动机生产厂家（可选）
     * @param engineModel 发动机型号（可选）
     * @return 车辆数据摘要列表
     */
    List<VehicleDataSummary> getVehicleDataSummary(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("epaVehicleType") String epaVehicleType,
        @Param("actualAgreement") String actualAgreement,
        @Param("engineManufacturerName") String engineManufacturerName,
        @Param("engineModel") String engineModel
    );
    
    /**
     * 按照车辆类型分组获取车辆数据质量摘要信息
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param actualAgreement 协议类型（可选）
     * @return 按车辆类型分组的车辆数据摘要列表
     */
    List<VehicleDataSummary> getVehicleDataSummaryByVtype(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("actualAgreement") String actualAgreement
    );
    
    /**
     * 获取数据越界率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型（可选）
     * @param actualAgreement 协议类型（可选）
     * @param engineManufacturerName 发动机生产厂家（可选）
     * @param engineModel 发动机型号（可选）
     * @return 数据越界率指标数据列表
     */
    List<DataOutboundaryRate> getEngineIndicatorData(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("epaVehicleType") String epaVehicleType,
        @Param("actualAgreement") String actualAgreement,
        @Param("engineManufacturerName") String engineManufacturerName,
        @Param("engineModel") String engineModel
    );
    
    /**
     * 获取数据缺失率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型（可选）
     * @param actualAgreement 协议类型（可选）  
     * @param engineManufacturerName 发动机生产厂家（可选）
     * @param engineModel 发动机型号（可选）
     * @return 数据缺失率指标数据列表
     */
    List<DataLossrate> getEngineLossRateData(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("epaVehicleType") String epaVehicleType,
        @Param("actualAgreement") String actualAgreement,
        @Param("engineManufacturerName") String engineManufacturerName,
        @Param("engineModel") String engineModel
    );
    
    /**
     * 获取传输数据缺失率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型
     * @param engineManufacturerName 发动机生产厂家（可选）
     * @param engineModel 发动机型号（可选）
     * @return 传输数据缺失率指标数据列表
     */
    List<TransDataLossrate> getTransDataLossrate(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("epaVehicleType") String epaVehicleType,
        @Param("actualAgreement") String actualAgreement,
        @Param("engineManufacturerName") String engineManufacturerName,
        @Param("engineModel") String engineModel
    );
    
    /**
     * 获取数据异常率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型
     * @param engineManufacturerName 发动机生产厂家（可选）
     * @param engineModel 发动机型号（可选）
     * @return 数据异常率指标数据列表
     */
    List<DataExecption> getDataExecption(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("epaVehicleType") String epaVehicleType,
        @Param("actualAgreement") String actualAgreement,
        @Param("engineManufacturerName") String engineManufacturerName,
        @Param("engineModel") String engineModel
    );
    
    /**
     * 获取企业名称列表
     * 
     * @return 企业名称列表
     */
    List<String> getManufacturerBuildNameList();
    
    /**
     * 获取发动机生产厂家列表
     * 
     * @return 发动机生产厂家列表
     */
    List<String> getEngineManufacturerNameList();
    
    /**
     * 获取发动机型号列表
     * 
     * @param engineManufacturerName 发动机生产厂家
     * @return 发动机型号列表
     */
    List<String> getEngineModelList(@Param("engineManufacturerName") String engineManufacturerName);
    
    /**
     * 获取柴油车型VIN码的数据越界率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型（可选）
     * @param engineManufacturerName 发动机生产厂家（可选）
     * @param engineModel 发动机型号（可选）
     * @param vin 车辆VIN码（可选）
     * @return 柴油车型VIN码的数据越界率指标数据列表
     */
    List<DieselVinOutboundaryList> getDieselVinOutboundaryList(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("epaVehicleType") String epaVehicleType,
        @Param("actualAgreement") String actualAgreement,
        @Param("engineManufacturerName") String engineManufacturerName,
        @Param("engineModel") String engineModel,
        @Param("vin") String vin
    );
    
    /**
     * 获取燃气车型VIN码的数据越界率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型（可选）
     * @param engineManufacturerName 发动机生产厂家（可选）
     * @param engineModel 发动机型号（可选）
     * @param vin 车辆VIN码（可选）
     * @return 燃气车型VIN码的数据越界率指标数据列表
     */
    List<GasVinOutboundaryList> getGasVinOutboundaryList(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("epaVehicleType") String epaVehicleType,
        @Param("actualAgreement") String actualAgreement,
        @Param("engineManufacturerName") String engineManufacturerName,
        @Param("engineModel") String engineModel,
        @Param("vin") String vin
    );
    
    /**
     * 获取柴油车型VIN码的数据缺失率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型（可选）
     * @param engineManufacturerName 发动机生产厂家（可选）
     * @param engineModel 发动机型号（可选）
     * @param vin 车辆VIN码（可选）
     * @return 柴油车型VIN码的数据缺失率指标数据列表
     */
    List<DieselVinLossrateList> getDieselVinLossrateList(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("epaVehicleType") String epaVehicleType,
        @Param("actualAgreement") String actualAgreement,
        @Param("engineManufacturerName") String engineManufacturerName,
        @Param("engineModel") String engineModel,
        @Param("vin") String vin
    );
    
    /**
     * 获取燃气车型VIN码的数据缺失率指标数据
     * 
     * @param ptMonth 月份
     * @param manufacturerBuildName 企业名称
     * @param epaVehicleType 燃料类型
     * @param actualAgreement 协议类型（可选）
     * @param engineManufacturerName 发动机生产厂家（可选）
     * @param engineModel 发动机型号（可选）
     * @param vin 车辆VIN码（可选）
     * @return 燃气车型VIN码的数据缺失率指标数据列表
     */
    List<GasVinLossrateList> getGasVinLossrateList(
        @Param("ptMonth") Date ptMonth,
        @Param("manufacturerBuildName") String manufacturerBuildName,
        @Param("epaVehicleType") String epaVehicleType,
        @Param("actualAgreement") String actualAgreement,
        @Param("engineManufacturerName") String engineManufacturerName,
        @Param("engineModel") String engineModel,
        @Param("vin") String vin
    );
} 