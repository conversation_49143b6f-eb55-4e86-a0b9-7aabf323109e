package com.portal.obd_dataquality_score.domain;

import com.portal.common.annotation.Excel;

/**
 * 燃气车型VIN码的数据缺失率指标数据
 * 
 * <AUTHOR>
 */
public class GasVinLossrateList {
    @Excel(name = "车辆VIN码")
    private String vin;                       // 车辆VIN码
    @Excel(name = "企业名称")
    private String manufacturerBuildName;     // 企业名称
    @Excel(name = "发动机型号")
    private String engineModel;               // 发动机型号
    @Excel(name = "发动机生产厂家")
    private String engineManufacturerName;    // 发动机生产厂家
    @Excel(name = "燃料类型")
    private String epaVehicleType;            // 燃料类型
    @Excel(name = "协议类型")
    private String actualAgreement;           // 协议类型
    @Excel(name = "三元催化剂入口氧传感器缺失率", suffix = "%")
    private Double threeCatalystO3InRate;     // 三元催化剂入口氧传感器缺失率
    @Excel(name = "三元催化剂出口氧传感器缺失率", suffix = "%")
    private Double threeCatalystO3OutRate;    // 三元催化剂出口氧传感器缺失率
    @Excel(name = "经度缺失率", suffix = "%")
    private Double lonRate;                   // 经度缺失率
    @Excel(name = "纬度缺失率", suffix = "%")
    private Double latRate;                   // 纬度缺失率
    @Excel(name = "诊断协议缺失率", suffix = "%")
    private Double diagnosticProtocolRate;    // 诊断协议缺失率
    @Excel(name = "MIL状态缺失率", suffix = "%")
    private Double milStateRate;              // MIL状态缺失率
    @Excel(name = "故障码数量缺失率", suffix = "%")
    private Double defectCodeCntRate;         // 故障码数量缺失率
    @Excel(name = "IUPR缺失率", suffix = "%")
    private Double iuprRate;                  // IUPR缺失率
    
    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel;
    }

    public String getEngineManufacturerName() {
        return engineManufacturerName;
    }

    public void setEngineManufacturerName(String engineManufacturerName) {
        this.engineManufacturerName = engineManufacturerName;
    }

    public String getEpaVehicleType() {
        return epaVehicleType;
    }

    public void setEpaVehicleType(String epaVehicleType) {
        this.epaVehicleType = epaVehicleType;
    }

    public String getActualAgreement() {
        return actualAgreement;
    }

    public void setActualAgreement(String actualAgreement) {
        this.actualAgreement = actualAgreement;
    }

    public Double getThreeCatalystO3InRate() {
        return threeCatalystO3InRate;
    }

    public void setThreeCatalystO3InRate(Double threeCatalystO3InRate) {
        this.threeCatalystO3InRate = threeCatalystO3InRate;
    }

    public Double getThreeCatalystO3OutRate() {
        return threeCatalystO3OutRate;
    }

    public void setThreeCatalystO3OutRate(Double threeCatalystO3OutRate) {
        this.threeCatalystO3OutRate = threeCatalystO3OutRate;
    }

    public Double getLonRate() {
        return lonRate;
    }

    public void setLonRate(Double lonRate) {
        this.lonRate = lonRate;
    }

    public Double getLatRate() {
        return latRate;
    }

    public void setLatRate(Double latRate) {
        this.latRate = latRate;
    }

    public Double getDiagnosticProtocolRate() {
        return diagnosticProtocolRate;
    }

    public void setDiagnosticProtocolRate(Double diagnosticProtocolRate) {
        this.diagnosticProtocolRate = diagnosticProtocolRate;
    }

    public Double getMilStateRate() {
        return milStateRate;
    }

    public void setMilStateRate(Double milStateRate) {
        this.milStateRate = milStateRate;
    }

    public Double getDefectCodeCntRate() {
        return defectCodeCntRate;
    }

    public void setDefectCodeCntRate(Double defectCodeCntRate) {
        this.defectCodeCntRate = defectCodeCntRate;
    }

    public Double getIuprRate() {
        return iuprRate;
    }

    public void setIuprRate(Double iuprRate) {
        this.iuprRate = iuprRate;
    }

    @Override
    public String toString() {
        return "GasVinLossrateList{" +
                "vin='" + vin + '\'' +
                ", manufacturerBuildName='" + manufacturerBuildName + '\'' +
                ", engineModel='" + engineModel + '\'' +
                ", engineManufacturerName='" + engineManufacturerName + '\'' +
                ", epaVehicleType='" + epaVehicleType + '\'' +
                ", actualAgreement='" + actualAgreement + '\'' +
                ", threeCatalystO3InRate=" + threeCatalystO3InRate +
                ", threeCatalystO3OutRate=" + threeCatalystO3OutRate +
                ", lonRate=" + lonRate +
                ", latRate=" + latRate +
                ", diagnosticProtocolRate=" + diagnosticProtocolRate +
                ", milStateRate=" + milStateRate +
                ", defectCodeCntRate=" + defectCodeCntRate +
                ", iuprRate=" + iuprRate +
                '}';
    }
} 