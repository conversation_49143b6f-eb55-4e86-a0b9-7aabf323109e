package com.portal.obd_dataquality_score.domain;

/**
 * 数据缺失率指标数据
 * 
 * <AUTHOR>
 */
public class DataLossrate {
    private String type;                      // 指标类型
    private String manufacturerBuildName;     // 企业名称
    private String epaVehicleType;            // 燃料类型
    private String actualAgreement;           // 协议类型
    private Double avgLossRate;               // 平均缺失率
    private Double score;                     // 得分
    
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getEpaVehicleType() {
        return epaVehicleType;
    }

    public void setEpaVehicleType(String epaVehicleType) {
        this.epaVehicleType = epaVehicleType;
    }

    public String getActualAgreement() {
        return actualAgreement;
    }

    public void setActualAgreement(String actualAgreement) {
        this.actualAgreement = actualAgreement;
    }

    public Double getAvgLossRate() {
        return avgLossRate;
    }

    public void setAvgLossRate(Double avgLossRate) {
        this.avgLossRate = avgLossRate;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    @Override
    public String toString() {
        return "DataLossrate{" +
                "type='" + type + '\'' +
                ", manufacturerBuildName='" + manufacturerBuildName + '\'' +
                ", epaVehicleType='" + epaVehicleType + '\'' +
                ", actualAgreement='" + actualAgreement + '\'' +
                ", avgLossRate=" + avgLossRate +
                ", score=" + score +
                '}';
    }
} 