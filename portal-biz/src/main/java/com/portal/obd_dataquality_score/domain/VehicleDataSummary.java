package com.portal.obd_dataquality_score.domain;

import java.util.Date;

/**
 * 车辆数据质量摘要
 * 
 * <AUTHOR>
 */
public class VehicleDataSummary {
    private Date ptMonth;                    // 月份
    private String manufacturerBuildName;    // 企业名称
    private String epaVehicleType;           // 车辆类型
    private Long onlineCnt;                  // 上线车辆数
    private Long connectedCnt;               // 联网车辆数
    private Double offlineCnt;               // 长期离线车辆数
    
    public Date getPtMonth() {
        return ptMonth;
    }

    public void setPtMonth(Date ptMonth) {
        this.ptMonth = ptMonth;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }
    
    public String getEpaVehicleType() {
        return epaVehicleType;
    }

    public void setEpaVehicleType(String epaVehicleType) {
        this.epaVehicleType = epaVehicleType;
    }

    public Long getOnlineCnt() {
        return onlineCnt;
    }

    public void setOnlineCnt(Long onlineCnt) {
        this.onlineCnt = onlineCnt;
    }

    public Long getConnectedCnt() {
        return connectedCnt;
    }

    public void setConnectedCnt(Long connectedCnt) {
        this.connectedCnt = connectedCnt;
    }

    public Double getOfflineCnt() {
        return offlineCnt;
    }

    public void setOfflineCnt(Double offlineCnt) {
        this.offlineCnt = offlineCnt;
    }

    @Override
    public String toString() {
        return "VehicleDataSummary{" +
                "ptMonth=" + ptMonth +
                ", manufacturerBuildName='" + manufacturerBuildName + '\'' +
                ", epaVehicleType='" + epaVehicleType + '\'' +
                ", onlineCnt=" + onlineCnt +
                ", connectedCnt=" + connectedCnt +
                ", offlineCnt=" + offlineCnt +
                '}';
    }
} 