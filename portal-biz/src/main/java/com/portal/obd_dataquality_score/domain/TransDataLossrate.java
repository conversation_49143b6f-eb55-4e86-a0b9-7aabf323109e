package com.portal.obd_dataquality_score.domain;

/**
 * 传输数据缺失率指标数据
 * 
 * <AUTHOR>
 */
public class TransDataLossrate {
    private String type;                      // 指标类型
    private String manufacturerBuildName;     // 企业名称
    private String epaVehicleType;                     // 燃料类型
    private String acturalAgreement;                 // 协议类型
    private Double obdLossrateScore;          // OBD缺失率得分
    private Double obdLossrateRate;           // OBD缺失率
    
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getCtype() {
        return epaVehicleType;
    }

    public void setCtype(String epaVehicleType) {
        this.epaVehicleType = epaVehicleType;
    }

    public String getAgreement() {
        return acturalAgreement;
    }

    public void setAgreement(String acturalAgreement) {
        this.acturalAgreement = acturalAgreement;
    }

    public Double getObdLossrateScore() {
        return obdLossrateScore;
    }

    public void setObdLossrateScore(Double obdLossrateScore) {
        this.obdLossrateScore = obdLossrateScore;
    }

    public Double getObdLossrateRate() {
        return obdLossrateRate;
    }

    public void setObdLossrateRate(Double obdLossrateRate) {
        this.obdLossrateRate = obdLossrateRate;
    }

    @Override
    public String toString() {
        return "TransDataLossrate{" +
                "type='" + type + '\'' +
                ", manufacturerBuildName='" + manufacturerBuildName + '\'' +
                ", epaVehicleType='" + epaVehicleType + '\'' +
                ", acturalAgreement='" + acturalAgreement + '\'' +
                ", obdLossrateScore=" + obdLossrateScore +
                ", obdLossrateRate=" + obdLossrateRate +
                '}';
    }
} 