package com.portal.obd_dataquality_score.domain;

/**
 * 数据越界率指标数据
 * 
 * <AUTHOR>
 */
public class DataOutboundaryRate {
    private String type;                      // 指标类型
    private String manufacturerBuildName;     // 企业名称
    private String epaVehicleType;            // 燃料类型
    private String actualAgreement;           // 协议类型
    private Double avgCrossBorderRate;        // 平均越界率/无效率
    private Double score;                     // 得分
    
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getEpaVehicleType() {
        return epaVehicleType;
    }

    public void setEpaVehicleType(String epaVehicleType) {
        this.epaVehicleType = epaVehicleType;
    }

    public String getActualAgreement() {
        return actualAgreement;
    }

    public void setActualAgreement(String actualAgreement) {
        this.actualAgreement = actualAgreement;
    }

    public Double getAvgCrossBorderRate() {
        return avgCrossBorderRate;
    }

    public void setAvgCrossBorderRate(Double avgCrossBorderRate) {
        this.avgCrossBorderRate = avgCrossBorderRate;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    @Override
    public String toString() {
        return "DataOutboundaryRate{" +
                "type='" + type + '\'' +
                ", manufacturerBuildName='" + manufacturerBuildName + '\'' +
                ", epaVehicleType='" + epaVehicleType + '\'' +
                ", actualAgreement='" + actualAgreement + '\'' +
                ", avgCrossBorderRate=" + avgCrossBorderRate +
                ", score=" + score +
                '}';
    }
} 