package com.portal.obd_dataquality_score.domain;

/**
 * 数据异常率指标数据
 * 
 * <AUTHOR>
 */
public class DataExecption {
    private String manufacturerBuildName;     // 企业名称
    private String epaVehicleType;                     // 燃料类型
    private String acturalAgreement;                 // 协议类型
    private Double obdExceptionRate;          // OBD异常率
    private Double obdExceptionScore;         // OBD异常得分
    private Double motorExceptionRate1;       // 发动机异常率1
    private Double motorExceptionScore1;      // 发动机异常得分1
    private Double motorExceptionRate2;       // 发动机异常率2
    private Double motorExceptionScore2;      // 发动机异常得分2
    private Double logExceptionRate1;         // 日志异常率1
    private Double logExceptionScore1;        // 日志异常得分1
    private Double logExceptionRate2;         // 日志异常率2
    private Double logExceptionScore2;        // 日志异常得分2
    
    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getCtype() {
        return epaVehicleType;
    }

    public void setCtype(String epaVehicleType) {
        this.epaVehicleType = epaVehicleType;
    }

    public String getAgreement() {
        return acturalAgreement;
    }

    public void setAgreement(String acturalAgreement) {
        this.acturalAgreement = acturalAgreement;
    }

    public Double getObdExceptionRate() {
        return obdExceptionRate;
    }

    public void setObdExceptionRate(Double obdExceptionRate) {
        this.obdExceptionRate = obdExceptionRate;
    }

    public Double getObdExceptionScore() {
        return obdExceptionScore;
    }

    public void setObdExceptionScore(Double obdExceptionScore) {
        this.obdExceptionScore = obdExceptionScore;
    }

    public Double getMotorExceptionRate1() {
        return motorExceptionRate1;
    }

    public void setMotorExceptionRate1(Double motorExceptionRate1) {
        this.motorExceptionRate1 = motorExceptionRate1;
    }

    public Double getMotorExceptionScore1() {
        return motorExceptionScore1;
    }

    public void setMotorExceptionScore1(Double motorExceptionScore1) {
        this.motorExceptionScore1 = motorExceptionScore1;
    }

    public Double getMotorExceptionRate2() {
        return motorExceptionRate2;
    }

    public void setMotorExceptionRate2(Double motorExceptionRate2) {
        this.motorExceptionRate2 = motorExceptionRate2;
    }

    public Double getMotorExceptionScore2() {
        return motorExceptionScore2;
    }

    public void setMotorExceptionScore2(Double motorExceptionScore2) {
        this.motorExceptionScore2 = motorExceptionScore2;
    }

    public Double getLogExceptionRate1() {
        return logExceptionRate1;
    }

    public void setLogExceptionRate1(Double logExceptionRate1) {
        this.logExceptionRate1 = logExceptionRate1;
    }

    public Double getLogExceptionScore1() {
        return logExceptionScore1;
    }

    public void setLogExceptionScore1(Double logExceptionScore1) {
        this.logExceptionScore1 = logExceptionScore1;
    }

    public Double getLogExceptionRate2() {
        return logExceptionRate2;
    }

    public void setLogExceptionRate2(Double logExceptionRate2) {
        this.logExceptionRate2 = logExceptionRate2;
    }

    public Double getLogExceptionScore2() {
        return logExceptionScore2;
    }

    public void setLogExceptionScore2(Double logExceptionScore2) {
        this.logExceptionScore2 = logExceptionScore2;
    }

    @Override
    public String toString() {
        return "DataExecption{" +
                "manufacturerBuildName='" + manufacturerBuildName + '\'' +
                ", epaVehicleType='" + epaVehicleType + '\'' +
                ", acturalAgreement='" + acturalAgreement + '\'' +
                ", obdExceptionRate=" + obdExceptionRate +
                ", obdExceptionScore=" + obdExceptionScore +
                ", motorExceptionRate1=" + motorExceptionRate1 +
                ", motorExceptionScore1=" + motorExceptionScore1 +
                ", motorExceptionRate2=" + motorExceptionRate2 +
                ", motorExceptionScore2=" + motorExceptionScore2 +
                ", logExceptionRate1=" + logExceptionRate1 +
                ", logExceptionScore1=" + logExceptionScore1 +
                ", logExceptionRate2=" + logExceptionRate2 +
                ", logExceptionScore2=" + logExceptionScore2 +
                '}';
    }
} 