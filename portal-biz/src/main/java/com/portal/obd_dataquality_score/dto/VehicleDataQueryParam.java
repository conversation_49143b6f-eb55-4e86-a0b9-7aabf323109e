package com.portal.obd_dataquality_score.dto;

import java.util.Date;

/**
 * 车辆数据查询参数
 * 
 * <AUTHOR>
 */
public class VehicleDataQueryParam {
    private Date ptMonth;                    // 查询月份，必填
    private String manufacturerBuildName;    // 企业名称，必填
    private String epaVehicleType;           // 燃料类型，选填
    private String actualAgreement;          // 协议类型，选填
    private String engineManufacturerName;   // 发动机生产厂家，选填
    private String engineModel;              // 发动机型号，选填
    private String vin;                      // 车辆VIN码，选填
    private Boolean exportCurrentPage;       // 是否导出当前页，选填
    private Integer pageNum;                 // 当前页码
    private Integer pageSize;                // 每页记录数
    private String orderByColumn;            // 排序列
    private String isAsc;                    // 排序方向（desc或asc）
    
    public Date getPtMonth() {
        return ptMonth;
    }

    public void setPtMonth(Date ptMonth) {
        this.ptMonth = ptMonth;
    }

    public String getManufacturerBuildName() {
        return manufacturerBuildName;
    }

    public void setManufacturerBuildName(String manufacturerBuildName) {
        this.manufacturerBuildName = manufacturerBuildName;
    }

    public String getEpaVehicleType() {
        return epaVehicleType;
    }

    public void setEpaVehicleType(String epaVehicleType) {
        this.epaVehicleType = epaVehicleType;
    }

    public String getActualAgreement() {
        return actualAgreement;
    }

    public void setActualAgreement(String actualAgreement) {
        this.actualAgreement = actualAgreement;
    }

    public String getEngineManufacturerName() {
        return engineManufacturerName;
    }

    public void setEngineManufacturerName(String engineManufacturerName) {
        this.engineManufacturerName = engineManufacturerName;
    }

    public String getEngineModel() {
        return engineModel;
    }

    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel;
    }
    
    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }
    
    public Boolean getExportCurrentPage() {
        return exportCurrentPage;
    }

    public void setExportCurrentPage(Boolean exportCurrentPage) {
        this.exportCurrentPage = exportCurrentPage;
    }
    
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    
    public String getOrderByColumn() {
        return orderByColumn;
    }

    public void setOrderByColumn(String orderByColumn) {
        this.orderByColumn = orderByColumn;
    }

    public String getIsAsc() {
        return isAsc;
    }

    public void setIsAsc(String isAsc) {
        this.isAsc = isAsc;
    }

    @Override
    public String toString() {
        return "VehicleDataQueryParam{" +
                "ptMonth=" + ptMonth +
                ", manufacturerBuildName='" + manufacturerBuildName + '\'' +
                ", epaVehicleType='" + epaVehicleType + '\'' +
                ", actualAgreement='" + actualAgreement + '\'' +
                ", engineManufacturerName='" + engineManufacturerName + '\'' +
                ", engineModel='" + engineModel + '\'' +
                ", vin='" + vin + '\'' +
                ", exportCurrentPage=" + exportCurrentPage +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", orderByColumn='" + orderByColumn + '\'' +
                ", isAsc='" + isAsc + '\'' +
                '}';
    }
} 