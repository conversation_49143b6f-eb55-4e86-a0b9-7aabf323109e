package com.portal.shiyunhui_daily_report.util;

import org.junit.Test;
import static org.junit.Assert.*;
import com.portal.common.utils.GeometryCompressionUtil;

/**
 * GeometryCompressionUtil 测试类
 *
 * <AUTHOR>
 */
public class GeometryCompressionUtilTest {

    @Test
    public void testCompressSmallData() {
        // 测试小数据不压缩
        String smallData = "small geometry data";
        String result = GeometryCompressionUtil.compress(smallData);
        assertEquals("小数据应该不压缩", smallData, result);
        assertFalse("小数据不应该被标记为已压缩", GeometryCompressionUtil.isCompressed(result));
    }

    @Test
    public void testCompressLargeData() {
        // 创建大数据进行压缩测试
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 5000; i++) {
            sb.append("POLYGON((116.3974 39.9093,116.3975 39.9094,116.3976 39.9095,116.3977 39.9096,116.3974 39.9093)),");
        }
        String largeData = sb.toString();
        
        String compressed = GeometryCompressionUtil.compress(largeData);
        
        // 验证压缩效果
        assertTrue("大数据应该被压缩", compressed.length() < largeData.length());
        assertTrue("压缩后的数据应该被标记为已压缩", GeometryCompressionUtil.isCompressed(compressed));
        
        // 验证解压缩
        String decompressed = GeometryCompressionUtil.decompress(compressed);
        assertEquals("解压缩后应该恢复原数据", largeData, decompressed);
        
        // 输出压缩率
        double compressionRatio = (double) compressed.length() / largeData.length();
        System.out.println("原始数据大小: " + largeData.length() + " 字符");
        System.out.println("压缩后大小: " + compressed.length() + " 字符");
        System.out.println("压缩率: " + String.format("%.2f%%", compressionRatio * 100));
    }

    @Test
    public void testDecompressUncompressedData() {
        // 测试解压缩未压缩的数据
        String uncompressedData = "uncompressed geometry data";
        String result = GeometryCompressionUtil.decompress(uncompressedData);
        assertEquals("未压缩的数据应该原样返回", uncompressedData, result);
    }

    @Test
    public void testCompressDecompressCycle() {
        // 测试压缩-解压缩循环
        String originalData = generateLargeGeometryData();
        
        String compressed = GeometryCompressionUtil.compress(originalData);
        String decompressed = GeometryCompressionUtil.decompress(compressed);
        
        assertEquals("压缩-解压缩循环后应该恢复原数据", originalData, decompressed);
    }

    @Test
    public void testNullAndEmptyData() {
        // 测试 null 和空数据
        assertNull("null 数据应该返回 null", GeometryCompressionUtil.compress(null));
        assertNull("null 数据解压缩应该返回 null", GeometryCompressionUtil.decompress(null));
        
        String emptyData = "";
        assertEquals("空字符串应该原样返回", emptyData, GeometryCompressionUtil.compress(emptyData));
        assertEquals("空字符串解压缩应该原样返回", emptyData, GeometryCompressionUtil.decompress(emptyData));
    }

    @Test
    public void testIsCompressed() {
        // 测试压缩标识检查
        assertFalse("null 不应该被认为是压缩数据", GeometryCompressionUtil.isCompressed(null));
        assertFalse("普通字符串不应该被认为是压缩数据", GeometryCompressionUtil.isCompressed("normal string"));
        assertTrue("带压缩前缀的字符串应该被认为是压缩数据", 
                  GeometryCompressionUtil.isCompressed("GZIP_BASE64:somedata"));
    }

    /**
     * 生成大的几何数据用于测试
     */
    private String generateLargeGeometryData() {
        StringBuilder sb = new StringBuilder();
        sb.append("{\"type\":\"Polygon\",\"coordinates\":[[[");
        
        // 生成大量坐标点
        for (int i = 0; i < 2000; i++) {
            double lng = 116.3974 + (i * 0.0001);
            double lat = 39.9093 + (i * 0.0001);
            sb.append("[").append(lng).append(",").append(lat).append("]");
            if (i < 1999) {
                sb.append(",");
            }
        }
        
        sb.append("]]]}");
        return sb.toString();
    }
}
