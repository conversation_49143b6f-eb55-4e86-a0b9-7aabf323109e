<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.portal.dashboard.mapper.DashboardTotalIndexMapper">

    <!-- 获取联网车辆数 -->
    <select id="getConnectedCars" resultType="int">
        SELECT count(DISTINCT vin) as connected_cars
        from dw_ads.ads_sys_vehicle_basis_sync
    </select>

    <!-- 根据传入的日期查询当日上线车辆数及里程 -->
    <select id="getCurDayMetrics" resultType="map">
        SELECT count(DISTINCT vin) as online_cars,
               round(sum(case when COALESCE(cur_day_odometer,0) > 2000 then 0 else COALESCE(cur_day_odometer,0) end),2) as drive_odomoter
        FROM  dw_ads.ads_daily_online_vehicle_distribute
        where pt_date = #{pt_date}
    </select>

    <!-- 根据传入的日期查询省当日上线车辆数top5 -->
    <select id="getProvinceTop" resultType="map">
        SELECT province_name, count(DISTINCT vin) as cnt
        FROM dw_ads.ads_daily_online_vehicle_distribute
        WHERE pt_date = '${pt_date}'
          AND province_name IS NOT NULL
        GROUP BY province_name
        ORDER BY cnt DESC
        LIMIT 5
    </select>

    <!-- 获取边界数据+各省当日上线车辆数 -->
    <select id="getMapData" resultType="map">
        SELECT t1.name,
               COALESCE(t2.online_num,0) as online_num,
               t1.center,
               t1.geometry_coordinates
        from
            (
                select
                    name,
                    center,
                    geometry_coordinates
                from dw_ads.ads_map_boundary_data
            ) t1 left join
            (SELECT province_code ,
                    province_name,
                    count(DISTINCT vin) as online_num
             FROM dw_ads.ads_daily_online_vehicle_distribute
             WHERE pt_date = '${pt_date}'
               AND province_name IS NOT NULL
             GROUP BY province_name,province_code
            ) t2 on t1.name = t2.province_name
    </select>

</mapper>