<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.portal.dashboard.mapper.RoadMapMapper">

    <!-- 获取道路名称 -->
    <select id="getRoadName" resultType="String">
        select distinct name
        from dw_dwm.dwm_osm_road_data_whole_country
        where province = #{province}
    </select>

    <!-- 根据路名获取线段 -->
    <select id="getRoadData" resultType="com.portal.dashboard.domain.RoadData">
        select id,`geometry`
        from dw_dwm.dwm_osm_road_data_whole_country
        where name = #{road_name}
    </select>

</mapper>