package com.portal.dashboard.service.impl;

import com.portal.common.annotation.DataSource;
import com.portal.common.enums.DataSourceType;
import com.portal.dashboard.domain.RoadData;
import com.portal.dashboard.mapper.RoadMapMapper;
import com.portal.dashboard.service.IRoadMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RoadMapImpl implements IRoadMapService {
    @Autowired
    private RoadMapMapper roadMapMapper;

    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<String> getRoadName(@Param("province") String province){
        return  roadMapMapper.getRoadName(province);
    }

    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<RoadData> getRoadData(@Param("road_name") String roadName){
        return  roadMapMapper.getRoadData(roadName);
    }

}
