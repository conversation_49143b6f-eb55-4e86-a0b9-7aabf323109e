package com.portal.dashboard.service;

import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * 首页指标 数据层
 *
 */
public interface IDashboardTotalIndexService {
    // 联网车辆数
    public int getConnectedCars();

    // 当日上线车辆数及里程
    public Map<String, Object> getCurDayMetrics(@Param("pt_date") String ptDate);

    // 当日省上线车辆数top5
    public List<Map<String, Integer>> getProvinceTop(@Param("pt_date") String ptDate);

    // 地图数据+当日各省上线车辆数
    public List<Map<String, String>> getMapData(@Param("pt_date") String ptDate);
}
