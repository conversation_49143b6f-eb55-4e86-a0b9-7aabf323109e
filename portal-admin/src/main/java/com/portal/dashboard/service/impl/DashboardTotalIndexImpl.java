package com.portal.dashboard.service.impl;

import com.portal.common.annotation.DataSource;
import com.portal.common.enums.DataSourceType;
import com.portal.dashboard.mapper.DashboardTotalIndexMapper;
import com.portal.dashboard.service.IDashboardTotalIndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className DashboardTotalIndexImpl
 * @date 2024/9/24 16:44
 * @description TODO 首页指标服务层
 */
@Service
public class DashboardTotalIndexImpl implements IDashboardTotalIndexService {
    @Autowired
    private DashboardTotalIndexMapper totalIndexMapper;

    /**
     * 获取联网车辆数
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int getConnectedCars() {
        return totalIndexMapper.getConnectedCars();
    }

    /**
     * 根据传入的日期查询当日上线车辆数及里程
     * @param ptDate  查询的日期
     * @return 包含当日上线车辆数和总里程的 Map
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public Map<String, Object> getCurDayMetrics(@Param("pt_date") String ptDate) {
        return totalIndexMapper.getCurDayMetrics(ptDate);
    }
    /**
     * 根据传入的日期查询当日省上线车辆数
     * @param ptDate  查询的日期
     * @return 当日省上线车辆数
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<Map<String, Integer>> getProvinceTop(@Param("pt_date") String ptDate) {
        return totalIndexMapper.getProvinceTop(ptDate);
    }

    /**
     * @return 返回地图边界
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<Map<String, String>> getMapData(@Param("pt_date") String ptDate) {
        return totalIndexMapper.getMapData(ptDate);
    }


}
