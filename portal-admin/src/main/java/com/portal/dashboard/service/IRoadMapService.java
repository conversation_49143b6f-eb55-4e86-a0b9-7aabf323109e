package com.portal.dashboard.service;

import com.portal.dashboard.domain.RoadData;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;


public interface IRoadMapService {

    // 返回道路名称
    public List<String> getRoadName(@Param("province") String province);


    // 返回道路数据
    public List<RoadData> getRoadData(@Param("road_name") String roadName);

}
