package com.portal.dashboard.controller;

import com.portal.common.core.domain.AjaxResult;
import com.portal.dashboard.domain.RoadData;
import com.portal.dashboard.service.IDashboardTotalIndexService;
import com.portal.dashboard.service.IRoadMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/roadNet")
public class RoadMapController {

    @Autowired
    private IRoadMapService roadMapService;

    /**
     * 获取道路数据
     *
     * @return RoadData
     */
    @PreAuthorize("@ss.hasPermi('roadNet:road_data')")
    @GetMapping("/road_data")
    public AjaxResult getRoadData(@RequestParam(value = "road_name", required = false)String roadName) {
        try {
            List<RoadData> roadData = roadMapService.getRoadData(roadName);
            return AjaxResult.success("操作成功", roadData);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("获取数据失败");
        }
    }

    /**
     * 获取道路名称
     *
     * @return RoadData
     */
    @PreAuthorize("@ss.hasPermi('roadNet:road_name')")
    @GetMapping("/road_name")
    public AjaxResult getRoadName(@RequestParam(value = "province", required = false)String province) {
        try {
            if (province == null || province.isEmpty()) {
                province = "sichuan";
            }
            List<String> roadName = roadMapService.getRoadName(province);
            return AjaxResult.success("操作成功", roadName);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("获取数据失败");
        }
    }
}