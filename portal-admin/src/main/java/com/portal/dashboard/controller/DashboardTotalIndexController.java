package com.portal.dashboard.controller;

import com.portal.common.core.domain.AjaxResult;
import com.portal.dashboard.service.IDashboardTotalIndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className DashboardTotalIndexController
 * @date 2024/9/28
 * @description 首页指标控制器
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardTotalIndexController {

    @Autowired
    private IDashboardTotalIndexService dashboardTotalIndexService;

    /**
     * 获取联网车辆总数
     *
     * @return 联网车辆总数
     */
    @PreAuthorize("@ss.hasPermi('dashboard:connected_cars')")
    @GetMapping("/connected_cars")
    public AjaxResult getConnectedCars() {
        try {
            int totalCars = dashboardTotalIndexService.getConnectedCars();
            return AjaxResult.success("操作成功", totalCars);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("获取数据失败");
        }
    }

    /**
     * 根据日期获取当日上线车辆数及总里程
     *
     * @param ptDate 查询的日期，格式如 "2024-09-28"
     * @return 当日上线车辆数和总里程
     */
    @PreAuthorize("@ss.hasPermi('dashboard:cur_day_metrics')")
    @GetMapping("/cur_day_metrics")
    public AjaxResult getCurDayMetrics(@RequestParam(value = "pt_date", required = false) String ptDate) {
        try {
            //  默认为t-7
            if (ptDate == null || ptDate.isEmpty()) {
                LocalDate defaultDate = LocalDate.now().minusDays(7);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                ptDate = defaultDate.format(formatter);
            }
            Map<String, Object> metrics = dashboardTotalIndexService.getCurDayMetrics(ptDate);
            return AjaxResult.success("操作成功", metrics);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("获取数据失败");
        }
    }

    /**
     * 根据日期获取当日省上线车辆数top5
     *
     * @param ptDate 查询的日期，格式如 "2024-09-28"
     * @return 省上线车辆数top5
     */
    @PreAuthorize("@ss.hasPermi('dashboard:province_top')")
    @GetMapping("/province_top")
    public AjaxResult getProvinceTop(@RequestParam(value = "pt_date", required = false) String ptDate) {
        try {
            //  默认为t-7
            if (ptDate == null || ptDate.isEmpty()) {
                LocalDate defaultDate = LocalDate.now().minusDays(7);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                ptDate = defaultDate.format(formatter);
            }

            List<Map<String, Integer>> metrics = dashboardTotalIndexService.getProvinceTop(ptDate);
            return AjaxResult.success("操作成功", metrics);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("获取数据失败");
        }
    }

    /**
     * 获取地图边界
     *
     * @return 获取地图边界+各省上线车辆数
     */
    @PreAuthorize("@ss.hasPermi('dashboard:map_data')")
    @GetMapping("/map_data")
    public AjaxResult getMapBoundary(@RequestParam(value = "pt_date", required = false) String ptDate) {
        try {
            //  默认为t-7
            if (ptDate == null || ptDate.isEmpty()) {
                LocalDate defaultDate = LocalDate.now().minusDays(7);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                ptDate = defaultDate.format(formatter);
            }

            List<Map<String, String>> mapData = dashboardTotalIndexService.getMapData(ptDate);
            return AjaxResult.success("操作成功", mapData);
        } catch (Exception e) {
            return AjaxResult.error("获取数据失败");
        }
    }

}