package com.portal.dashboard.mapper;

import com.portal.dashboard.domain.RoadData;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * 首页指标 服务层
 *
 */
public interface RoadMapMapper {

    // 返回道路名称
    public List<String> getRoadName(@Param("province") String province);

    // 返回道路数据
    public List<RoadData> getRoadData(@Param("road_name") String road_name);

}
