package com.portal.dashboard.domain;

/**
 * <AUTHOR>
 * @className DashboardTotalIndex
 * @date 2024/9/24 16:29
 * @description TODO 首页的三个总数量指标
 */
public class DashboardTotalIndex {
    /** 联网车辆数 */
    private int connectedCars;
    /** 在线车辆数 */
    private int onlineCars;
    /** 行驶里程 */
    private double driveOdomoter;

    // Getters and Setters
    public int getTotalCars() {
        return connectedCars;
    }

    public void setTotalCars(int connectedCars) {
        this.connectedCars = connectedCars;
    }

    public int getCurDayOnlineCars() {
        return onlineCars;
    }

    public void setCurDayOnlineCars(int onlineCars) {
        this.onlineCars = onlineCars;
    }

    public double getCurDayTotalOdometer() {
        return driveOdomoter;
    }

    public void setCurDayTotalOdometer(double driveOdomoter) {
        this.driveOdomoter = driveOdomoter;
    }

    @Override
    public String toString() {
        return "DashboardTotalIndex{" +
                "connectedCars=" + connectedCars +
                ", onlineCars=" + onlineCars +
                ", driveOdomoter=" + driveOdomoter +
                '}';
    }
}
