package com.portal.web.config;

import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;

/**
 * HTTP压缩配置
 * 
 * <AUTHOR>
 */
@Configuration
public class CompressionConfig {

    /**
     * 自定义Tomcat压缩配置
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> compressionCustomizer() {
        return factory -> {
            factory.addConnectorCustomizers(connector -> {
                connector.setProperty("compression", "on");
                // 压缩级别：1-9，6为平衡点
                connector.setProperty("compressionLevel", "6");
                // 最小压缩大小（字节）
                connector.setProperty("compressionMinSize", "512");
                // 可压缩的MIME类型
                connector.setProperty("compressibleMimeType", 
                    String.join(",", 
                        MediaType.APPLICATION_JSON_VALUE,
                        MediaType.APPLICATION_XML_VALUE,
                        MediaType.TEXT_HTML_VALUE,
                        MediaType.TEXT_XML_VALUE,
                        MediaType.TEXT_PLAIN_VALUE,
                        "text/css",
                        "text/javascript",
                        "application/javascript",
                        "application/x-javascript",
                        "application/octet-stream"
                    )
                );
                // 不压缩的用户代理
                connector.setProperty("noCompressionUserAgents", 
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)");
            });
        };
    }
}
